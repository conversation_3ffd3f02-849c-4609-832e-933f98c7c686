{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Ai.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Ai.vue", "mtime": 1741618492749}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["chatWithAI", "name", "data", "messages", "userInput", "isLoading", "chatHistory", "suggestedQuestions", "mounted", "lname", "sessionStorage", "getItem", "$message", "message", "type", "offset", "$router", "push", "methods", "handleSend", "trim", "userMessage", "addMessage", "role", "content", "response", "error", "console", "scrollToBottom", "time", "Date", "$nextTick", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "container", "$refs", "messageContainer", "scrollTop", "scrollHeight", "formatMessage", "replace", "quickAsk", "question"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Ai.vue"], "sourcesContent": ["<template>\r\n    <div class=\"ai-advisor\">\r\n        <!-- 聊天容器 -->\r\n        <div class=\"chat-container\">\r\n            <!-- 聊天头部 -->\r\n            <div class=\"chat-header\">\r\n                <div class=\"advisor-info\">\r\n                    <i class=\"fas fa-robot advisor-avatar\"></i>\r\n                    <div class=\"advisor-details\">\r\n                        <h2>AI求职顾问</h2>\r\n                        <span class=\"status online\">\r\n                            <i class=\"fas fa-circle\"></i> 在线\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 聊天消息区域 -->\r\n            <div class=\"chat-messages\" ref=\"messageContainer\">\r\n                <!-- 欢迎消息 -->\r\n                <div class=\"message-item ai-message\">\r\n                    <div class=\"message-avatar\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\">\r\n                            <p>👋 你好！我是你的AI求职顾问。</p>\r\n                            <p>我可以为你提供以下帮助：</p>\r\n                            <ul>\r\n                                <li>📝 简历优化和求职信写作指导</li>\r\n                                <li>🎯 职业规划和发展建议</li>\r\n                                <li>🤝 面试技巧和模拟面试</li>\r\n                                <li>💼 行业动态和求职策略</li>\r\n                                <li>❓ 解答求职过程中的各类问题</li>\r\n                            </ul>\r\n                            <p>请告诉我你需要什么帮助？</p>\r\n                        </div>\r\n                        <span class=\"message-time\">{{ formatTime(new Date()) }}</span>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 动态消息列表 -->\r\n                <div v-for=\"(msg, index) in messages\" :key=\"index\"\r\n                    :class=\"['message-item', msg.type === 'user' ? 'user-message' : 'ai-message']\">\r\n                    <div class=\"message-avatar\">\r\n                        <i :class=\"msg.type === 'user' ? 'fas fa-user' : 'fas fa-robot'\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\" v-html=\"formatMessage(msg.content)\"></div>\r\n                        <span class=\"message-time\">{{ formatTime(msg.time) }}</span>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 加载动画 -->\r\n                <div class=\"message-item ai-message\" v-if=\"isLoading\">\r\n                    <div class=\"message-avatar\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"typing-indicator\">\r\n                            <span></span>\r\n                            <span></span>\r\n                            <span></span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 输入区域 -->\r\n            <div class=\"chat-input\">\r\n                <div class=\"input-wrapper\">\r\n                    <el-input v-model=\"userInput\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入你的问题...\"\r\n                        :disabled=\"isLoading\" @keyup.enter.native.exact=\"handleSend\"\r\n                        @keyup.ctrl.enter.native=\"handleSend\">\r\n                    </el-input>\r\n                    <el-button type=\"primary\" :loading=\"isLoading\" :disabled=\"!userInput.trim() || isLoading\"\r\n                        @click=\"handleSend\">\r\n                        <i class=\"fas fa-paper-plane\"></i>\r\n                        发送\r\n                    </el-button>\r\n                </div>\r\n                <!-- 快捷问题建议 -->\r\n                <div class=\"quick-questions\" v-if=\"messages.length <= 1\">\r\n                    <el-tag v-for=\"(question, index) in suggestedQuestions\" :key=\"index\" @click=\"quickAsk(question)\"\r\n                        :disabled=\"isLoading\" effect=\"light\" class=\"question-tag\">\r\n                        <i class=\"fas fa-question-circle\"></i>\r\n                        {{ question }}\r\n                    </el-tag>\r\n                </div>\r\n                <div class=\"input-tips\">\r\n                    <span>按Enter换行，Ctrl + Enter发送</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import { chatWithAI } from \"../../../utils/http\";\r\n\r\n\r\n    export default {\r\n        name: \"Ai\",\r\n        data() {\r\n            return {\r\n                messages: [],\r\n                userInput: \"\",\r\n                isLoading: false,\r\n                chatHistory: [],\r\n                suggestedQuestions: [\r\n                    \"如何写一份优秀的简历？\",\r\n                    \"面试常见问题及回答技巧\",\r\n                    \"职业规划该怎么做？\",\r\n                    \"如何准备一份好的求职信？\",\r\n                    \"面试着装和礼仪要求\",\r\n                    \"如何谈薪资待遇？\"\r\n                ]\r\n            };\r\n        },\r\n        mounted() {\r\n            // 检查登录状态\r\n            const lname = sessionStorage.getItem(\"lname\");\r\n            if (!lname) {\r\n                this.$message({\r\n                    message: \"请先登录\",\r\n                    type: \"warning\",\r\n                    offset: 320,\r\n                });\r\n                this.$router.push(\"/slogin\");\r\n                return;\r\n            }\r\n        },\r\n        methods: {\r\n            async handleSend() {\r\n                if (!this.userInput.trim() || this.isLoading) return;\r\n\r\n                // 添加用户消息\r\n                const userMessage = this.userInput.trim();\r\n                this.addMessage(userMessage, 'user');\r\n                this.userInput = '';\r\n                this.isLoading = true;\r\n\r\n                try {\r\n                    // 准备对话历史\r\n                    this.chatHistory.push({ role: \"user\", content: userMessage });\r\n\r\n                    // 调用AI接口\r\n                    const response = await chatWithAI(this.chatHistory);\r\n\r\n                    // 保存AI回复到历史记录\r\n                    this.chatHistory.push({ role: \"assistant\", content: response });\r\n\r\n                    // 显示AI回复\r\n                    this.addMessage(response, 'ai');\r\n                } catch (error) {\r\n                    console.error('AI回复出错:', error);\r\n                    this.addMessage('抱歉，我遇到了一些问题，请稍后再试。', 'ai');\r\n                } finally {\r\n                    this.isLoading = false;\r\n                    this.scrollToBottom();\r\n                }\r\n            },\r\n\r\n            addMessage(content, type) {\r\n                this.messages.push({\r\n                    content,\r\n                    type,\r\n                    time: new Date()\r\n                });\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n            },\r\n\r\n            formatTime(date) {\r\n                return new Date(date).toLocaleTimeString('zh-CN', {\r\n                    hour: '2-digit',\r\n                    minute: '2-digit'\r\n                });\r\n            },\r\n\r\n            scrollToBottom() {\r\n                this.$nextTick(() => {\r\n                    const container = this.$refs.messageContainer;\r\n                    container.scrollTop = container.scrollHeight;\r\n                });\r\n            },\r\n\r\n            formatMessage(message) {\r\n                // 将换行符转换为<br>标签\r\n                return message.replace(/\\n/g, '<br>');\r\n            },\r\n\r\n            quickAsk(question) {\r\n                if (!this.isLoading) {\r\n                    this.userInput = question;\r\n                    this.handleSend();\r\n                }\r\n            }\r\n        }\r\n    };\r\n</script>\r\n\r\n<style scoped>\r\n    .ai-advisor {\r\n        padding: 20px;\r\n        height: calc(100vh - 180px);\r\n        display: flex;\r\n        justify-content: center;\r\n        background-color: #f5f7fa;\r\n    }\r\n\r\n    .chat-container {\r\n        width: 100%;\r\n        max-width: 900px;\r\n        background: #fff;\r\n        border-radius: 16px;\r\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: hidden;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 20px;\r\n        background: linear-gradient(135deg, #3498db, #2c3e50);\r\n        color: white;\r\n    }\r\n\r\n    .advisor-info {\r\n        display: flex;\r\n        align-items: center;\r\n    }\r\n\r\n    .advisor-avatar {\r\n        font-size: 32px;\r\n        margin-right: 15px;\r\n        color: #fff;\r\n    }\r\n\r\n    .advisor-details h2 {\r\n        margin: 0;\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n    }\r\n\r\n    .status {\r\n        font-size: 14px;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 5px;\r\n    }\r\n\r\n    .status.online i {\r\n        color: #2ecc71;\r\n        font-size: 10px;\r\n    }\r\n\r\n    .chat-messages {\r\n        flex: 1;\r\n        padding: 20px;\r\n        overflow-y: auto;\r\n        background: #f8f9fa;\r\n    }\r\n\r\n    .message-item {\r\n        display: flex;\r\n        margin-bottom: 20px;\r\n        align-items: flex-start;\r\n        animation: fadeIn 0.3s ease;\r\n    }\r\n\r\n    .message-avatar {\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        background: #e9ecef;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-right: 12px;\r\n        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    .ai-message .message-avatar {\r\n        background: linear-gradient(135deg, #3498db, #2980b9);\r\n        color: white;\r\n    }\r\n\r\n    .user-message {\r\n        flex-direction: row-reverse;\r\n    }\r\n\r\n    .user-message .message-avatar {\r\n        margin-right: 0;\r\n        margin-left: 12px;\r\n        background: linear-gradient(135deg, #2ecc71, #27ae60);\r\n        color: white;\r\n    }\r\n\r\n    .message-content {\r\n        max-width: 70%;\r\n    }\r\n\r\n    .user-message .message-content {\r\n        align-items: flex-end;\r\n    }\r\n\r\n    .message-text {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        background: white;\r\n        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\r\n        font-size: 15px;\r\n        line-height: 1.6;\r\n        white-space: pre-wrap;\r\n    }\r\n\r\n    .message-text :deep(br) {\r\n        margin-bottom: 8px;\r\n    }\r\n\r\n    .user-message .message-text {\r\n        background: #3498db;\r\n        color: white;\r\n    }\r\n\r\n    .message-time {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-top: 5px;\r\n        display: block;\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 20px;\r\n        background: white;\r\n        border-top: 1px solid #eaeaea;\r\n    }\r\n\r\n    .input-wrapper {\r\n        display: flex;\r\n        gap: 10px;\r\n    }\r\n\r\n    .input-wrapper .el-button {\r\n        height: auto;\r\n        padding: 12px 24px;\r\n    }\r\n\r\n    .input-wrapper .el-button i {\r\n        margin-right: 8px;\r\n    }\r\n\r\n    .quick-questions {\r\n        margin-top: 15px;\r\n        display: flex;\r\n        gap: 10px;\r\n        flex-wrap: wrap;\r\n    }\r\n\r\n    .question-tag {\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        padding: 8px 12px;\r\n    }\r\n\r\n    .question-tag i {\r\n        margin-right: 6px;\r\n    }\r\n\r\n    .question-tag:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    .input-tips {\r\n        margin-top: 10px;\r\n        font-size: 12px;\r\n        color: #666;\r\n        display: flex;\r\n        justify-content: space-between;\r\n    }\r\n\r\n    /* 修改输入提示文本 */\r\n    .input-tips span:last-child {\r\n        display: none;\r\n    }\r\n\r\n    /* 打字动画 */\r\n    .typing-indicator {\r\n        display: flex;\r\n        padding: 12px 16px;\r\n        background: white;\r\n        border-radius: 12px;\r\n        gap: 4px;\r\n    }\r\n\r\n    .typing-indicator span {\r\n        width: 8px;\r\n        height: 8px;\r\n        background: #3498db;\r\n        border-radius: 50%;\r\n        animation: typing 1s infinite ease-in-out;\r\n    }\r\n\r\n    .typing-indicator span:nth-child(1) {\r\n        animation-delay: 0.2s;\r\n    }\r\n\r\n    .typing-indicator span:nth-child(2) {\r\n        animation-delay: 0.3s;\r\n    }\r\n\r\n    .typing-indicator span:nth-child(3) {\r\n        animation-delay: 0.4s;\r\n    }\r\n\r\n    @keyframes typing {\r\n\r\n        0%,\r\n        100% {\r\n            transform: translateY(0);\r\n        }\r\n\r\n        50% {\r\n            transform: translateY(-10px);\r\n        }\r\n    }\r\n\r\n    @keyframes fadeIn {\r\n        from {\r\n            opacity: 0;\r\n            transform: translateY(10px);\r\n        }\r\n\r\n        to {\r\n            opacity: 1;\r\n            transform: translateY(0);\r\n        }\r\n    }\r\n\r\n    /* 消息内容样式 */\r\n    .message-text :deep(p) {\r\n        margin: 0 0 10px;\r\n    }\r\n\r\n    .message-text :deep(ul) {\r\n        margin: 10px 0;\r\n        padding-left: 20px;\r\n    }\r\n\r\n    .message-text :deep(li) {\r\n        margin: 5px 0;\r\n    }\r\n\r\n    .message-text :deep(code) {\r\n        background: rgba(0, 0, 0, 0.05);\r\n        padding: 2px 4px;\r\n        border-radius: 4px;\r\n        font-family: monospace;\r\n    }\r\n\r\n    .user-message .message-text :deep(code) {\r\n        background: rgba(255, 255, 255, 0.2);\r\n    }\r\n\r\n    /* 滚动条样式 */\r\n    .chat-messages::-webkit-scrollbar {\r\n        width: 6px;\r\n    }\r\n\r\n    .chat-messages::-webkit-scrollbar-track {\r\n        background: #f1f1f1;\r\n        border-radius: 3px;\r\n    }\r\n\r\n    .chat-messages::-webkit-scrollbar-thumb {\r\n        background: #888;\r\n        border-radius: 3px;\r\n    }\r\n\r\n    .chat-messages::-webkit-scrollbar-thumb:hover {\r\n        background: #555;\r\n    }\r\n\r\n    /* 响应式设计 */\r\n    @media (max-width: 768px) {\r\n        .ai-advisor {\r\n            padding: 10px;\r\n            height: calc(100vh - 140px);\r\n        }\r\n\r\n        .message-content {\r\n            max-width: 85%;\r\n        }\r\n\r\n        .quick-questions {\r\n            display: grid;\r\n            grid-template-columns: repeat(2, 1fr);\r\n            gap: 8px;\r\n        }\r\n    }\r\n</style>"], "mappings": ";AAkGI,SAASA,UAAS,QAAS,qBAAqB;AAGhD,eAAe;EACXC,IAAI,EAAE,IAAI;EACVC,IAAIA,CAAA,EAAG;IACH,OAAO;MACHC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,EAAE;MACfC,kBAAkB,EAAE,CAChB,aAAa,EACb,aAAa,EACb,WAAW,EACX,cAAc,EACd,WAAW,EACX,UAAS;IAEjB,CAAC;EACL,CAAC;EACDC,OAAOA,CAAA,EAAG;IACN;IACA,MAAMC,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;IAC7C,IAAI,CAACF,KAAK,EAAE;MACR,IAAI,CAACG,QAAQ,CAAC;QACVC,OAAO,EAAE,MAAM;QACfC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;MAC5B;IACJ;EACJ,CAAC;EACDC,OAAO,EAAE;IACL,MAAMC,UAAUA,CAAA,EAAG;MACf,IAAI,CAAC,IAAI,CAACf,SAAS,CAACgB,IAAI,CAAC,KAAK,IAAI,CAACf,SAAS,EAAE;;MAE9C;MACA,MAAMgB,WAAU,GAAI,IAAI,CAACjB,SAAS,CAACgB,IAAI,CAAC,CAAC;MACzC,IAAI,CAACE,UAAU,CAACD,WAAW,EAAE,MAAM,CAAC;MACpC,IAAI,CAACjB,SAAQ,GAAI,EAAE;MACnB,IAAI,CAACC,SAAQ,GAAI,IAAI;MAErB,IAAI;QACA;QACA,IAAI,CAACC,WAAW,CAACW,IAAI,CAAC;UAAEM,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAEH;QAAY,CAAC,CAAC;;QAE7D;QACA,MAAMI,QAAO,GAAI,MAAMzB,UAAU,CAAC,IAAI,CAACM,WAAW,CAAC;;QAEnD;QACA,IAAI,CAACA,WAAW,CAACW,IAAI,CAAC;UAAEM,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAEC;QAAS,CAAC,CAAC;;QAE/D;QACA,IAAI,CAACH,UAAU,CAACG,QAAQ,EAAE,IAAI,CAAC;MACnC,EAAE,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAACJ,UAAU,CAAC,oBAAoB,EAAE,IAAI,CAAC;MAC/C,UAAU;QACN,IAAI,CAACjB,SAAQ,GAAI,KAAK;QACtB,IAAI,CAACuB,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC;IAEDN,UAAUA,CAACE,OAAO,EAAEV,IAAI,EAAE;MACtB,IAAI,CAACX,QAAQ,CAACc,IAAI,CAAC;QACfO,OAAO;QACPV,IAAI;QACJe,IAAI,EAAE,IAAIC,IAAI,CAAC;MACnB,CAAC,CAAC;MACF,IAAI,CAACC,SAAS,CAAC,MAAM;QACjB,IAAI,CAACH,cAAc,CAAC,CAAC;MACzB,CAAC,CAAC;IACN,CAAC;IAEDI,UAAUA,CAACC,IAAI,EAAE;MACb,OAAO,IAAIH,IAAI,CAACG,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;QAC9CC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACZ,CAAC,CAAC;IACN,CAAC;IAEDR,cAAcA,CAAA,EAAG;MACb,IAAI,CAACG,SAAS,CAAC,MAAM;QACjB,MAAMM,SAAQ,GAAI,IAAI,CAACC,KAAK,CAACC,gBAAgB;QAC7CF,SAAS,CAACG,SAAQ,GAAIH,SAAS,CAACI,YAAY;MAChD,CAAC,CAAC;IACN,CAAC;IAEDC,aAAaA,CAAC7B,OAAO,EAAE;MACnB;MACA,OAAOA,OAAO,CAAC8B,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;IACzC,CAAC;IAEDC,QAAQA,CAACC,QAAQ,EAAE;MACf,IAAI,CAAC,IAAI,CAACxC,SAAS,EAAE;QACjB,IAAI,CAACD,SAAQ,GAAIyC,QAAQ;QACzB,IAAI,CAAC1B,UAAU,CAAC,CAAC;MACrB;IACJ;EACJ;AACJ,CAAC", "ignoreList": []}]}