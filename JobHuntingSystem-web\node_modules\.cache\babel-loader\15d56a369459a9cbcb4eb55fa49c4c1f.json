{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sreg.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sreg.vue", "mtime": 1741615898664}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "formData", "professionalsList", "add<PERSON><PERSON>", "sno", "required", "message", "trigger", "password", "password2", "validator", "rule", "value", "callback", "Error", "sname", "gender", "phone", "pattern", "proid", "spic", "btnLoading", "uploadVisible", "created", "getProfessionals", "methods", "url", "post", "params", "currentPage", "pageSize", "then", "res", "code", "resdata", "save", "$refs", "validate", "valid", "$message", "type", "offset", "$router", "push", "msg", "showUpload", "hideUpload", "handleRemove", "file", "fileList", "handlePreview", "console", "log", "handleExceed", "files", "duration", "fileListChange", "extendFileName", "extendFileNames", "split", "regExpRules", "i", "length", "RegExp", "fileNames", "that", "for<PERSON>ach", "key", "val", "ret", "test", "indexOf", "multiFiles", "handleConfirm", "filePath", "FormData", "append", "raw", "furl"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sreg.vue"], "sourcesContent": ["<template>\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n    <el-form-item label=\"账号\" prop=\"sno\">\n      <el-input v-model=\"formData.sno\" placeholder=\"账号\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"密码\" prop=\"password\">\n      <el-input\n        type=\"password\"\n        v-model=\"formData.password\"\n        placeholder=\"密码\"\n        style=\"width: 50%\"\n      ></el-input>\n    </el-form-item>\n    <el-form-item label=\"确认密码\" prop=\"password2\">\n      <el-input\n        type=\"password\"\n        v-model=\"formData.password2\"\n        placeholder=\"确认密码\"\n        style=\"width: 50%\"\n      ></el-input>\n    </el-form-item>\n    <el-form-item label=\"姓名\" prop=\"sname\">\n      <el-input v-model=\"formData.sname\" placeholder=\"姓名\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"性别\" prop=\"gender\">\n      <el-radio-group v-model=\"formData.gender\">\n        <el-radio label=\"男\"> 男 </el-radio>\n        <el-radio label=\"女\"> 女 </el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item label=\"年龄\" prop=\"age\">\n      <el-input v-model=\"formData.age\" placeholder=\"年龄\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"手机号码\" prop=\"phone\">\n      <el-input v-model=\"formData.phone\" placeholder=\"手机号码\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"专业\" prop=\"proid\">\n      <el-select v-model=\"formData.proid\" placeholder=\"请选择\" size=\"small\">\n        <el-option\n          v-for=\"item in professionalsList\"\n          :key=\"item.proid\"\n          :label=\"item.proname\"\n          :value=\"item.proid\"\n        ></el-option>\n      </el-select>\n    </el-form-item>\n    <el-form-item prop=\"spic\" label=\"照片\" min-width=\"20%\">\n      <el-input\n        v-model=\"formData.spic\"\n        placeholder=\"照片\"\n        readonly=\"true\"\n        style=\"width: 50%\"\n      ></el-input>\n      <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n    </el-form-item>\n\n    <el-form-item>\n      <el-button\n        type=\"primary\"\n        size=\"small\"\n        @click=\"save\"\n        :loading=\"btnLoading\"\n        icon=\"el-icon-upload\"\n        >注 册</el-button\n      >\n    </el-form-item>\n  </el-form>\n  <el-dialog\n    v-model=\"uploadVisible\"\n    title=\"附件上传\"\n    custom-class=\"el-dialog-widthSmall\"\n    @close=\"closeDialog\"\n  >\n    <div>\n      <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n    </div>\n    <el-upload\n      action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n      style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\"\n      drag\n      :limit=\"1\"\n      :on-preview=\"handlePreview\"\n      :on-remove=\"handleRemove\"\n      :file-list=\"fileList\"\n      :on-exceed=\"handleExceed\"\n      :auto-upload=\"false\"\n      name=\"file\"\n      :on-change=\"fileListChange\"\n    >\n      <i class=\"el-icon-upload\"></i>\n      <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n      <div class=\"el-upload__tip\">\n        <div\n          style=\"display: inline; color: #d70000; font-size: 14px\"\n          class=\"uploadFileWarning\"\n          id=\"uploadFileWarning\"\n        ></div>\n      </div>\n    </el-upload>\n    <span class=\"dialog-footer\">\n      <el-button @click=\"hideUpload\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n    </span>\n  </el-dialog>\n</template>\n<script>\nimport request, { base } from '../../../utils/http';\nexport default {\n  name: 'Sreg',\n  data() {\n    return {\n      formData: {},\n      professionalsList: [], // 专业列表\n\n      addrules: {\n        sno: [{ required: true, message: '请输入账号', trigger: 'blur' }],\n        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\n        password2: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          {\n            validator: (rule, value, callback) => {\n              if (value !== this.formData.password) {\n                callback(new Error('两次输入密码不一致!'));\n              } else {\n                callback();\n              }\n            },\n            trigger: 'blur',\n          },\n        ],\n        sname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' }],\n        phone: [\n          { required: true, message: '请输入手机号码', trigger: 'blur' },\n          { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\n        ],\n        proid: [{ required: true, message: '请选择专业', trigger: 'onchange' }],\n        spic: [{ required: true, message: '请上传照片', trigger: 'blur' }],\n      },\n\n      btnLoading: false, //按钮是否在加载中\n      uploadVisible: false, //上传弹出框\n    };\n  },\n  created() {\n    this.getProfessionals();\n  },\n  methods: {\n    // 获取专业列表\n    getProfessionals() {\n      let url = base + '/professionals/list';\n      request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\n        if (res.code == 200) {\n          this.professionalsList = res.resdata;\n        }\n      });\n    },\n    //注册\n    save() {\n      //表单验证\n      this.$refs['formDataRef'].validate((valid) => {\n        if (valid) {\n          let url = base + '/students/add'; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          request.post(url, this.formData).then((res) => {\n            //请求接口\n            if (res.code == 200) {\n              this.$message({\n                message: '恭喜您，注册成功！',\n                type: 'success',\n                offset: 320,\n              });\n              this.$router.push('/slogin');\n            } else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: 'error',\n                offset: 320,\n              });\n              this.btnLoading = false;\n            } else {\n              this.$message({\n                message: '服务器错误',\n                type: 'error',\n                offset: 320,\n              });\n              this.btnLoading = false;\n            }\n          });\n        }\n      });\n    },\n    //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: '只能上传一个文件',\n        type: 'error',\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = 'png,jpg';\n      let extendFileNames = extendFileName.split(',');\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key['name']);\n        }\n        if (!ret) {\n          console.log(key['name'] + ':' + ret);\n          that.$message({\n            duration: 1000,\n            message: '上传的文件后缀必须为' + extendFileName + '格式！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key['name']) != -1) {\n          that.$message({\n            duration: 1000,\n            message: '上传的文件重复！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key['name']);\n        if (fileNames !== '') {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: '请选择文件！',\n          type: 'error',\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append('file', file.raw, file.raw.name);\n      });\n      let url = base + '/common/uploadFile';\n      console.log('url=' + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.spic = furl; // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n  },\n};\n</script>\n\n<style></style>\n"], "mappings": ";;;;AA0GA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,CAAC,CAAC;MACZC,iBAAiB,EAAE,EAAE;MAAE;;MAEvBC,QAAQ,EAAE;QACRC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC5DC,QAAQ,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACjEE,SAAS,EAAE,CACT;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,EACrD;UACEG,SAAS,EAAEA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;YACpC,IAAID,KAAI,KAAM,IAAI,CAACX,QAAQ,CAACO,QAAQ,EAAE;cACpCK,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;YACnC,OAAO;cACLD,QAAQ,CAAC,CAAC;YACZ;UACF,CAAC;UACDN,OAAO,EAAE;QACX,CAAC,CACF;QACDQ,KAAK,EAAE,CAAC;UAAEV,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC9DS,MAAM,EAAE,CAAC;UAAEX,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC/DU,KAAK,EAAE,CACL;UAAEZ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACvD;UAAEW,OAAO,EAAE,mBAAmB;UAAEZ,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CACxE;QACDY,KAAK,EAAE,CAAC;UAAEd,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAW,CAAC,CAAC;QAClEa,IAAI,EAAE,CAAC;UAAEf,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC;MAC9D,CAAC;MAEDc,UAAU,EAAE,KAAK;MAAE;MACnBC,aAAa,EAAE,KAAK,CAAE;IACxB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EACDC,OAAO,EAAE;IACP;IACAD,gBAAgBA,CAAA,EAAG;MACjB,IAAIE,GAAE,GAAI5B,IAAG,GAAI,qBAAqB;MACtCD,OAAO,CAAC8B,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,EAAE;QAAEE,MAAM,EAAE;UAAEC,WAAW,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAI;MAAE,CAAC,CAAC,CAACC,IAAI,CAAEC,GAAG,IAAK;QACjF,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAAC/B,iBAAgB,GAAI8B,GAAG,CAACE,OAAO;QACtC;MACF,CAAC,CAAC;IACJ,CAAC;IACD;IACAC,IAAIA,CAAA,EAAG;MACL;MACA,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAC5C,IAAIA,KAAK,EAAE;UACT,IAAIZ,GAAE,GAAI5B,IAAG,GAAI,eAAe,EAAE;UAClC,IAAI,CAACuB,UAAS,GAAI,IAAI,EAAE;UACxBxB,OAAO,CAAC8B,IAAI,CAACD,GAAG,EAAE,IAAI,CAACzB,QAAQ,CAAC,CAAC8B,IAAI,CAAEC,GAAG,IAAK;YAC7C;YACA,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACM,QAAQ,CAAC;gBACZjC,OAAO,EAAE,WAAW;gBACpBkC,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;YAC9B,OAAO,IAAIX,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cAC1B,IAAI,CAACM,QAAQ,CAAC;gBACZjC,OAAO,EAAE0B,GAAG,CAACY,GAAG;gBAChBJ,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACpB,UAAS,GAAI,KAAK;YACzB,OAAO;cACL,IAAI,CAACkB,QAAQ,CAAC;gBACZjC,OAAO,EAAE,OAAO;gBAChBkC,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACpB,UAAS,GAAI,KAAK;YACzB;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IACD;IACAwB,UAAUA,CAAA,EAAG;MACX,IAAI,CAACvB,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACAwB,UAAUA,CAAA,EAAG;MACX,IAAI,CAACxB,aAAY,GAAI,KAAK;IAC5B,CAAC;IACD;IACAyB,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAO,GAAIA,QAAQ;IAC1B,CAAC;IACDC,aAAaA,CAACF,IAAI,EAAE;MAClBG,OAAO,CAACC,GAAG,CAACJ,IAAI,CAAC;IACnB,CAAC;IACDK,YAAYA,CAACC,KAAK,EAAEL,QAAQ,EAAE;MAC5B,IAAI,CAACV,QAAQ,CAAC;QACZgB,QAAQ,EAAE,IAAI;QACdjD,OAAO,EAAE,UAAU;QACnBkC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD;IACAe,cAAcA,CAACR,IAAI,EAAEC,QAAQ,EAAE;MAC7B,IAAIQ,cAAa,GAAI,SAAS;MAC9B,IAAIC,eAAc,GAAID,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC;MAC/C,IAAIC,WAAU,GAAI,EAAE;MACpB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,eAAe,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/CD,WAAW,CAACjB,IAAI,CAAC,IAAIoB,MAAM,CAAC,QAAO,GAAIL,eAAe,CAACG,CAAC,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC;MAC3E;MACA,IAAIG,SAAQ,GAAI,EAAE;MAClB,IAAIV,KAAI,GAAI,EAAE;MACd,IAAIW,IAAG,GAAI,IAAI;MACfhB,QAAQ,CAACiB,OAAO,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnC,IAAIC,GAAE,GAAI,KAAK;QACf,KAAK,IAAIR,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3CQ,GAAE,GAAIA,GAAE,IAAKT,WAAW,CAACC,CAAC,CAAC,CAACS,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C;QACA,IAAI,CAACE,GAAG,EAAE;UACRlB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC,MAAM,IAAI,GAAE,GAAIE,GAAG,CAAC;UACpCJ,IAAI,CAAC1B,QAAQ,CAAC;YACZgB,QAAQ,EAAE,IAAI;YACdjD,OAAO,EAAE,YAAW,GAAImD,cAAa,GAAI,KAAK;YAC9CjB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA,IAAIuB,SAAS,CAACO,OAAO,CAACJ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;UACxCF,IAAI,CAAC1B,QAAQ,CAAC;YACZgB,QAAQ,EAAE,IAAI;YACdjD,OAAO,EAAE,UAAU;YACnBkC,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA;QACA,IAAI,CAACwB,IAAI,CAACO,UAAU,EAAE;UACpBlB,KAAI,GAAI,EAAE;UACVU,SAAQ,GAAI,EAAE;QAChB;QACAV,KAAK,CAACX,IAAI,CAACwB,GAAG,CAAC;QACfH,SAAS,CAACrB,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAIH,SAAQ,KAAM,EAAE,EAAE;UACpB;QAAA;QAEF;MACF,CAAC,CAAC;MACF,IAAI,CAACV,KAAI,GAAIU,SAAS;MACtB,IAAI,CAACf,QAAO,GAAIK,KAAK;IACvB,CAAC;IACD;;;IAGAmB,aAAaA,CAAA,EAAG;MACd,IAAIC,QAAO,GAAI,IAAI,CAACzB,QAAQ;MAC5B,IAAIyB,QAAQ,CAACZ,MAAK,KAAM,CAAC,EAAE;QACzB,IAAI,CAACvB,QAAQ,CAAC;UACZgB,QAAQ,EAAE,IAAI;UACdjD,OAAO,EAAE,QAAQ;UACjBkC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MACA,IAAIxC,QAAO,GAAI,IAAI0E,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC1B,QAAQ,CAACiB,OAAO,CAAElB,IAAI,IAAK;QAC9B/C,QAAQ,CAAC2E,MAAM,CAAC,MAAM,EAAE5B,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC6B,GAAG,CAAC9E,IAAI,CAAC;MAClD,CAAC,CAAC;MACF,IAAI2B,GAAE,GAAI5B,IAAG,GAAI,oBAAoB;MACrCqD,OAAO,CAACC,GAAG,CAAC,MAAK,GAAI1B,GAAG,CAAC;MACzB7B,OAAO,CAAC8B,IAAI,CAACD,GAAG,EAAEzB,QAAQ,CAAC,CAAC8B,IAAI,CAAEC,GAAG,IAAK;QACxCmB,OAAO,CAACC,GAAG,CAACpB,GAAG,CAAC;QAChB,IAAI8C,IAAG,GAAI9C,GAAG,CAACE,OAAO,CAACwC,QAAQ;QAC/B,IAAI,CAACzE,QAAQ,CAACmB,IAAG,GAAI0D,IAAI,EAAE;QAC3B,IAAI,CAAChC,UAAU,CAAC,CAAC;QACjBK,OAAO,CAACC,GAAG,CAACpB,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}