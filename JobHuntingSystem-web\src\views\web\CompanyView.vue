<template>
  <div class="company-detail">
    <!-- 企业基本信息 -->
    <div class="company-card">
      <div class="company-header">
        <div class="company-logo">
          <img :src="'http://localhost:8088/JobHuntingSystem/' + formData.logo" alt="企业logo" />
        </div>
        <div class="company-qualification" v-if="formData.qualification">
          <img :src="'http://localhost:8088/JobHuntingSystem/' + formData.qualification" alt="资质图片" />
        </div>
        <div class="company-info">
          <div class="name-action">
            <h1 class="company-name">{{ formData.comname }}</h1>
          
          </div>
          <div class="info-item">
            <i class="el-icon-location"></i>
            <span>地址：{{ formData.address }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-phone"></i>
            <span>联系方式：{{ formData.contact }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-office-building"></i>
            <span>企业规模：{{ formData.scale }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-collection"></i>
            <span>企业性质：{{ formData.nature }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-time"></i>
            <span>添加时间：{{ formData.addtime }}</span>
          </div>
        </div>
      </div>

      <div class="company-description">
        <h2 class="section-title">
          <i class="el-icon-document"></i>
          企业简介
        </h2>
        <div class="description-content" v-html="formData.introduction"></div>
      </div>
    </div>

    <!-- 企业招聘职位列表 -->
    <div class="positions-card">
      <h2 class="section-title">
        <i class="el-icon-suitcase"></i>
        招聘职位
      </h2>

      <div v-if="positionsList.length > 0" class="positions-list">
        <div v-for="position in positionsList" :key="position.pid" class="position-item">
          <div class="position-header">
            <h3 class="position-title">{{ position.pname }}</h3>
            <span class="salary">{{ position.streatment }}</span>
          </div>
          <div class="position-info">
            <span><i class="el-icon-location"></i>{{ position.wlocation }}</span>
            <span><i class="el-icon-user"></i>招聘人数：{{ position.rnumber }}人</span>
            <span><i class="el-icon-time"></i>发布时间：{{ position.ptime }}</span>
          </div>
          <div class="position-footer">
            <el-button type="primary" size="small" @click="viewPosition(position.pid)"
              :disabled="position.pflag !== '开放'">
              查看详情
            </el-button>
            <el-tag :type="position.pflag === '开放' ? 'success' : 'info'" size="small">
              {{ position.pflag }}
            </el-tag>
          </div>
        </div>
      </div>

      <el-empty v-else description="暂无招聘职位"></el-empty>
    </div>

    <!-- 发送私信对话框 -->
    <el-dialog title="发送私信" v-model="dialogVisible" width="500px">
      <el-form :model="messageForm" ref="messageFormRef" :rules="rules">
        <el-form-item prop="content">
          <el-input type="textarea" v-model="messageForm.content" :rows="4" placeholder="请输入私信内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="sendMessage" :loading="sending">发 送</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import request, { base } from '../../../utils/http';

  export default {
    name: 'CompanyView',
    data() {
      return {
        id: '',
        formData: {},
        positionsList: [],
        dialogVisible: false,
        sending: false,
        messageForm: {
          content: '',
          clname: '', // 企业账号
          sno: '', // 求职者账号
        },
        rules: {
          content: [
            { required: true, message: '请输入私信内容', trigger: 'blur' },
            { min: 1, max: 500, message: '长度在1到500个字符', trigger: 'blur' }
          ]
        }
      };
    },
    created() {
      this.id = this.$route.query.id;
      this.getCompanyData();
      this.getPositionsList();
    },
    methods: {
      // 获取企业信息
      async getCompanyData() {
        try {
          const res = await request.post(base + '/company/get?id=' + this.id);
          if (res.code === 200) {
            this.formData = res.resdata;
          }
        } catch (error) {
          console.error('获取企业信息失败:', error);
          this.$message({
            message: '获取企业信息失败',
            type: 'error',
            offset: 320,
          });
        }
      },

      // 获取企业发布的职位列表
      async getPositionsList() {
        try {
          const res = await request.post(
            base + '/positions/list',
            { cid: this.id, pflag: '开放', pflag2: '审核通过' },
            { params: { currentPage: 1, pageSize: 100 } }
          );
          if (res.code === 200) {
            this.positionsList = res.resdata;
          }
        } catch (error) {
          console.error('获取职位列表失败:', error);
          this.$message({
            message: '获取职位列表失败',
            type: 'error',
            offset: 320,
          });
        }
      },

      // 查看职位详情
      viewPosition(pid) {
        this.$router.push({
          path: '/positionsView',
          query: { id: pid },
        });
      },



   
    },
  };
</script>

<style scoped>
  .company-detail {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
  }

  .company-card,
  .positions-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 20px;
  }

  .company-header {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
  }

  .company-logo {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
  }

  .company-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .company-qualification {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
  }

  .company-qualification img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .company-info {
    flex: 1;
  }

  .name-action {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 15px;
  }

  .company-name {
    margin: 0;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    color: #666;
  }

  .section-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-bottom: 2px solid #409eff;
    padding-bottom: 10px;
  }

  .description-content {
    color: #666;
    line-height: 1.8;
  }

  .positions-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  .position-item {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
  }

  .position-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .position-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .position-title {
    font-size: 16px;
    color: #333;
    margin: 0;
  }

  .salary {
    color: #ff4d4f;
    font-weight: bold;
  }

  .position-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
    color: #666;
  }

  .position-info span {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .position-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .dialog-footer {
    text-align: right;
    display: block;
  }
</style>