{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Foot.vue?vue&type=template&id=24d25cc4&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Foot.vue", "mtime": 1741618811922}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Foot.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAER,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/components/Foot.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <footer class=\"footer\">\n    <!-- 主页脚 -->\n    <div class=\"footer__main\">\n      <div class=\"container\">\n        <div class=\"row\">\n          <!-- 关于我们 -->\n          <div class=\"col-md-4 footer-column\">\n            <h4 class=\"footer-title\">关于我们</h4>\n            <p class=\"footer-description\">\n              求职系统致力于为求职者和企业提供高效、便捷的招聘服务平台，帮助求职者找到理想工作，助力企业招揽优秀人才。\n            </p>\n        \n          </div>\n\n          <!-- 快速链接 -->\n          <div class=\"col-md-4 footer-column\">\n            <h4 class=\"footer-title\">快速链接</h4>\n            <ul class=\"footer-links list-no-style\">\n              <li><a href=\"/index\">网站首页</a></li>\n              <li><a href=\"/positionsList\">招聘职位</a></li>\n              <li><a href=\"/companyList\">企业展示</a></li>\n              <li><a href=\"/bbs\">交流论坛</a></li>\n              <li><a href=\"/ai\">AI顾问</a></li>\n            </ul>\n          </div>\n\n          <!-- 联系我们 -->\n          <div class=\"col-md-4 footer-column\">\n            <h4 class=\"footer-title\">联系我们</h4>\n            <ul class=\"footer-contact list-no-style\">\n              <li><i class=\"fas fa-map-marker-alt\"></i> 北京市海淀区中关村大街1号</li>\n              <li><i class=\"fas fa-phone\"></i> 010-6666-6666</li>\n              <li><i class=\"fas fa-envelope\"></i> <EMAIL></li>\n              <li><i class=\"fas fa-clock\"></i> 周一至周五 9:00-18:00</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 版权信息 -->\n    <div class=\"footer__bottom\">\n      <div class=\"container\">\n        <div class=\"row\">\n          <!-- 版权 -->\n          <div class=\"col-md-6\">\n            <p class=\"copyright\">&#9400; {{ currentYear }} 求职系统 - 版权所有</p>\n          </div>\n          <!-- 后台入口 -->\n          <div class=\"col-md-6\">\n            <ul class=\"footer__bottom--links list-no-style\">\n              <li><a href=\"/login\">后台入口</a></li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  </footer>\n\n  <!-- 返回顶部按钮 -->\n  <el-backtop :right=\"100\" :bottom=\"100\">\n    <div class=\"back-to-top\">\n      <i class=\"fas fa-arrow-up\"></i>\n    </div>\n  </el-backtop>\n</template>\n\n<script>\n  export default {\n    name: 'Foot',\n    data() {\n      return {\n        currentYear: new Date().getFullYear()\n      };\n    },\n    mounted() { },\n    methods: {},\n  };\n</script>\n\n<style scoped>\n  /* 主页脚样式 */\n  .footer {\n    background-color: #2c3e50;\n    color: #ecf0f1;\n  }\n\n  .footer__main {\n    padding: 60px 0 40px;\n  }\n\n  .footer-column {\n    margin-bottom: 30px;\n  }\n\n  .footer-title {\n    color: #fff;\n    font-size: 20px;\n    font-weight: 600;\n    margin-bottom: 20px;\n    position: relative;\n    padding-bottom: 10px;\n  }\n\n  .footer-title::after {\n    content: '';\n    position: absolute;\n    left: 0;\n    bottom: 0;\n    width: 50px;\n    height: 3px;\n    background-color: #3498db;\n    border-radius: 1.5px;\n  }\n\n  .footer-description {\n    color: #bdc3c7;\n    line-height: 1.6;\n    margin-bottom: 20px;\n  }\n\n  .footer-social {\n    display: flex;\n    gap: 15px;\n  }\n\n  .social-icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 36px;\n    height: 36px;\n    background-color: rgba(255, 255, 255, 0.1);\n    border-radius: 50%;\n    color: #ecf0f1;\n    transition: all 0.3s ease;\n  }\n\n  .social-icon:hover {\n    background-color: #3498db;\n    color: #fff;\n    transform: translateY(-3px);\n  }\n\n  .footer-links li {\n    margin-bottom: 10px;\n  }\n\n  .footer-links a {\n    color: #bdc3c7;\n    transition: all 0.3s ease;\n    display: inline-block;\n    position: relative;\n  }\n\n  .footer-links a::before {\n    content: '›';\n    margin-right: 8px;\n    color: #3498db;\n  }\n\n  .footer-links a:hover {\n    color: #3498db;\n    text-decoration: none;\n    transform: translateX(5px);\n  }\n\n  .footer-contact li {\n    color: #bdc3c7;\n    margin-bottom: 15px;\n    display: flex;\n    align-items: flex-start;\n  }\n\n  .footer-contact li i {\n    color: #3498db;\n    margin-right: 10px;\n    margin-top: 5px;\n  }\n\n  /* 版权信息样式 */\n  .footer__bottom {\n    background-color: #1a252f;\n    padding: 20px 0;\n    font-size: 14px;\n  }\n\n  .copyright {\n    margin: 0;\n  }\n\n  .footer__bottom--links {\n    display: flex;\n    justify-content: flex-end;\n    margin: 0;\n  }\n\n  .footer__bottom--links li a {\n    color: #bdc3c7;\n    transition: color 0.3s;\n  }\n\n  .footer__bottom--links li a:hover {\n    color: #3498db;\n    text-decoration: none;\n  }\n\n  /* 返回顶部按钮样式 */\n  .back-to-top {\n    height: 100%;\n    width: 100%;\n    background-color: #3498db;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #fff;\n    border-radius: 4px;\n    transition: all 0.3s ease;\n  }\n\n  .back-to-top:hover {\n    background-color: #2980b9;\n    transform: translateY(-2px);\n  }\n\n  .back-to-top i {\n    font-size: 20px;\n  }\n\n  /* 响应式设计 */\n  @media (max-width: 768px) {\n    .footer__bottom--links {\n      justify-content: center;\n      margin-top: 10px;\n    }\n\n    .copyright {\n      text-align: center;\n    }\n  }\n</style>"]}]}