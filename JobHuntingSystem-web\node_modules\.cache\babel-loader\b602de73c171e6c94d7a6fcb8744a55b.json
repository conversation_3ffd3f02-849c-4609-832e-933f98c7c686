{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsEdit.vue", "mtime": 1741615875291}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "sno", "required", "message", "trigger", "password", "sname", "gender", "phone", "pattern", "proid", "spic", "created", "$route", "query", "getDatas", "getprofessionalsList", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "proname", "save", "$refs", "validate", "valid", "code", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack", "professionalsList", "showUpload", "hideUpload", "handleRemove", "file", "fileList", "handlePreview", "console", "log", "handleExceed", "files", "duration", "fileListChange", "extendFileName", "extendFileNames", "split", "regExpRules", "i", "length", "RegExp", "fileNames", "that", "for<PERSON>ach", "key", "val", "ret", "test", "indexOf", "multiFiles", "handleConfirm", "filePath", "FormData", "append", "raw", "furl"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsEdit.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">求职者管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">编辑求职者</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">编辑求职者</h4>\n      </div>\n    </div>\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n      <el-form-item label=\"账号\" prop=\"sno\">\n        <el-input v-model=\"formData.sno\" placeholder=\"账号\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"密码\" prop=\"password\">\n        <el-input v-model=\"formData.password\" placeholder=\"密码\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"sname\">\n        <el-input v-model=\"formData.sname\" placeholder=\"姓名\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"formData.gender\">\n          <el-radio label=\"男\"> 男 </el-radio>\n          <el-radio label=\"女\"> 女 </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"年龄\" prop=\"age\">\n        <el-input v-model=\"formData.age\" placeholder=\"年龄\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"手机号码\" prop=\"phone\">\n        <el-input v-model=\"formData.phone\" placeholder=\"手机号码\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"专业\" prop=\"proid\">\n        <el-select v-model=\"formData.proid\" placeholder=\"请选择\" size=\"small\">\n          <el-option v-for=\"item in professionalsList\" :key=\"item.proid\" :label=\"item.proname\"\n            :value=\"item.proid\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item prop=\"spic\" label=\"照片\" min-width=\"20%\">\n        <el-input v-model=\"formData.spic\" placeholder=\"照片\" readonly=\"true\" style=\"width: 50%\"></el-input>\n        <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\n        <el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\n      </el-form-item>\n    </el-form>\n    <el-dialog v-model=\"uploadVisible\" title=\"附件上传\" custom-class=\"el-dialog-widthSmall\" @close=\"closeDialog\">\n      <div>\n        <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n      </div>\n      <el-upload action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n        style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\" drag :limit=\"1\"\n        :on-preview=\"handlePreview\" :on-remove=\"handleRemove\" :file-list=\"fileList\" :on-exceed=\"handleExceed\"\n        :auto-upload=\"false\" name=\"file\" :on-change=\"fileListChange\">\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\">\n          <div style=\"display: inline; color: #d70000; font-size: 14px\" class=\"uploadFileWarning\"\n            id=\"uploadFileWarning\"></div>\n        </div>\n      </el-upload>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"hideUpload\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n<script>\n  import request, { base } from '../../../../utils/http';\n\n  export default {\n    name: 'StudentsEdit',\n    components: {},\n    data() {\n      return {\n        id: '',\n        isClear: false,\n        uploadVisible: false,\n        btnLoading: false, //保存按钮加载状态\n        formData: {}, //表单数据\n        addrules: {\n          sno: [{ required: true, message: '请输入账号', trigger: 'blur' }],\n          password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\n          sname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\n          gender: [{ required: true, message: '请输入性别', trigger: 'blur' }],\n          phone: [\n            { required: true, message: '请输入手机号码', trigger: 'blur' },\n            { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\n          ],\n          proid: [{ required: true, message: '请选择专业', trigger: 'onchange' }],\n          spic: [{ required: true, message: '请输入照片', trigger: 'blur' }],\n        },\n      };\n    },\n    created() {\n      this.id = this.$route.query.id;\n      this.getDatas();\n      this.getprofessionalsList();\n    },\n\n    methods: {\n      //获取列表数据\n      getDatas() {\n        let para = {};\n        this.listLoading = true;\n        let url = base + '/students/get?id=' + this.id;\n        request.post(url, para).then((res) => {\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\n          this.listLoading = false;\n\n          this.proid = this.formData.proid;\n          this.formData.proid = this.formData.proname;\n        });\n      },\n\n      // 添加\n      save() {\n        this.$refs['formDataRef'].validate((valid) => {\n          //验证表单\n          if (valid) {\n            let url = base + '/students/update';\n            this.btnLoading = true;\n            this.formData.proid =\n              this.formData.proid == this.formData.proname ? this.proid : this.formData.proid;\n\n            request.post(url, this.formData).then((res) => {\n              //发送请求\n              if (res.code == 200) {\n                this.$message({\n                  message: '操作成功',\n                  type: 'success',\n                  offset: 320,\n                });\n                this.$router.push({\n                  path: '/StudentsManage',\n                });\n              } else {\n                this.$message({\n                  message: res.msg,\n                  type: 'error',\n                  offset: 320,\n                });\n              }\n              this.btnLoading = false;\n            });\n          }\n        });\n      },\n\n      // 返回\n      goBack() {\n        this.$router.push({\n          path: '/StudentsManage',\n        });\n      },\n\n      getprofessionalsList() {\n        let para = {};\n        this.listLoading = true;\n        let url = base + '/professionals/list?currentPage=1&pageSize=1000';\n        request.post(url, para).then((res) => {\n          this.professionalsList = res.resdata;\n        });\n      },\n\n      //显示上传框\n      showUpload() {\n        this.uploadVisible = true;\n      },\n\n      //隐藏上传框\n      hideUpload() {\n        this.uploadVisible = false;\n      },\n      //上传\n      handleRemove(file, fileList) {\n        this.fileList = fileList;\n      },\n      handlePreview(file) {\n        console.log(file);\n      },\n      handleExceed(files, fileList) {\n        this.$message({\n          duration: 1000,\n          message: '只能上传一个文件',\n          type: 'error',\n          offset: 320,\n        });\n      },\n      // 判断上传文件后缀\n      fileListChange(file, fileList) {\n        let extendFileName = 'png,jpg';\n        let extendFileNames = extendFileName.split(',');\n        let regExpRules = [];\n        for (let i = 0; i < extendFileNames.length; i++) {\n          regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n        }\n        let fileNames = [];\n        let files = [];\n        let that = this;\n        fileList.forEach(function (key, val) {\n          let ret = false;\n          for (let i = 0; i < regExpRules.length; i++) {\n            ret = ret || regExpRules[i].test(key['name']);\n          }\n          if (!ret) {\n            console.log(key['name'] + ':' + ret);\n            that.$message({\n              duration: 1000,\n              message: '上传的文件后缀必须为' + extendFileName + '格式！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          if (fileNames.indexOf(key['name']) != -1) {\n            that.$message({\n              duration: 1000,\n              message: '上传的文件重复！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          //只能上传一个文件，用最后上传的覆盖\n          if (!that.multiFiles) {\n            files = [];\n            fileNames = [];\n          }\n          files.push(key);\n          fileNames.push(key['name']);\n          if (fileNames !== '') {\n            // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n          }\n          //$(\".uploadFileWarning\").text(\"\");\n        });\n        this.files = fileNames;\n        this.fileList = files;\n      },\n      /**\n       * 确认按钮\n       */\n      handleConfirm() {\n        let filePath = this.fileList;\n        if (filePath.length === 0) {\n          this.$message({\n            duration: 1000,\n            message: '请选择文件！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        let formData = new FormData();\n        this.fileList.forEach((file) => {\n          formData.append('file', file.raw, file.raw.name);\n        });\n        let url = base + '/common/uploadFile';\n        console.log('url=' + url);\n        request.post(url, formData).then((res) => {\n          console.log(res);\n          let furl = res.resdata.filePath;\n          this.formData.spic = furl; // 上传文件的路径\n          this.hideUpload();\n          console.log(res);\n        });\n      },\n    },\n  };\n</script>\n<style scoped></style>"], "mappings": ";;;AA0EE,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE,CAAC,CAAC;EACdC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC5DC,QAAQ,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACjEE,KAAK,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC9DG,MAAM,EAAE,CAAC;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC/DI,KAAK,EAAE,CACL;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACvD;UAAEK,OAAO,EAAE,mBAAmB;UAAEN,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CACxE;QACDM,KAAK,EAAE,CAAC;UAAER,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAW,CAAC,CAAC;QAClEO,IAAI,EAAE,CAAC;UAAET,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC;MAC9D;IACF,CAAC;EACH,CAAC;EACDQ,OAAOA,CAAA,EAAG;IACR,IAAI,CAACjB,EAAC,GAAI,IAAI,CAACkB,MAAM,CAACC,KAAK,CAACnB,EAAE;IAC9B,IAAI,CAACoB,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B,CAAC;EAEDC,OAAO,EAAE;IACP;IACAF,QAAQA,CAAA,EAAG;MACT,IAAIG,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAI7B,IAAG,GAAI,mBAAkB,GAAI,IAAI,CAACI,EAAE;MAC9CL,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACxB,QAAO,GAAIyB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;QAExB,IAAI,CAACT,KAAI,GAAI,IAAI,CAACX,QAAQ,CAACW,KAAK;QAChC,IAAI,CAACX,QAAQ,CAACW,KAAI,GAAI,IAAI,CAACX,QAAQ,CAAC6B,OAAO;MAC7C,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAC5C;QACA,IAAIA,KAAK,EAAE;UACT,IAAIZ,GAAE,GAAI7B,IAAG,GAAI,kBAAkB;UACnC,IAAI,CAACO,UAAS,GAAI,IAAI;UACtB,IAAI,CAACC,QAAQ,CAACW,KAAI,GAChB,IAAI,CAACX,QAAQ,CAACW,KAAI,IAAK,IAAI,CAACX,QAAQ,CAAC6B,OAAM,GAAI,IAAI,CAAClB,KAAI,GAAI,IAAI,CAACX,QAAQ,CAACW,KAAK;UAEjFpB,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAE,IAAI,CAACrB,QAAQ,CAAC,CAACuB,IAAI,CAAEC,GAAG,IAAK;YAC7C;YACA,IAAIA,GAAG,CAACU,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZ/B,OAAO,EAAE,MAAM;gBACfgC,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZ/B,OAAO,EAAEoB,GAAG,CAACiB,GAAG;gBAChBL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAACtC,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACA2C,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAEDvB,oBAAoBA,CAAA,EAAG;MACrB,IAAIE,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAI7B,IAAG,GAAI,iDAAiD;MAClED,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACmB,iBAAgB,GAAInB,GAAG,CAACI,OAAO;MACtC,CAAC,CAAC;IACJ,CAAC;IAED;IACAgB,UAAUA,CAAA,EAAG;MACX,IAAI,CAAC9C,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACA+C,UAAUA,CAAA,EAAG;MACX,IAAI,CAAC/C,aAAY,GAAI,KAAK;IAC5B,CAAC;IACD;IACAgD,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAO,GAAIA,QAAQ;IAC1B,CAAC;IACDC,aAAaA,CAACF,IAAI,EAAE;MAClBG,OAAO,CAACC,GAAG,CAACJ,IAAI,CAAC;IACnB,CAAC;IACDK,YAAYA,CAACC,KAAK,EAAEL,QAAQ,EAAE;MAC5B,IAAI,CAACb,QAAQ,CAAC;QACZmB,QAAQ,EAAE,IAAI;QACdlD,OAAO,EAAE,UAAU;QACnBgC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD;IACAkB,cAAcA,CAACR,IAAI,EAAEC,QAAQ,EAAE;MAC7B,IAAIQ,cAAa,GAAI,SAAS;MAC9B,IAAIC,eAAc,GAAID,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC;MAC/C,IAAIC,WAAU,GAAI,EAAE;MACpB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,eAAe,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/CD,WAAW,CAACpB,IAAI,CAAC,IAAIuB,MAAM,CAAC,QAAO,GAAIL,eAAe,CAACG,CAAC,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC;MAC3E;MACA,IAAIG,SAAQ,GAAI,EAAE;MAClB,IAAIV,KAAI,GAAI,EAAE;MACd,IAAIW,IAAG,GAAI,IAAI;MACfhB,QAAQ,CAACiB,OAAO,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnC,IAAIC,GAAE,GAAI,KAAK;QACf,KAAK,IAAIR,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3CQ,GAAE,GAAIA,GAAE,IAAKT,WAAW,CAACC,CAAC,CAAC,CAACS,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C;QACA,IAAI,CAACE,GAAG,EAAE;UACRlB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC,MAAM,IAAI,GAAE,GAAIE,GAAG,CAAC;UACpCJ,IAAI,CAAC7B,QAAQ,CAAC;YACZmB,QAAQ,EAAE,IAAI;YACdlD,OAAO,EAAE,YAAW,GAAIoD,cAAa,GAAI,KAAK;YAC9CpB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA,IAAI0B,SAAS,CAACO,OAAO,CAACJ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;UACxCF,IAAI,CAAC7B,QAAQ,CAAC;YACZmB,QAAQ,EAAE,IAAI;YACdlD,OAAO,EAAE,UAAU;YACnBgC,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA;QACA,IAAI,CAAC2B,IAAI,CAACO,UAAU,EAAE;UACpBlB,KAAI,GAAI,EAAE;UACVU,SAAQ,GAAI,EAAE;QAChB;QACAV,KAAK,CAACd,IAAI,CAAC2B,GAAG,CAAC;QACfH,SAAS,CAACxB,IAAI,CAAC2B,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAIH,SAAQ,KAAM,EAAE,EAAE;UACpB;QAAA;QAEF;MACF,CAAC,CAAC;MACF,IAAI,CAACV,KAAI,GAAIU,SAAS;MACtB,IAAI,CAACf,QAAO,GAAIK,KAAK;IACvB,CAAC;IACD;;;IAGAmB,aAAaA,CAAA,EAAG;MACd,IAAIC,QAAO,GAAI,IAAI,CAACzB,QAAQ;MAC5B,IAAIyB,QAAQ,CAACZ,MAAK,KAAM,CAAC,EAAE;QACzB,IAAI,CAAC1B,QAAQ,CAAC;UACZmB,QAAQ,EAAE,IAAI;UACdlD,OAAO,EAAE,QAAQ;UACjBgC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MACA,IAAIrC,QAAO,GAAI,IAAI0E,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC1B,QAAQ,CAACiB,OAAO,CAAElB,IAAI,IAAK;QAC9B/C,QAAQ,CAAC2E,MAAM,CAAC,MAAM,EAAE5B,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC6B,GAAG,CAACnF,IAAI,CAAC;MAClD,CAAC,CAAC;MACF,IAAI4B,GAAE,GAAI7B,IAAG,GAAI,oBAAoB;MACrC0D,OAAO,CAACC,GAAG,CAAC,MAAK,GAAI9B,GAAG,CAAC;MACzB9B,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAErB,QAAQ,CAAC,CAACuB,IAAI,CAAEC,GAAG,IAAK;QACxC0B,OAAO,CAACC,GAAG,CAAC3B,GAAG,CAAC;QAChB,IAAIqD,IAAG,GAAIrD,GAAG,CAACI,OAAO,CAAC6C,QAAQ;QAC/B,IAAI,CAACzE,QAAQ,CAACY,IAAG,GAAIiE,IAAI,EAAE;QAC3B,IAAI,CAAChC,UAAU,CAAC,CAAC;QACjBK,OAAO,CAACC,GAAG,CAAC3B,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}