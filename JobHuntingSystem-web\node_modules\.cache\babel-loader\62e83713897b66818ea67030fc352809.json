{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsAdd.vue?vue&type=template&id=553c4954", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsAdd.vue", "mtime": 1741603034000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_input", "pname", "$event", "placeholder", "style", "_component_el_select", "catid", "size", "_Fragment", "_renderList", "jobcategoriesList", "item", "_createBlock", "_component_el_option", "key", "catname", "value", "wlocation", "rnumber", "streatment", "_component_WangEditor", "prequirements", "config", "_ctx", "editorConfig", "isClear", "onChange", "$options", "<PERSON><PERSON><PERSON><PERSON>", "_component_el_radio_group", "pflag", "_component_el_radio", "_cache", "_component_el_button", "type", "onClick", "save", "loading", "btnLoading", "icon", "goBack"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsAdd.vue"], "sourcesContent": ["<template>\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">职位管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">添加职位</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">添加职位</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\r\n      <el-form-item label=\"职位名称\" prop=\"pname\">\r\n        <el-input v-model=\"formData.pname\" placeholder=\"职位名称\" style=\"width: 50%\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"职位分类\" prop=\"catid\">\r\n        <el-select v-model=\"formData.catid\" placeholder=\"请选择\" size=\"small\" style=\"width: 50%\">\r\n          <el-option\r\n            v-for=\"item in jobcategoriesList\"\r\n            :key=\"item.catid\"\r\n            :label=\"item.catname\"\r\n            :value=\"item.catid\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"工作地点\" prop=\"wlocation\">\r\n        <el-input v-model=\"formData.wlocation\" placeholder=\"工作地点\" style=\"width: 50%\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"招聘人数\" prop=\"rnumber\">\r\n        <el-input v-model=\"formData.rnumber\" placeholder=\"招聘人数\" style=\"width: 50%\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"薪资待遇\" prop=\"streatment\">\r\n        <el-input\r\n          v-model=\"formData.streatment\"\r\n          placeholder=\"薪资待遇\"\r\n          style=\"width: 50%\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"职位要求\" prop=\"prequirements\">\r\n        <WangEditor\r\n          ref=\"wangEditorRef\"\r\n          v-model=\"formData.prequirements\"\r\n          :config=\"editorConfig\"\r\n          :isClear=\"isClear\"\r\n          @change=\"editorChange\"\r\n        ></WangEditor>\r\n      </el-form-item>\r\n      <el-form-item label=\"招聘状态\" prop=\"pflag\">\r\n        <el-radio-group v-model=\"formData.pflag\">\r\n          <el-radio label=\"开放\"> 开放 </el-radio>\r\n          <el-radio label=\"关闭\"> 关闭 </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          @click=\"save\"\r\n          :loading=\"btnLoading\"\r\n          icon=\"el-icon-upload\"\r\n          >提 交</el-button\r\n        >\r\n        <el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport request, { base } from '../../../../utils/http';\r\nimport WangEditor from '../../../components/WangEditor';\r\nexport default {\r\n  name: 'PositionsAdd',\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      uploadVisible: false,\r\n      btnLoading: false, //保存按钮加载状态\r\n      formData: {}, //表单数据\r\n      jobcategoriesList: [], // 职位分类列表\r\n      addrules: {\r\n        pname: [{ required: true, message: '请输入职位名称', trigger: 'blur' }],\r\n        catid: [{ required: true, message: '请选择职位分类', trigger: 'onchange' }],\r\n        wlocation: [{ required: true, message: '请输入工作地点', trigger: 'blur' }],\r\n        rnumber: [{ required: true, message: '请输入招聘人数', trigger: 'blur' }],\r\n        streatment: [{ required: true, message: '请输入薪资待遇', trigger: 'blur' }],\r\n        pflag: [{ required: true, message: '请输入招聘状态', trigger: 'blur' }],\r\n        cid: [{ required: true, message: '请输入企业id', trigger: 'blur' }],\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getJobCategories();\r\n  },\r\n\r\n  methods: {\r\n    // 获取职位分类列表\r\n    getJobCategories() {\r\n      let url = base + '/jobcategories/list';\r\n      request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.jobcategoriesList = res.resdata;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 添加\r\n    save() {\r\n      this.$refs['formDataRef'].validate((valid) => {\r\n        //验证表单\r\n        if (valid) {\r\n          let url = base + '/positions/add';\r\n\r\n          var user = JSON.parse(sessionStorage.getItem('user'));\r\n\r\n          this.formData.cid = user.cid;\r\n\r\n          this.btnLoading = true;\r\n          request.post(url, this.formData).then((res) => {\r\n            //发送请求\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: '操作成功',\r\n                type: 'success',\r\n                offset: 320,\r\n              });\r\n              this.$router.push({\r\n                path: '/PositionsManage',\r\n              });\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: 'error',\r\n                offset: 320,\r\n              });\r\n            }\r\n            this.btnLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 返回\r\n    goBack() {\r\n      this.$router.push({\r\n        path: '/PositionsManage',\r\n      });\r\n    },\r\n\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.prequirements = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped></style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAK;;;;;;;;;;;uBAAhBC,mBAAA,CAoEM,OApENC,UAoEM,G,wWAxDJC,YAAA,CAuDUC,kBAAA;IAvDAC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAEC,KAAK,EAAC;;sBACrF,MAEe,CAFfR,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAoF,CAApFX,YAAA,CAAoFY,mBAAA;oBAAjET,KAAA,CAAAC,QAAQ,CAACS,KAAK;mEAAdV,KAAA,CAAAC,QAAQ,CAACS,KAAK,GAAAC,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAExDhB,YAAA,CAUeS,uBAAA;MAVDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAQY,CARZX,YAAA,CAQYiB,oBAAA;oBARQd,KAAA,CAAAC,QAAQ,CAACc,KAAK;mEAAdf,KAAA,CAAAC,QAAQ,CAACc,KAAK,GAAAJ,MAAA;QAAEC,WAAW,EAAC,KAAK;QAACI,IAAI,EAAC,OAAO;QAACH,KAAkB,EAAlB;UAAA;QAAA;;0BAE/D,MAAiC,E,kBADnClB,mBAAA,CAMYsB,SAAA,QAAAC,WAAA,CALKlB,KAAA,CAAAmB,iBAAiB,EAAzBC,IAAI;+BADbC,YAAA,CAMYC,oBAAA;YAJTC,GAAG,EAAEH,IAAI,CAACL,KAAK;YACfR,KAAK,EAAEa,IAAI,CAACI,OAAO;YACnBC,KAAK,EAAEL,IAAI,CAACL;;;;;;QAKnBlB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAwF,CAAxFX,YAAA,CAAwFY,mBAAA;oBAArET,KAAA,CAAAC,QAAQ,CAACyB,SAAS;mEAAlB1B,KAAA,CAAAC,QAAQ,CAACyB,SAAS,GAAAf,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAE5DhB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAAC0B,OAAO;mEAAhB3B,KAAA,CAAAC,QAAQ,CAAC0B,OAAO,GAAAhB,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAE1DhB,YAAA,CAMeS,uBAAA;MANDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAIY,CAJZX,YAAA,CAIYY,mBAAA;oBAHDT,KAAA,CAAAC,QAAQ,CAAC2B,UAAU;mEAAnB5B,KAAA,CAAAC,QAAQ,CAAC2B,UAAU,GAAAjB,MAAA;QAC5BC,WAAW,EAAC,MAAM;QAClBC,KAAkB,EAAlB;UAAA;QAAA;;;QAGJhB,YAAA,CAQeS,uBAAA;MARDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAMc,CANdX,YAAA,CAMcgC,qBAAA;QALZ3B,GAAG,EAAC,eAAe;oBACVF,KAAA,CAAAC,QAAQ,CAAC6B,aAAa;mEAAtB9B,KAAA,CAAAC,QAAQ,CAAC6B,aAAa,GAAAnB,MAAA;QAC9BoB,MAAM,EAAEC,IAAA,CAAAC,YAAY;QACpBC,OAAO,EAAEF,IAAA,CAAAE,OAAO;QAChBC,QAAM,EAAEC,QAAA,CAAAC;;;QAGbxC,YAAA,CAKeS,uBAAA;MALDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAGiB,CAHjBX,YAAA,CAGiByC,yBAAA;oBAHQtC,KAAA,CAAAC,QAAQ,CAACsC,KAAK;mEAAdvC,KAAA,CAAAC,QAAQ,CAACsC,KAAK,GAAA5B,MAAA;;0BACrC,MAAoC,CAApCd,YAAA,CAAoC2C,mBAAA;UAA1BjC,KAAK,EAAC;QAAI;4BAAC,MAAIkC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;YACzB5C,YAAA,CAAoC2C,mBAAA;UAA1BjC,KAAK,EAAC;QAAI;4BAAC,MAAIkC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;;;;QAI7B5C,YAAA,CAUeS,uBAAA;wBATb,MAM4B,CAN5BT,YAAA,CAM4B6C,oBAAA;QAL1BC,IAAI,EAAC,SAAS;QACd3B,IAAI,EAAC,OAAO;QACX4B,OAAK,EAAER,QAAA,CAAAS,IAAI;QACXC,OAAO,EAAE9C,KAAA,CAAA+C,UAAU;QACpBC,IAAI,EAAC;;0BACJ,MAAGP,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;iDAEN5C,YAAA,CAAuF6C,oBAAA;QAA5EC,IAAI,EAAC,MAAM;QAAC3B,IAAI,EAAC,OAAO;QAAE4B,OAAK,EAAER,QAAA,CAAAa,MAAM;QAAED,IAAI,EAAC;;0BAAe,MAAGP,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E", "ignoreList": []}]}