{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue?vue&type=style&index=0&id=7a4880f2&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue", "mtime": 1741617073822}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue"], "names": [], "mappings": ";EAoGE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;EACF", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Sweclome.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"welcome-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <div class=\"banner-content\">\r\n        <div class=\"user-avatar\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" alt=\"用户头像\" />\r\n        </div>\r\n        <div class=\"user-info\">\r\n          <h2 class=\"welcome-text\">欢迎回来，<span class=\"user-name\">{{ lname }}</span></h2>\r\n          <p class=\"login-time\" style=\"color: white;\">登录时间：{{ time }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n\r\n\r\n    <!-- 快捷操作 -->\r\n    <div class=\"quick-actions\">\r\n      <div class=\"section-title\">\r\n        <h3>快捷操作</h3>\r\n      </div>\r\n\r\n      <div class=\"row\">\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-plus-circle\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">创建简历</h4>\r\n            <p class=\"action-desc\">创建一份专业的简历，展示你的技能和经验</p>\r\n            <a href=\"/resume_Add\" class=\"action-link\">立即创建</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-search\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">搜索职位</h4>\r\n            <p class=\"action-desc\">浏览最新的职位信息，寻找适合你的工作机会</p>\r\n            <a href=\"/positionsList\" class=\"action-link\">开始搜索</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-robot\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">AI顾问</h4>\r\n            <p class=\"action-desc\">获取个性化的职业建议和简历优化指导</p>\r\n            <a href=\"/ai\" class=\"action-link\">咨询顾问</a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n  export default {\r\n    name: 'Sweclome',\r\n    data() {\r\n      return {\r\n        lname: '',\r\n        formData: {}, //表单数据\r\n        time: '', //当前时间\r\n        resumeCount: 0,\r\n        deliveryCount: 0,\r\n        browseCount: 0,\r\n        favoriteCount: 0\r\n      };\r\n    },\r\n    created() {\r\n      this.lname = sessionStorage.getItem('lname');\r\n      this.getDatas();\r\n      this.time = new Date().toLocaleString();\r\n    },\r\n    methods: {\r\n      //获取个人信息数据\r\n      getDatas() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + '/students/get?id=' + this.lname;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n  \r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 欢迎页面容器 */\r\n  .welcome-container {\r\n    padding: 20px;\r\n  }\r\n\r\n  /* 欢迎横幅 */\r\n  .welcome-banner {\r\n    background: linear-gradient(135deg, #3498db, #2c3e50);\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    margin-bottom: 30px;\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .banner-content {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .user-avatar {\r\n    margin-right: 30px;\r\n  }\r\n\r\n  .user-avatar img {\r\n    width: 100px;\r\n    height: 100px;\r\n    border-radius: 50%;\r\n    border: 4px solid rgba(255, 255, 255, 0.3);\r\n    object-fit: cover;\r\n  }\r\n\r\n  .user-info {\r\n    color: #fff;\r\n  }\r\n\r\n  .welcome-text {\r\n    font-size: 24px;\r\n    margin-bottom: 10px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .user-name {\r\n    color: #f1c40f;\r\n    font-weight: 700;\r\n  }\r\n\r\n  .login-time {\r\n    font-size: 14px;\r\n    opacity: 0.8;\r\n    margin: 0;\r\n  }\r\n\r\n  /* 统计卡片 */\r\n  .stats-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .stats-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .stats-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .stats-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 10px;\r\n    background-color: rgba(231, 76, 60, 0.1);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 15px;\r\n  }\r\n\r\n  .stats-icon i {\r\n    font-size: 24px;\r\n    color: #e74c3c;\r\n  }\r\n\r\n  .stats-icon.blue {\r\n    background-color: rgba(52, 152, 219, 0.1);\r\n  }\r\n\r\n  .stats-icon.blue i {\r\n    color: #3498db;\r\n  }\r\n\r\n  .stats-icon.green {\r\n    background-color: rgba(46, 204, 113, 0.1);\r\n  }\r\n\r\n  .stats-icon.green i {\r\n    color: #2ecc71;\r\n  }\r\n\r\n  .stats-icon.purple {\r\n    background-color: rgba(155, 89, 182, 0.1);\r\n  }\r\n\r\n  .stats-icon.purple i {\r\n    color: #9b59b6;\r\n  }\r\n\r\n  .stats-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .stats-title {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin: 0 0 5px;\r\n  }\r\n\r\n  .stats-value {\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    color: #2c3e50;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .stats-action a {\r\n    color: #3498db;\r\n    font-size: 13px;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .stats-action a:hover {\r\n    color: #2980b9;\r\n  }\r\n\r\n  .stats-action i {\r\n    font-size: 10px;\r\n    margin-left: 3px;\r\n  }\r\n\r\n  /* 个人信息卡片 */\r\n  .profile-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .section-title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .section-title h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0;\r\n    position: relative;\r\n    padding-left: 15px;\r\n  }\r\n\r\n  .section-title h3::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: 0;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 5px;\r\n    height: 20px;\r\n    background-color: #3498db;\r\n    border-radius: 2.5px;\r\n  }\r\n\r\n  .edit-link {\r\n    color: #3498db;\r\n    font-size: 14px;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .edit-link:hover {\r\n    color: #2980b9;\r\n  }\r\n\r\n  .profile-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 25px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  }\r\n\r\n  .info-group {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-label {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .info-value {\r\n    font-size: 16px;\r\n    color: #2c3e50;\r\n    font-weight: 500;\r\n  }\r\n\r\n  /* 快捷操作 */\r\n  .quick-actions {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .action-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 25px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n    height: 100%;\r\n  }\r\n\r\n  .action-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .action-icon {\r\n    width: 70px;\r\n    height: 70px;\r\n    border-radius: 50%;\r\n    background-color: rgba(52, 152, 219, 0.1);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin: 0 auto 20px;\r\n  }\r\n\r\n  .action-icon i {\r\n    font-size: 30px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .action-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .action-desc {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin-bottom: 20px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  .action-link {\r\n    display: inline-block;\r\n    padding: 8px 20px;\r\n    background-color: #3498db;\r\n    color: #fff;\r\n    border-radius: 5px;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .action-link:hover {\r\n    background-color: #2980b9;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n  @media (max-width: 768px) {\r\n    .banner-content {\r\n      flex-direction: column;\r\n      text-align: center;\r\n    }\r\n\r\n    .user-avatar {\r\n      margin-right: 0;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .welcome-text {\r\n      font-size: 20px;\r\n    }\r\n  }\r\n</style>"]}]}