{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Resume_Add.vue?vue&type=template&id=a6506278", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Resume_Add.vue", "mtime": 1741614895979}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_createBlock", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_createVNode", "_component_el_form_item", "label", "prop", "_component_el_input", "resumename", "$event", "placeholder", "style", "type", "rows", "education", "size", "parttimejob", "introduction", "_component_el_button", "onClick", "$options", "save", "loading", "btnLoading", "icon", "_cache", "back"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Resume_Add.vue"], "sourcesContent": ["<template>\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n    <el-form-item label=\"简历名称\" prop=\"resumename\">\n      <el-input v-model=\"formData.resumename\" placeholder=\"简历名称\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"教育经历\" prop=\"education\">\n      <el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.education\" placeholder=\"教育经历\" size=\"small\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"工作经历\" prop=\"parttimejob\">\n      <el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.parttimejob\" placeholder=\"工作经历\" size=\"small\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"个人简介\" prop=\"introduction\">\n      <el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.introduction\" placeholder=\"个人简介\" size=\"small\"></el-input>\n    </el-form-item>\n\n    <el-form-item>\n      <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\n      <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\n    </el-form-item>\n  </el-form>\n</template>\n<script>\n  import request, { base } from '../../../utils/http';\n\n  export default {\n    name: 'Resume_Add',\n    components: {},\n    data() {\n      return {\n        uploadVisible: false,\n        btnLoading: false, //保存按钮加载状态\n        formData: {}, //表单数据\n        addrules: {\n          resumename: [{ required: true, message: '请输入简历名称', trigger: 'blur' }],\n          education: [{ required: true, message: '请输入教育经历', trigger: 'blur' }],\n          parttimejob: [{ required: true, message: '请输入工作经历', trigger: 'blur' }],\n          introduction: [{ required: true, message: '请输入个人简介', trigger: 'blur' }],\n          sno: [{ required: true, message: '请输入账号', trigger: 'blur' }],\n        },\n      };\n    },\n    mounted() {\n      this.id = this.$route.query.id;\n    },\n\n    methods: {\n      // 添加\n      save() {\n        this.$refs['formDataRef'].validate((valid) => {\n          //验证表单\n          if (valid) {\n            let url = base + '/resume/add';\n            this.btnLoading = true;\n\n            this.formData.sno = sessionStorage.getItem('lname');\n\n            request.post(url, this.formData).then((res) => {\n              //发送请求\n              if (res.code == 200) {\n                this.$message({\n                  message: '操作成功',\n                  type: 'success',\n                  offset: 320,\n                });\n                this.$router.push({\n                  path: '/Resume_Manage',\n                });\n              } else {\n                this.$message({\n                  message: '服务器错误',\n                  type: 'error',\n                  offset: 320,\n                });\n              }\n            });\n          }\n        });\n      },\n\n      // 返回\n      back() {\n        //返回上一页\n        this.$router.go(-1);\n      },\n    },\n  };\n</script>\n\n<style></style>"], "mappings": ";;;;;;uBACEA,YAAA,CAkBUC,kBAAA;IAlBAC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAEC,KAAK,EAAC;;sBACrF,MAEe,CAFfC,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAyF,CAAzFH,YAAA,CAAyFI,mBAAA;oBAAtEV,KAAA,CAAAC,QAAQ,CAACU,UAAU;mEAAnBX,KAAA,CAAAC,QAAQ,CAACU,UAAU,GAAAC,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAE7DR,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAA4G,CAA5GH,YAAA,CAA4GI,mBAAA;QAAlGK,IAAI,EAAC,UAAU;QAAEC,IAAI,EAAE,CAAC;oBAAWhB,KAAA,CAAAC,QAAQ,CAACgB,SAAS;mEAAlBjB,KAAA,CAAAC,QAAQ,CAACgB,SAAS,GAAAL,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACK,IAAI,EAAC;;;QAE3FZ,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAA8G,CAA9GH,YAAA,CAA8GI,mBAAA;QAApGK,IAAI,EAAC,UAAU;QAAEC,IAAI,EAAE,CAAC;oBAAWhB,KAAA,CAAAC,QAAQ,CAACkB,WAAW;mEAApBnB,KAAA,CAAAC,QAAQ,CAACkB,WAAW,GAAAP,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACK,IAAI,EAAC;;;QAE7FZ,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAA+G,CAA/GH,YAAA,CAA+GI,mBAAA;QAArGK,IAAI,EAAC,UAAU;QAAEC,IAAI,EAAE,CAAC;oBAAWhB,KAAA,CAAAC,QAAQ,CAACmB,YAAY;mEAArBpB,KAAA,CAAAC,QAAQ,CAACmB,YAAY,GAAAR,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACK,IAAI,EAAC;;;QAG9FZ,YAAA,CAGeC,uBAAA;wBAFb,MAAgH,CAAhHD,YAAA,CAAgHe,oBAAA;QAArGN,IAAI,EAAC,SAAS;QAACG,IAAI,EAAC,OAAO;QAAEI,OAAK,EAAEC,QAAA,CAAAC,IAAI;QAAGC,OAAO,EAAEzB,KAAA,CAAA0B,UAAU;QAAEC,IAAI,EAAC;;0BAAiB,MAAGC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;iDACpGtB,YAAA,CAAqFe,oBAAA;QAA1EN,IAAI,EAAC,MAAM;QAACG,IAAI,EAAC,OAAO;QAAEI,OAAK,EAAEC,QAAA,CAAAM,IAAI;QAAEF,IAAI,EAAC;;0BAAe,MAAGC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E", "ignoreList": []}]}