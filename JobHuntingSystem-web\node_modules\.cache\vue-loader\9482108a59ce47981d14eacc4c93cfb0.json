{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Default.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Default.vue", "mtime": 1741615257013}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Default.vue"], "names": [], "mappings": ";EAwJE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;UACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC;YACD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;QACD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC;IACH,CAAC;EACH,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Default.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <!--幻灯片大图开始-->\r\n  <div id=\"banner_main\">\r\n    <div id=\"banner\" class=\"banner\" style=\"height: 400px\">\r\n      <div class=\"swiper-container swiper-container1\">\r\n        <div class=\"swiper-wrapper\">\r\n          <div class=\"swiper-slide\">\r\n            <img style=\"width: 100%; height: 405px\" :src=\"banner1\" />\r\n          </div>\r\n          <div class=\"swiper-slide\">\r\n            <img style=\"width: 100%; height: 405px\" :src=\"banner2\" />\r\n          </div>\r\n          <div class=\"swiper-slide\">\r\n            <img style=\"width: 100%; height: 405px\" :src=\"banner3\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <!--幻灯片大图结束-->\r\n  <div class=\"main-content\">\r\n    <div class=\"container\">\r\n      <div class=\"pb-90\" style=\"padding-top: 5.625rem\">\r\n        <!-- main title -->\r\n        <div class=\"row\">\r\n          <div class=\"col-12\">\r\n            <div class=\"main-title\">\r\n              <h2 class=\"title\">推荐职位</h2>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- main title -->\r\n        <!-- services icon box -->\r\n        <div class=\"row\">\r\n          <div class=\"col-lg-4 col-md-6 wow fadeIn\" data-wow-delay=\"300ms\" v-for=\"(item, index) in list1\" :key=\"index\">\r\n            <div class=\"icon-box icon-box--border\">\r\n              <div class=\"icon-box__heading\">\r\n                <h3 class=\"heading-title\">{{ item.pname }}</h3>\r\n              </div>\r\n              <div class=\"icon-box__content\">\r\n                <div class=\"job-info\">\r\n                  <p><i class=\"el-icon-location\"></i> 工作地点：{{ item.wlocation }}</p>\r\n                  <p><i class=\"el-icon-user\"></i> 招聘人数：{{ item.rnumber }}</p>\r\n                  <p>\r\n                    <i class=\"el-icon-money\"></i> 薪资待遇：<span class=\"salary\">{{\r\n                      item.streatment\r\n                      }}</span>\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <div class=\"icon-box__btn\">\r\n                <a :href=\"'positionsView?id=' + item.pid\">查看详情<span><i class=\"fa fa-chevron-right\"></i></span></a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- end services icon box -->\r\n      </div>\r\n\r\n      <div class=\"pb-90\" style=\"padding-top: 5.625rem\">\r\n        <!-- main title -->\r\n        <div class=\"row\">\r\n          <div class=\"col-12\">\r\n            <div class=\"main-title\">\r\n              <h2 class=\"title\">最新职位</h2>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- main title -->\r\n        <!-- services icon box -->\r\n        <div class=\"row\">\r\n          <div class=\"col-lg-4 col-md-6 wow fadeIn\" data-wow-delay=\"300ms\" v-for=\"(item, index) in list2\" :key=\"index\">\r\n            <div class=\"icon-box icon-box--border\">\r\n              <div class=\"icon-box__heading\">\r\n                <h3 class=\"heading-title\">{{ item.pname }}</h3>\r\n              </div>\r\n              <div class=\"icon-box__content\">\r\n                <div class=\"job-info\">\r\n                  <p><i class=\"el-icon-location\"></i> 工作地点：{{ item.wlocation }}</p>\r\n                  <p><i class=\"el-icon-user\"></i> 招聘人数：{{ item.rnumber }}</p>\r\n                  <p>\r\n                    <i class=\"el-icon-money\"></i> 薪资待遇：<span class=\"salary\">{{\r\n                      item.streatment\r\n                      }}</span>\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <div class=\"icon-box__btn\">\r\n                <a :href=\"'positionsView?id=' + item.pid\">查看详情<span><i class=\"fa fa-chevron-right\"></i></span></a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- end services icon box -->\r\n      </div>\r\n      <div class=\"container pb-90\">\r\n        <!-- main title -->\r\n        <div class=\"row\">\r\n          <div class=\"col-12\">\r\n            <div class=\"main-title\">\r\n              <h2 class=\"title\">最新企业</h2>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- main title -->\r\n        <div class=\"row\">\r\n          <div class=\"col-lg-4 col-md-6 wow fadeInUp\" v-for=\"(item, index) in list3\" :key=\"index\">\r\n            <div class=\"blog-post blog-post--card\">\r\n              <!-- post card image -->\r\n              <div class=\"blog-post__img\">\r\n                <a :href=\"'companyView?id=' + item.cid\"><img\r\n                    :src=\"'http://localhost:8088/JobHuntingSystem/' + item.logo\"\r\n                    style=\"width: 340px; height: 230px\" /></a>\r\n              </div>\r\n              <!-- post card body -->\r\n              <div class=\"blog-post__body\">\r\n                <!-- post card title -->\r\n                <div class=\"blog-post__body--title\">\r\n                  <a :href=\"'companyView?id=' + item.cid\">\r\n                    <h4 class=\"title\">{{ item.comname }}</h4>\r\n                  </a>\r\n                </div>\r\n                <!-- post card details -->\r\n                <div class=\"blog-post__body--meta\">\r\n                  <span class=\"date\"><span class=\"meta-icon\"><i class=\"far fa-calendar-minus\"></i></span>{{ item.addtime\r\n                    }}\r\n                  </span>\r\n                </div>\r\n                <!-- post card content -->\r\n                <div class=\"blog-post__body--content\">\r\n                  <p>\r\n                    {{\r\n                    item.introduction.length > 50\r\n                    ? item.introduction.substring(0, 50) + '...'\r\n                    : item.introduction\r\n                    }}\r\n                  </p>\r\n                </div>\r\n                <!-- post card read more button -->\r\n                <div class=\"blog-post__body--btn\">\r\n                  <a :href=\"'companyView?id=' + item.cid\" class>Read more<span><i\r\n                        class=\"fa fa-chevron-right\"></i></span></a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n  import Swiper from 'swiper';\r\n  import 'swiper/dist/css/swiper.min.css';\r\n  import 'swiper/dist/js/swiper.min';\r\n\r\n  export default {\r\n    name: 'Default',\r\n    data() {\r\n      return {\r\n        banner1: require('@/assets/img/1.jpg'),\r\n        banner2: require('@/assets/img/2.jpg'),\r\n        banner3: require('@/assets/img/3.jpg'),\r\n        list1: '',\r\n        list2: '',\r\n        list3: '',\r\n      };\r\n    },\r\n    mounted() {\r\n      new Swiper('.swiper-container', {\r\n        slidesPerView: 1,\r\n        spaceBetween: 0,\r\n        loop: true,\r\n        autoplay: 3000,\r\n      });\r\n    },\r\n    created() {\r\n      this.getlist1();\r\n      this.getlist2();\r\n      this.getlist3();\r\n    },\r\n    methods: {\r\n      // 获取推荐职位\r\n      getlist1() {\r\n        const lname = sessionStorage.getItem('lname');\r\n        let url = base + '/positions/recommend';\r\n\r\n        let para = {\r\n          by1: lname,\r\n          pflag2: \"审核通过\",\r\n        };\r\n\r\n        request.post(url, para).then((res) => {\r\n          if (res.code === 200) {\r\n            this.list1 = res.resdata;\r\n          } else {\r\n            // 如果获取推荐失败,使用原来的方式获取职位\r\n            let para = {\r\n              pflag: '开放',\r\n            };\r\n            url = base + '/positions/list?currentPage=1&pageSize=9';\r\n            request.post(url, para).then((res) => {\r\n              this.list1 = res.resdata;\r\n            });\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取最新职位\r\n      getlist2() {\r\n        let para = {\r\n          pflag: '开放', // 只获取开放状态的职位\r\n          pflag2: \"审核通过\",\r\n        };\r\n        let url = base + '/positions/list?currentPage=1&pageSize=9';\r\n        request.post(url, para).then((res) => {\r\n          this.list2 = res.resdata;\r\n        });\r\n      },\r\n\r\n      // 获取最新企业\r\n      getlist3() {\r\n        let para = {\r\n          cflag: \"审核通过\",\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/company/list?currentPage=1&pageSize=6';\r\n        request.post(url, para).then((res) => {\r\n          this.list3 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .job-info {\r\n    text-align: left;\r\n    padding: 10px 0;\r\n  }\r\n\r\n  .job-info p {\r\n    margin: 5px 0;\r\n    color: #666;\r\n  }\r\n\r\n  .job-info i {\r\n    margin-right: 5px;\r\n    color: #3bc0c3;\r\n  }\r\n\r\n  .icon-box {\r\n    background: #fff;\r\n    padding: 20px;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .icon-box:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n  }\r\n\r\n  .heading-title {\r\n    color: #333;\r\n    font-size: 18px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .icon-box__btn a {\r\n    color: #3bc0c3;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .icon-box__btn a:hover {\r\n    color: #2a8f91;\r\n  }\r\n\r\n  .salary {\r\n    color: #ff4d4f;\r\n    font-weight: bold;\r\n  }\r\n</style>"]}]}