{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Leftnav.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Leftnav.vue", "mtime": 1741536471000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uL3V0aWxzL2h0dHAiOwppbXBvcnQgTGVmdCBmcm9tICIuLi8uLi9jb21wb25lbnRzL0xlZnQiOwppbXBvcnQgVG9wTWVudSBmcm9tICIuLi8uLi9jb21wb25lbnRzL1RvcE1lbnUiOwppbXBvcnQgRm9vdCBmcm9tICIuLi8uLi9jb21wb25lbnRzL0Zvb3QiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkxlZnRuYXYiLAogIGNvbXBvbmVudHM6IHsKICAgIExlZnQsCiAgICBUb3BNZW51LAogICAgRm9vdAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9LAogIG1vdW50ZWQoKSB7fSwKICBjcmVhdGVkKCkge30sCiAgbWV0aG9kczoge30KfTs="}, {"version": 3, "names": ["request", "base", "Left", "TopMenu", "Foot", "name", "components", "data", "mounted", "created", "methods"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Leftnav.vue"], "sourcesContent": ["<template>\r\n\t<TopMenu />\r\n\r\n\r\n\t<div class=\"custom-breadcrumb custom-breadcrumb--bg\"\r\n\t\t:style=\"{ backgroundImage: 'url(' + require('@/assets/images/breadcrumb-bg.jpg') + ')' }\">\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"row\">\r\n\t\t\t\t<!-- page title -->\r\n\r\n\t\t\t\t<div class=\"col-md-6\">\r\n\t\t\t\t\t<h1 class=\"page-title\">\r\n\t\t\t\t\t\t{{ $route.meta.title }}\r\n\t\t\t\t\t</h1>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- breadcrumb -->\r\n\t\t\t\t<div class=\"col-md-6 breadcrumb-menu\">\r\n\t\t\t\t\t<nav aria-label=\"breadcrumb\">\r\n\t\t\t\t\t\t<ol class=\"breadcrumb\">\r\n\t\t\t\t\t\t\t<li class=\"breadcrumb-item\">\r\n\t\t\t\t\t\t\t\t<a href=\"/index\">\r\n\t\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"fas fa-home\">\r\n\t\t\t\t\t\t\t\t\t\t</i>\r\n\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t网站首页\r\n\t\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t<li class=\"breadcrumb-item active\" aria-current=\"page\">\r\n\t\t\t\t\t\t\t\t‌{{ $route.meta.title }}\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t</ol>\r\n\t\t\t\t\t</nav>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\r\n\r\n\t<div class=\"main-content pt-120\">\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"pb-120\">\r\n\t\t\t\t<!-- our team list -->\r\n\t\t\t\t<div class=\"row\">\r\n\r\n\r\n\t\t\t\t\t<div class=\"col-xxl-8 col-xl-8 col-lg-8\">\r\n\t\t\t\t\t\t<div style=\"line-height: 30px;\">\r\n\t\t\t\t\t\t\t<router-view />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<Left />\r\n\r\n\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<!-- call to action banner -->\r\n\t\t<!-- end call to action banner -->\r\n\t</div>\r\n\r\n\r\n\r\n\r\n\t<Foot />\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport Left from \"../../components/Left\";\r\nimport TopMenu from \"../../components/TopMenu\";\r\nimport Foot from \"../../components/Foot\";\r\n\r\nexport default {\r\n\tname: \"Leftnav\",\r\n\tcomponents: {\r\n\t\tLeft,\r\n\t\tTopMenu,\r\n\t\tFoot,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\r\n\t\t};\r\n\t},\r\n\tmounted() {\r\n\r\n\t},\r\n\tcreated() {\r\n\r\n\t},\r\n\tmethods: {\r\n\r\n\t},\r\n};\r\n</script>\r\n\r\n\r\n<style></style>\r\n"], "mappings": "AAoEA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,OAAOC,IAAG,MAAO,uBAAuB;AACxC,OAAOC,OAAM,MAAO,0BAA0B;AAC9C,OAAOC,IAAG,MAAO,uBAAuB;AAExC,eAAe;EACdC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE;IACXJ,IAAI;IACJC,OAAO;IACPC;EACD,CAAC;EACDG,IAAIA,CAAA,EAAG;IACN,OAAO,CAEP,CAAC;EACF,CAAC;EACDC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAO,EAAE,CAET;AACD,CAAC", "ignoreList": []}]}