{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Default.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Default.vue", "mtime": 1741615257013}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "Swiper", "name", "data", "banner1", "require", "banner2", "banner3", "list1", "list2", "list3", "mounted", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "loop", "autoplay", "created", "getlist1", "getlist2", "getlist3", "methods", "lname", "sessionStorage", "getItem", "url", "para", "by1", "pflag2", "post", "then", "res", "code", "resdata", "pflag", "cflag", "listLoading"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Default.vue"], "sourcesContent": ["<template>\r\n  <!--幻灯片大图开始-->\r\n  <div id=\"banner_main\">\r\n    <div id=\"banner\" class=\"banner\" style=\"height: 400px\">\r\n      <div class=\"swiper-container swiper-container1\">\r\n        <div class=\"swiper-wrapper\">\r\n          <div class=\"swiper-slide\">\r\n            <img style=\"width: 100%; height: 405px\" :src=\"banner1\" />\r\n          </div>\r\n          <div class=\"swiper-slide\">\r\n            <img style=\"width: 100%; height: 405px\" :src=\"banner2\" />\r\n          </div>\r\n          <div class=\"swiper-slide\">\r\n            <img style=\"width: 100%; height: 405px\" :src=\"banner3\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <!--幻灯片大图结束-->\r\n  <div class=\"main-content\">\r\n    <div class=\"container\">\r\n      <div class=\"pb-90\" style=\"padding-top: 5.625rem\">\r\n        <!-- main title -->\r\n        <div class=\"row\">\r\n          <div class=\"col-12\">\r\n            <div class=\"main-title\">\r\n              <h2 class=\"title\">推荐职位</h2>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- main title -->\r\n        <!-- services icon box -->\r\n        <div class=\"row\">\r\n          <div class=\"col-lg-4 col-md-6 wow fadeIn\" data-wow-delay=\"300ms\" v-for=\"(item, index) in list1\" :key=\"index\">\r\n            <div class=\"icon-box icon-box--border\">\r\n              <div class=\"icon-box__heading\">\r\n                <h3 class=\"heading-title\">{{ item.pname }}</h3>\r\n              </div>\r\n              <div class=\"icon-box__content\">\r\n                <div class=\"job-info\">\r\n                  <p><i class=\"el-icon-location\"></i> 工作地点：{{ item.wlocation }}</p>\r\n                  <p><i class=\"el-icon-user\"></i> 招聘人数：{{ item.rnumber }}</p>\r\n                  <p>\r\n                    <i class=\"el-icon-money\"></i> 薪资待遇：<span class=\"salary\">{{\r\n                      item.streatment\r\n                      }}</span>\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <div class=\"icon-box__btn\">\r\n                <a :href=\"'positionsView?id=' + item.pid\">查看详情<span><i class=\"fa fa-chevron-right\"></i></span></a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- end services icon box -->\r\n      </div>\r\n\r\n      <div class=\"pb-90\" style=\"padding-top: 5.625rem\">\r\n        <!-- main title -->\r\n        <div class=\"row\">\r\n          <div class=\"col-12\">\r\n            <div class=\"main-title\">\r\n              <h2 class=\"title\">最新职位</h2>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- main title -->\r\n        <!-- services icon box -->\r\n        <div class=\"row\">\r\n          <div class=\"col-lg-4 col-md-6 wow fadeIn\" data-wow-delay=\"300ms\" v-for=\"(item, index) in list2\" :key=\"index\">\r\n            <div class=\"icon-box icon-box--border\">\r\n              <div class=\"icon-box__heading\">\r\n                <h3 class=\"heading-title\">{{ item.pname }}</h3>\r\n              </div>\r\n              <div class=\"icon-box__content\">\r\n                <div class=\"job-info\">\r\n                  <p><i class=\"el-icon-location\"></i> 工作地点：{{ item.wlocation }}</p>\r\n                  <p><i class=\"el-icon-user\"></i> 招聘人数：{{ item.rnumber }}</p>\r\n                  <p>\r\n                    <i class=\"el-icon-money\"></i> 薪资待遇：<span class=\"salary\">{{\r\n                      item.streatment\r\n                      }}</span>\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <div class=\"icon-box__btn\">\r\n                <a :href=\"'positionsView?id=' + item.pid\">查看详情<span><i class=\"fa fa-chevron-right\"></i></span></a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- end services icon box -->\r\n      </div>\r\n      <div class=\"container pb-90\">\r\n        <!-- main title -->\r\n        <div class=\"row\">\r\n          <div class=\"col-12\">\r\n            <div class=\"main-title\">\r\n              <h2 class=\"title\">最新企业</h2>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- main title -->\r\n        <div class=\"row\">\r\n          <div class=\"col-lg-4 col-md-6 wow fadeInUp\" v-for=\"(item, index) in list3\" :key=\"index\">\r\n            <div class=\"blog-post blog-post--card\">\r\n              <!-- post card image -->\r\n              <div class=\"blog-post__img\">\r\n                <a :href=\"'companyView?id=' + item.cid\"><img\r\n                    :src=\"'http://localhost:8088/JobHuntingSystem/' + item.logo\"\r\n                    style=\"width: 340px; height: 230px\" /></a>\r\n              </div>\r\n              <!-- post card body -->\r\n              <div class=\"blog-post__body\">\r\n                <!-- post card title -->\r\n                <div class=\"blog-post__body--title\">\r\n                  <a :href=\"'companyView?id=' + item.cid\">\r\n                    <h4 class=\"title\">{{ item.comname }}</h4>\r\n                  </a>\r\n                </div>\r\n                <!-- post card details -->\r\n                <div class=\"blog-post__body--meta\">\r\n                  <span class=\"date\"><span class=\"meta-icon\"><i class=\"far fa-calendar-minus\"></i></span>{{ item.addtime\r\n                    }}\r\n                  </span>\r\n                </div>\r\n                <!-- post card content -->\r\n                <div class=\"blog-post__body--content\">\r\n                  <p>\r\n                    {{\r\n                    item.introduction.length > 50\r\n                    ? item.introduction.substring(0, 50) + '...'\r\n                    : item.introduction\r\n                    }}\r\n                  </p>\r\n                </div>\r\n                <!-- post card read more button -->\r\n                <div class=\"blog-post__body--btn\">\r\n                  <a :href=\"'companyView?id=' + item.cid\" class>Read more<span><i\r\n                        class=\"fa fa-chevron-right\"></i></span></a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n  import Swiper from 'swiper';\r\n  import 'swiper/dist/css/swiper.min.css';\r\n  import 'swiper/dist/js/swiper.min';\r\n\r\n  export default {\r\n    name: 'Default',\r\n    data() {\r\n      return {\r\n        banner1: require('@/assets/img/1.jpg'),\r\n        banner2: require('@/assets/img/2.jpg'),\r\n        banner3: require('@/assets/img/3.jpg'),\r\n        list1: '',\r\n        list2: '',\r\n        list3: '',\r\n      };\r\n    },\r\n    mounted() {\r\n      new Swiper('.swiper-container', {\r\n        slidesPerView: 1,\r\n        spaceBetween: 0,\r\n        loop: true,\r\n        autoplay: 3000,\r\n      });\r\n    },\r\n    created() {\r\n      this.getlist1();\r\n      this.getlist2();\r\n      this.getlist3();\r\n    },\r\n    methods: {\r\n      // 获取推荐职位\r\n      getlist1() {\r\n        const lname = sessionStorage.getItem('lname');\r\n        let url = base + '/positions/recommend';\r\n\r\n        let para = {\r\n          by1: lname,\r\n          pflag2: \"审核通过\",\r\n        };\r\n\r\n        request.post(url, para).then((res) => {\r\n          if (res.code === 200) {\r\n            this.list1 = res.resdata;\r\n          } else {\r\n            // 如果获取推荐失败,使用原来的方式获取职位\r\n            let para = {\r\n              pflag: '开放',\r\n            };\r\n            url = base + '/positions/list?currentPage=1&pageSize=9';\r\n            request.post(url, para).then((res) => {\r\n              this.list1 = res.resdata;\r\n            });\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取最新职位\r\n      getlist2() {\r\n        let para = {\r\n          pflag: '开放', // 只获取开放状态的职位\r\n          pflag2: \"审核通过\",\r\n        };\r\n        let url = base + '/positions/list?currentPage=1&pageSize=9';\r\n        request.post(url, para).then((res) => {\r\n          this.list2 = res.resdata;\r\n        });\r\n      },\r\n\r\n      // 获取最新企业\r\n      getlist3() {\r\n        let para = {\r\n          cflag: \"审核通过\",\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/company/list?currentPage=1&pageSize=6';\r\n        request.post(url, para).then((res) => {\r\n          this.list3 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .job-info {\r\n    text-align: left;\r\n    padding: 10px 0;\r\n  }\r\n\r\n  .job-info p {\r\n    margin: 5px 0;\r\n    color: #666;\r\n  }\r\n\r\n  .job-info i {\r\n    margin-right: 5px;\r\n    color: #3bc0c3;\r\n  }\r\n\r\n  .icon-box {\r\n    background: #fff;\r\n    padding: 20px;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .icon-box:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n  }\r\n\r\n  .heading-title {\r\n    color: #333;\r\n    font-size: 18px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .icon-box__btn a {\r\n    color: #3bc0c3;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .icon-box__btn a:hover {\r\n    color: #2a8f91;\r\n  }\r\n\r\n  .salary {\r\n    color: #ff4d4f;\r\n    font-weight: bold;\r\n  }\r\n</style>"], "mappings": "AAwJE,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,OAAOC,MAAK,MAAO,QAAQ;AAC3B,OAAO,gCAAgC;AACvC,OAAO,2BAA2B;AAElC,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAEC,OAAO,CAAC,oBAAoB,CAAC;MACtCC,OAAO,EAAED,OAAO,CAAC,oBAAoB,CAAC;MACtCE,OAAO,EAAEF,OAAO,CAAC,oBAAoB,CAAC;MACtCG,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAIV,MAAM,CAAC,mBAAmB,EAAE;MAC9BW,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,CAAC;MACfC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE;IACP;IACAH,QAAQA,CAAA,EAAG;MACT,MAAMI,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;MAC7C,IAAIC,GAAE,GAAIxB,IAAG,GAAI,sBAAsB;MAEvC,IAAIyB,IAAG,GAAI;QACTC,GAAG,EAAEL,KAAK;QACVM,MAAM,EAAE;MACV,CAAC;MAED5B,OAAO,CAAC6B,IAAI,CAACJ,GAAG,EAAEC,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,IAAG,KAAM,GAAG,EAAE;UACpB,IAAI,CAACvB,KAAI,GAAIsB,GAAG,CAACE,OAAO;QAC1B,OAAO;UACL;UACA,IAAIP,IAAG,GAAI;YACTQ,KAAK,EAAE;UACT,CAAC;UACDT,GAAE,GAAIxB,IAAG,GAAI,0CAA0C;UACvDD,OAAO,CAAC6B,IAAI,CAACJ,GAAG,EAAEC,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;YACpC,IAAI,CAACtB,KAAI,GAAIsB,GAAG,CAACE,OAAO;UAC1B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAd,QAAQA,CAAA,EAAG;MACT,IAAIO,IAAG,GAAI;QACTQ,KAAK,EAAE,IAAI;QAAE;QACbN,MAAM,EAAE;MACV,CAAC;MACD,IAAIH,GAAE,GAAIxB,IAAG,GAAI,0CAA0C;MAC3DD,OAAO,CAAC6B,IAAI,CAACJ,GAAG,EAAEC,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACrB,KAAI,GAAIqB,GAAG,CAACE,OAAO;MAC1B,CAAC,CAAC;IACJ,CAAC;IAED;IACAb,QAAQA,CAAA,EAAG;MACT,IAAIM,IAAG,GAAI;QACTS,KAAK,EAAE;MACT,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIX,GAAE,GAAIxB,IAAG,GAAI,wCAAwC;MACzDD,OAAO,CAAC6B,IAAI,CAACJ,GAAG,EAAEC,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACpB,KAAI,GAAIoB,GAAG,CAACE,OAAO;QACxB,IAAI,CAACG,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}