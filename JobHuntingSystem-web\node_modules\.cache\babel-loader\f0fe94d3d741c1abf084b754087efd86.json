{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue?vue&type=template&id=9bcc0be2&scoped=true", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue", "mtime": 1741617283358}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIG5vcm1hbGl6ZUNsYXNzIGFzIF9ub3JtYWxpemVDbGFzcywgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IHsKICBjbGFzczogImNvbC1sZy0zIHNpZGViYXIgc2lkZWJhci0tbGVmdCIKfTsKY29uc3QgX2hvaXN0ZWRfMiA9IHsKICBjbGFzczogInNpZGViYXItY29udGFpbmVyIgp9Owpjb25zdCBfaG9pc3RlZF8zID0gewogIGNsYXNzOiAic2lkZWJhci1tZW51Igp9Owpjb25zdCBfaG9pc3RlZF80ID0gewogIGNsYXNzOiAibWVudS1ncm91cCIKfTsKY29uc3QgX2hvaXN0ZWRfNSA9IHsKICBjbGFzczogIm1lbnUtbGlzdCIKfTsKY29uc3QgX2hvaXN0ZWRfNiA9IHsKICBjbGFzczogIm1lbnUtZ3JvdXAiCn07CmNvbnN0IF9ob2lzdGVkXzcgPSB7CiAgY2xhc3M6ICJtZW51LWxpc3QiCn07CmNvbnN0IF9ob2lzdGVkXzggPSB7CiAgY2xhc3M6ICJtZW51LWdyb3VwIgp9Owpjb25zdCBfaG9pc3RlZF85ID0gewogIGNsYXNzOiAibWVudS1saXN0Igp9Owpjb25zdCBfaG9pc3RlZF8xMCA9IHsKICBjbGFzczogIm1lbnUtZ3JvdXAiCn07CmNvbnN0IF9ob2lzdGVkXzExID0gewogIGNsYXNzOiAibWVudS1saXN0Igp9Owpjb25zdCBfaG9pc3RlZF8xMiA9IHsKICBjbGFzczogIm1lbnUtZ3JvdXAiCn07CmNvbnN0IF9ob2lzdGVkXzEzID0gewogIGNsYXNzOiAibWVudS1saXN0Igp9Owpjb25zdCBfaG9pc3RlZF8xNCA9IHsKICBjbGFzczogIm1lbnUtaXRlbSIKfTsKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xLCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMiwgW19jYWNoZVsxM10gfHwgKF9jYWNoZVsxM10gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICBjbGFzczogInNpZGViYXItaGVhZGVyIgogIH0sIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJoMyIsIHsKICAgIGNsYXNzOiAic2lkZWJhci10aXRsZSIKICB9LCAi5Liq5Lq65Lit5b+DIiksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgIGNsYXNzOiAic2lkZWJhci1kaXZpZGVyIgogIH0pXSwgLTEgLyogSE9JU1RFRCAqLykpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8zLCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfNCwgW19jcmVhdGVFbGVtZW50Vk5vZGUoInVsIiwgX2hvaXN0ZWRfNSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImxpIiwgewogICAgY2xhc3M6IF9ub3JtYWxpemVDbGFzcyhbIm1lbnUtaXRlbSIsIHsKICAgICAgJ2FjdGl2ZSc6ICRvcHRpb25zLmlzQWN0aXZlKCcvU3dlY2xvbWUnKQogICAgfV0pCiAgfSwgX2NhY2hlWzFdIHx8IChfY2FjaGVbMV0gPSBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYSIsIHsKICAgIGhyZWY6ICIvU3dlY2xvbWUiCiAgfSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImkiLCB7CiAgICBjbGFzczogImZhcyBmYS1ob21lIgogIH0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgbnVsbCwgIuasoui/jumhtemdoiIpXSwgLTEgLyogSE9JU1RFRCAqLyldKSwgMiAvKiBDTEFTUyAqLyldKV0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF82LCBbX2NhY2hlWzRdIHx8IChfY2FjaGVbNF0gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICBjbGFzczogIm1lbnUtZ3JvdXAtdGl0bGUiCiAgfSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImkiLCB7CiAgICBjbGFzczogImZhcyBmYS1maWxlLWFsdCIKICB9KSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIG51bGwsICLnroDljobnrqHnkIYiKV0sIC0xIC8qIEhPSVNURUQgKi8pKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgidWwiLCBfaG9pc3RlZF83LCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgibGkiLCB7CiAgICBjbGFzczogX25vcm1hbGl6ZUNsYXNzKFsibWVudS1pdGVtIiwgewogICAgICAnYWN0aXZlJzogJG9wdGlvbnMuaXNBY3RpdmUoJy9yZXN1bWVfQWRkJykKICAgIH1dKQogIH0sIF9jYWNoZVsyXSB8fCAoX2NhY2hlWzJdID0gW19jcmVhdGVFbGVtZW50Vk5vZGUoImEiLCB7CiAgICBocmVmOiAiL3Jlc3VtZV9BZGQiCiAgfSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImkiLCB7CiAgICBjbGFzczogImZhcyBmYS1wbHVzLWNpcmNsZSIKICB9KSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIG51bGwsICLliJvlu7rnroDljoYiKV0sIC0xIC8qIEhPSVNURUQgKi8pXSksIDIgLyogQ0xBU1MgKi8pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJsaSIsIHsKICAgIGNsYXNzOiBfbm9ybWFsaXplQ2xhc3MoWyJtZW51LWl0ZW0iLCB7CiAgICAgICdhY3RpdmUnOiAkb3B0aW9ucy5pc0FjdGl2ZSgnL3Jlc3VtZV9tYW5hZ2UnKQogICAgfV0pCiAgfSwgX2NhY2hlWzNdIHx8IChfY2FjaGVbM10gPSBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYSIsIHsKICAgIGhyZWY6ICIvcmVzdW1lX21hbmFnZSIKICB9LCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaSIsIHsKICAgIGNsYXNzOiAiZmFzIGZhLXRhc2tzIgogIH0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgbnVsbCwgIueuoeeQhueugOWOhiIpXSwgLTEgLyogSE9JU1RFRCAqLyldKSwgMiAvKiBDTEFTUyAqLyldKV0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF84LCBbX2NhY2hlWzddIHx8IChfY2FjaGVbN10gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICBjbGFzczogIm1lbnUtZ3JvdXAtdGl0bGUiCiAgfSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImkiLCB7CiAgICBjbGFzczogImZhcyBmYS1oaXN0b3J5IgogIH0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgbnVsbCwgIuiusOW9leafpeivoiIpXSwgLTEgLyogSE9JU1RFRCAqLykpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJ1bCIsIF9ob2lzdGVkXzksIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJsaSIsIHsKICAgIGNsYXNzOiBfbm9ybWFsaXplQ2xhc3MoWyJtZW51LWl0ZW0iLCB7CiAgICAgICdhY3RpdmUnOiAkb3B0aW9ucy5pc0FjdGl2ZSgnL3Jlc3VtZWRlbGl2ZXJ5X21hbmFnZScpCiAgICB9XSkKICB9LCBfY2FjaGVbNV0gfHwgKF9jYWNoZVs1XSA9IFtfY3JlYXRlRWxlbWVudFZOb2RlKCJhIiwgewogICAgaHJlZjogIi9yZXN1bWVkZWxpdmVyeV9tYW5hZ2UiCiAgfSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImkiLCB7CiAgICBjbGFzczogImZhcyBmYS1wYXBlci1wbGFuZSIKICB9KSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIG51bGwsICLmiJHnmoTmipXpgJLorrDlvZUiKV0sIC0xIC8qIEhPSVNURUQgKi8pXSksIDIgLyogQ0xBU1MgKi8pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJsaSIsIHsKICAgIGNsYXNzOiBfbm9ybWFsaXplQ2xhc3MoWyJtZW51LWl0ZW0iLCB7CiAgICAgICdhY3RpdmUnOiAkb3B0aW9ucy5pc0FjdGl2ZSgnL2Jyb3dzaW5ncmVjb3Jkc19tYW5hZ2UnKQogICAgfV0pCiAgfSwgX2NhY2hlWzZdIHx8IChfY2FjaGVbNl0gPSBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYSIsIHsKICAgIGhyZWY6ICIvYnJvd3NpbmdyZWNvcmRzX21hbmFnZSIKICB9LCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaSIsIHsKICAgIGNsYXNzOiAiZmFzIGZhLWV5ZSIKICB9KSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIG51bGwsICLmiJHnmoTmtY/op4jorrDlvZUiKV0sIC0xIC8qIEhPSVNURUQgKi8pXSksIDIgLyogQ0xBU1MgKi8pXSldKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMTAsIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJ1bCIsIF9ob2lzdGVkXzExLCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgibGkiLCB7CiAgICBjbGFzczogX25vcm1hbGl6ZUNsYXNzKFsibWVudS1pdGVtIiwgewogICAgICAnYWN0aXZlJzogJG9wdGlvbnMuaXNBY3RpdmUoJy9iYnNfbWFuYWdlJykKICAgIH1dKQogIH0sIF9jYWNoZVs4XSB8fCAoX2NhY2hlWzhdID0gW19jcmVhdGVFbGVtZW50Vk5vZGUoImEiLCB7CiAgICBocmVmOiAiL2Jic19tYW5hZ2UiCiAgfSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImkiLCB7CiAgICBjbGFzczogImZhcyBmYS1jb21tZW50cyIKICB9KSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIG51bGwsICLmiJHnmoTluJblrZAiKV0sIC0xIC8qIEhPSVNURUQgKi8pXSksIDIgLyogQ0xBU1MgKi8pXSldKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMTIsIFtfY2FjaGVbMTJdIHx8IChfY2FjaGVbMTJdID0gX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgY2xhc3M6ICJtZW51LWdyb3VwLXRpdGxlIgogIH0sIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJpIiwgewogICAgY2xhc3M6ICJmYXMgZmEtdXNlci1jb2ciCiAgfSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoInNwYW4iLCBudWxsLCAi6LSm5oi36K6+572uIildLCAtMSAvKiBIT0lTVEVEICovKSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoInVsIiwgX2hvaXN0ZWRfMTMsIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJsaSIsIHsKICAgIGNsYXNzOiBfbm9ybWFsaXplQ2xhc3MoWyJtZW51LWl0ZW0iLCB7CiAgICAgICdhY3RpdmUnOiAkb3B0aW9ucy5pc0FjdGl2ZSgnL1NpbmZvJykKICAgIH1dKQogIH0sIF9jYWNoZVs5XSB8fCAoX2NhY2hlWzldID0gW19jcmVhdGVFbGVtZW50Vk5vZGUoImEiLCB7CiAgICBocmVmOiAiL1NpbmZvIgogIH0sIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJpIiwgewogICAgY2xhc3M6ICJmYXMgZmEtdXNlci1lZGl0IgogIH0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgbnVsbCwgIuS/ruaUueS4quS6uuS/oeaBryIpXSwgLTEgLyogSE9JU1RFRCAqLyldKSwgMiAvKiBDTEFTUyAqLyksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImxpIiwgewogICAgY2xhc3M6IF9ub3JtYWxpemVDbGFzcyhbIm1lbnUtaXRlbSIsIHsKICAgICAgJ2FjdGl2ZSc6ICRvcHRpb25zLmlzQWN0aXZlKCcvU3Bhc3N3b3JkJykKICAgIH1dKQogIH0sIF9jYWNoZVsxMF0gfHwgKF9jYWNoZVsxMF0gPSBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYSIsIHsKICAgIGhyZWY6ICIvU3Bhc3N3b3JkIgogIH0sIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJpIiwgewogICAgY2xhc3M6ICJmYXMgZmEtbG9jayIKICB9KSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIG51bGwsICLkv67mlLnlr4bnoIEiKV0sIC0xIC8qIEhPSVNURUQgKi8pXSksIDIgLyogQ0xBU1MgKi8pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJsaSIsIF9ob2lzdGVkXzE0LCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYSIsIHsKICAgIG9uQ2xpY2s6IF9jYWNoZVswXSB8fCAoX2NhY2hlWzBdID0gJGV2ZW50ID0+ICRvcHRpb25zLnF1aXQoKSksCiAgICBzdHlsZTogewogICAgICAiY3Vyc29yIjogInBvaW50ZXIiCiAgICB9CiAgfSwgX2NhY2hlWzExXSB8fCAoX2NhY2hlWzExXSA9IFtfY3JlYXRlRWxlbWVudFZOb2RlKCJpIiwgewogICAgY2xhc3M6ICJmYXMgZmEtc2lnbi1vdXQtYWx0IgogIH0sIG51bGwsIC0xIC8qIEhPSVNURUQgKi8pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgbnVsbCwgIumAgOWHuueZu+W9lSIsIC0xIC8qIEhPSVNURUQgKi8pXSkpXSldKV0pXSldKV0pOwp9"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_normalizeClass", "$options", "isActive", "href", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "onClick", "_cache", "$event", "quit", "style"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue"], "sourcesContent": ["<template>\n    <div class=\"col-lg-3 sidebar sidebar--left\">\n  <div class=\"sidebar-container\">\n    <div class=\"sidebar-header\">\n      <h3 class=\"sidebar-title\">个人中心</h3>\n      <div class=\"sidebar-divider\"></div>\n    </div>\n\n    <div class=\"sidebar-menu\">\n      <div class=\"menu-group\">\n     \n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Sweclome') }\">\n            <a href=\"/Sweclome\">\n              <i class=\"fas fa-home\"></i>\n              <span>欢迎页面</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-file-alt\"></i>\n          <span>简历管理</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resume_Add') }\">\n            <a href=\"/resume_Add\">\n              <i class=\"fas fa-plus-circle\"></i>\n              <span>创建简历</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resume_manage') }\">\n            <a href=\"/resume_manage\">\n              <i class=\"fas fa-tasks\"></i>\n              <span>管理简历</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-history\"></i>\n          <span>记录查询</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resumedelivery_manage') }\">\n            <a href=\"/resumedelivery_manage\">\n              <i class=\"fas fa-paper-plane\"></i>\n              <span>我的投递记录</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/browsingrecords_manage') }\">\n            <a href=\"/browsingrecords_manage\">\n              <i class=\"fas fa-eye\"></i>\n              <span>我的浏览记录</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">  \n      \n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/bbs_manage') }\">\n            <a href=\"/bbs_manage\">\n              <i class=\"fas fa-comments\"></i>\n              <span>我的帖子</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-user-cog\"></i>\n          <span>账户设置</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Sinfo') }\">\n            <a href=\"/Sinfo\">\n              <i class=\"fas fa-user-edit\"></i>\n              <span>修改个人信息</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Spassword') }\">\n            <a href=\"/Spassword\">\n              <i class=\"fas fa-lock\"></i>\n              <span>修改密码</span>\n            </a>\n          </li>\n          <li class=\"menu-item\">\n            <a @click=\"quit()\" style=\"cursor: pointer\">\n              <i class=\"fas fa-sign-out-alt\"></i>\n              <span>退出登录</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n    </div>\n  </div>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: 'Menu',\n    data() {\n      return {\n        currentPath: ''\n      };\n    },\n    mounted() {\n      //判断是否登录\n      var lname = sessionStorage.getItem('lname');\n      if (lname == null) {\n        //弹出提示\n        this.$message({\n          message: '请先登录',\n          type: 'warning',\n          offset: 320,\n        });\n        //跳转到登录页面\n        this.$router.push('/slogin');\n      }\n\n      // 获取当前路径\n      this.currentPath = window.location.pathname;\n    },\n    methods: {\n      quit() {\n        var _this = this;\n        this.$confirm('确认退出吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            sessionStorage.removeItem('lname');\n            _this.$router.push('/slogin');\n          })\n          .catch(() => { });\n      },\n      isActive(path) {\n        return this.currentPath === path;\n      }\n    },\n  };\n</script>\n\n<style scoped>\n  .sidebar-container {\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n    overflow: hidden;\n  }\n\n  .sidebar-header {\n    padding: 20px;\n    background-color: #3498db;\n    color: #fff;\n  }\n\n  .sidebar-title {\n    margin: 0;\n    font-size: 20px;\n    font-weight: 600;\n  }\n\n  .sidebar-divider {\n    height: 3px;\n    width: 50px;\n    background-color: rgba(255, 255, 255, 0.5);\n    margin-top: 10px;\n    border-radius: 1.5px;\n  }\n\n  .sidebar-menu {\n    padding: 15px 0;\n  }\n\n  .menu-group {\n    margin-bottom: 15px;\n  }\n\n  .menu-group-title {\n    padding: 10px 20px;\n    color: #7f8c8d;\n    font-size: 14px;\n    font-weight: 600;\n    text-transform: uppercase;\n    display: flex;\n    align-items: center;\n  }\n\n  .menu-group-title i {\n    margin-right: 8px;\n    font-size: 16px;\n  }\n\n  .menu-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .menu-item {\n    position: relative;\n  }\n\n  .menu-item a {\n    display: flex;\n    align-items: center;\n    padding: 12px 20px;\n    color: #34495e;\n    transition: all 0.3s ease;\n    text-decoration: none;\n  }\n\n  .menu-item a i {\n    margin-right: 10px;\n    width: 20px;\n    text-align: center;\n    font-size: 16px;\n    color: #7f8c8d;\n    transition: all 0.3s ease;\n  }\n\n  .menu-item a:hover {\n    background-color: #f8f9fa;\n    color: #3498db;\n  }\n\n  .menu-item a:hover i {\n    color: #3498db;\n  }\n\n  .menu-item.active a {\n    background-color: #ebf5fb;\n    color: #3498db;\n    font-weight: 600;\n  }\n\n  .menu-item.active a i {\n    color: #3498db;\n  }\n\n  .menu-item.active::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 0;\n    height: 100%;\n    width: 4px;\n    background-color: #3498db;\n    border-radius: 0 2px 2px 0;\n  }\n\n  /* 响应式设计 */\n  @media (max-width: 992px) {\n    .sidebar-container {\n      margin-bottom: 30px;\n    }\n  }\n</style>"], "mappings": ";;EACSA,KAAK,EAAC;AAAgC;;EACxCA,KAAK,EAAC;AAAmB;;EAMvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAEjBA,KAAK,EAAC;AAAW;;EAUlBA,KAAK,EAAC;AAAY;;EAKjBA,KAAK,EAAC;AAAW;;EAgBlBA,KAAK,EAAC;AAAY;;EAKjBA,KAAK,EAAC;AAAW;;EAgBlBA,KAAK,EAAC;AAAY;;EAEjBA,KAAK,EAAC;AAAW;;EASlBA,KAAK,EAAC;AAAY;;EAKjBA,KAAK,EAAC;AAAW;;EAafA,KAAK,EAAC;AAAW;;uBA3F3BC,mBAAA,CAqGI,OArGJC,UAqGI,GApGNC,mBAAA,CAmGM,OAnGNC,UAmGM,G,4BAlGJD,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAgB,IACzBG,mBAAA,CAAmC;IAA/BH,KAAK,EAAC;EAAe,GAAC,MAAI,GAC9BG,mBAAA,CAAmC;IAA9BH,KAAK,EAAC;EAAiB,G,sBAG9BG,mBAAA,CA4FM,OA5FNE,UA4FM,GA3FJF,mBAAA,CAUM,OAVNG,UAUM,GARJH,mBAAA,CAOK,MAPLI,UAOK,GANHJ,mBAAA,CAKK;IALDH,KAAK,EAAAQ,eAAA,EAAC,WAAW;MAAA,UAAqBC,QAAA,CAAAC,QAAQ;IAAA;gCAChDP,mBAAA,CAGI;IAHDQ,IAAI,EAAC;EAAW,IACjBR,mBAAA,CAA2B;IAAxBH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAiB,cAAX,MAAI,E,2CAMlBA,mBAAA,CAmBM,OAnBNS,UAmBM,G,0BAlBJT,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAkB,IAC3BG,mBAAA,CAA+B;IAA5BH,KAAK,EAAC;EAAiB,IAC1BG,mBAAA,CAAiB,cAAX,MAAI,E,sBAEZA,mBAAA,CAaK,MAbLU,UAaK,GAZHV,mBAAA,CAKK;IALDH,KAAK,EAAAQ,eAAA,EAAC,WAAW;MAAA,UAAqBC,QAAA,CAAAC,QAAQ;IAAA;gCAChDP,mBAAA,CAGI;IAHDQ,IAAI,EAAC;EAAa,IACnBR,mBAAA,CAAkC;IAA/BH,KAAK,EAAC;EAAoB,IAC7BG,mBAAA,CAAiB,cAAX,MAAI,E,uCAGdA,mBAAA,CAKK;IALDH,KAAK,EAAAQ,eAAA,EAAC,WAAW;MAAA,UAAqBC,QAAA,CAAAC,QAAQ;IAAA;gCAChDP,mBAAA,CAGI;IAHDQ,IAAI,EAAC;EAAgB,IACtBR,mBAAA,CAA4B;IAAzBH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAAiB,cAAX,MAAI,E,2CAMlBA,mBAAA,CAmBM,OAnBNW,UAmBM,G,0BAlBJX,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAkB,IAC3BG,mBAAA,CAA8B;IAA3BH,KAAK,EAAC;EAAgB,IACzBG,mBAAA,CAAiB,cAAX,MAAI,E,sBAEZA,mBAAA,CAaK,MAbLY,UAaK,GAZHZ,mBAAA,CAKK;IALDH,KAAK,EAAAQ,eAAA,EAAC,WAAW;MAAA,UAAqBC,QAAA,CAAAC,QAAQ;IAAA;gCAChDP,mBAAA,CAGI;IAHDQ,IAAI,EAAC;EAAwB,IAC9BR,mBAAA,CAAkC;IAA/BH,KAAK,EAAC;EAAoB,IAC7BG,mBAAA,CAAmB,cAAb,QAAM,E,uCAGhBA,mBAAA,CAKK;IALDH,KAAK,EAAAQ,eAAA,EAAC,WAAW;MAAA,UAAqBC,QAAA,CAAAC,QAAQ;IAAA;gCAChDP,mBAAA,CAGI;IAHDQ,IAAI,EAAC;EAAyB,IAC/BR,mBAAA,CAA0B;IAAvBH,KAAK,EAAC;EAAY,IACrBG,mBAAA,CAAmB,cAAb,QAAM,E,2CAMpBA,mBAAA,CAUM,OAVNa,WAUM,GARJb,mBAAA,CAOK,MAPLc,WAOK,GANHd,mBAAA,CAKK;IALDH,KAAK,EAAAQ,eAAA,EAAC,WAAW;MAAA,UAAqBC,QAAA,CAAAC,QAAQ;IAAA;gCAChDP,mBAAA,CAGI;IAHDQ,IAAI,EAAC;EAAa,IACnBR,mBAAA,CAA+B;IAA5BH,KAAK,EAAC;EAAiB,IAC1BG,mBAAA,CAAiB,cAAX,MAAI,E,2CAKlBA,mBAAA,CAyBM,OAzBNe,WAyBM,G,4BAxBJf,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAkB,IAC3BG,mBAAA,CAA+B;IAA5BH,KAAK,EAAC;EAAiB,IAC1BG,mBAAA,CAAiB,cAAX,MAAI,E,sBAEZA,mBAAA,CAmBK,MAnBLgB,WAmBK,GAlBHhB,mBAAA,CAKK;IALDH,KAAK,EAAAQ,eAAA,EAAC,WAAW;MAAA,UAAqBC,QAAA,CAAAC,QAAQ;IAAA;gCAChDP,mBAAA,CAGI;IAHDQ,IAAI,EAAC;EAAQ,IACdR,mBAAA,CAAgC;IAA7BH,KAAK,EAAC;EAAkB,IAC3BG,mBAAA,CAAmB,cAAb,QAAM,E,uCAGhBA,mBAAA,CAKK;IALDH,KAAK,EAAAQ,eAAA,EAAC,WAAW;MAAA,UAAqBC,QAAA,CAAAC,QAAQ;IAAA;kCAChDP,mBAAA,CAGI;IAHDQ,IAAI,EAAC;EAAY,IAClBR,mBAAA,CAA2B;IAAxBH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAiB,cAAX,MAAI,E,uCAGdA,mBAAA,CAKK,MALLiB,WAKK,GAJHjB,mBAAA,CAGI;IAHAkB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEd,QAAA,CAAAe,IAAI;IAAIC,KAAuB,EAAvB;MAAA;IAAA;kCACjBtB,mBAAA,CAAmC;IAAhCH,KAAK,EAAC;EAAqB,4BAC9BG,mBAAA,CAAiB,cAAX,MAAI,oB", "ignoreList": []}]}