{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue?vue&type=style&index=0&id=26cae6d4&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue", "mtime": 1741617188960}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue"], "names": [], "mappings": ";EAoIE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACd;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACjB", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/ResumePreview.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"resume-preview\">\r\n    <div class=\"resume-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"avatar-section\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" class=\"avatar\" />\r\n        </div>\r\n        <div class=\"basic-info\">\r\n          <h1 class=\"name\">{{ formData.sname }}</h1>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>性别：{{ formData.gender }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-date\"></i>\r\n              <span>年龄：{{ formData.age }}岁</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-phone\"></i>\r\n              <span>电话：{{ formData.phone }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-school\"></i>\r\n              <span>专业：{{ professionalName }}</span>\r\n            </div>\r\n          \r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"resume-body\">\r\n      <div class=\"resume-section\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          简历信息\r\n        </h2>\r\n        <div class=\"resume-content\" v-if=\"resumeData\">\r\n          <div class=\"info-row\">\r\n            <label>教育背景：</label>\r\n            <div class=\"content\" v-html=\"resumeData.education\"></div>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <label>实习经历：</label>\r\n            <div class=\"content\" v-html=\"resumeData.parttimejob\"></div>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <label>个人介绍：</label>\r\n            <div class=\"content\" v-html=\"resumeData.introduction\"></div>\r\n          </div>\r\n        </div>\r\n        <div class=\"no-resume\" v-else>\r\n          <el-empty description=\"暂无简历信息\">\r\n            <el-button type=\"primary\" @click=\"goToAddResume\">创建简历</el-button>\r\n          </el-empty>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    name: 'ResumePreview',\r\n    data() {\r\n      return {\r\n        formData: {},\r\n        resumeData: null,\r\n        professionalsList: [],\r\n        professionalName: '',\r\n      };\r\n    },\r\n    created() {\r\n      this.getUserInfo();\r\n      this.getProfessionals();\r\n      this.getResumeInfo();\r\n    },\r\n    methods: {\r\n      // 获取用户信息\r\n      getUserInfo() {\r\n        let lname = sessionStorage.getItem('lname');\r\n        let url = base + '/students/get?id=' + lname;\r\n        request.post(url).then((res) => {\r\n          if (res.code == 200) {\r\n            this.formData = res.resdata;\r\n            this.getProfessionalName();\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取专业列表\r\n      getProfessionals() {\r\n        let url = base + '/professionals/list';\r\n        request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n          if (res.code == 200) {\r\n            this.professionalsList = res.resdata;\r\n            this.getProfessionalName();\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取专业名称\r\n      getProfessionalName() {\r\n        if (this.formData.proid && this.professionalsList.length > 0) {\r\n          const professional = this.professionalsList.find((p) => p.proid === this.formData.proid);\r\n          this.professionalName = professional ? professional.proname : '';\r\n        }\r\n      },\r\n\r\n      // 获取简历信息\r\n      getResumeInfo() {\r\n        let lname = sessionStorage.getItem('lname');\r\n        let url = base + '/resume/list';\r\n        request.post(url, { sno: lname }, { params: { currentPage: 1, pageSize: 1 } }).then((res) => {\r\n          if (res.code == 200 && res.resdata.length > 0) {\r\n            this.resumeData = res.resdata[0];\r\n          }\r\n        });\r\n      },\r\n\r\n      // 跳转到创建简历页面\r\n      goToAddResume() {\r\n        this.$router.push('/Resume_Add');\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .resume-preview {\r\n    max-width: 1000px;\r\n    margin: 20px auto;\r\n    background: #fff;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    border-radius: 8px;\r\n  }\r\n\r\n  .resume-header {\r\n    background: linear-gradient(135deg, #3bc0c3 0%, #42d3d7 100%);\r\n    padding: 40px;\r\n    border-radius: 8px 8px 0 0;\r\n    color: #fff;\r\n  }\r\n\r\n  .header-content {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 40px;\r\n  }\r\n\r\n  .avatar-section {\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .avatar {\r\n    width: 150px;\r\n    height: 150px;\r\n    border-radius: 75px;\r\n    border: 4px solid rgba(255, 255, 255, 0.3);\r\n  }\r\n\r\n  .basic-info {\r\n    flex-grow: 1;\r\n  }\r\n\r\n  .name {\r\n    font-size: 28px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n    gap: 15px;\r\n  }\r\n\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .info-item i {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .resume-body {\r\n    padding: 40px;\r\n  }\r\n\r\n  .resume-section {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 20px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .section-title i {\r\n    color: #3bc0c3;\r\n  }\r\n\r\n  .info-row {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-row label {\r\n    font-weight: bold;\r\n    color: #666;\r\n    display: block;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .content {\r\n    line-height: 1.6;\r\n    color: #333;\r\n  }\r\n\r\n  .status-employed {\r\n    color: #67c23a;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .status-unemployed {\r\n    color: #f56c6c;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .no-resume {\r\n    text-align: center;\r\n    padding: 40px 0;\r\n  }\r\n</style>"]}]}