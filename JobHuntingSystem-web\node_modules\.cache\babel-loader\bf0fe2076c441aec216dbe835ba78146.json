{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sinfo.vue?vue&type=template&id=0acd2d48", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sinfo.vue", "mtime": 1741615257012}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "align", "_component_el_form_item", "label", "prop", "_component_el_input", "sname", "$event", "placeholder", "style", "_component_el_radio_group", "gender", "_component_el_radio", "_cache", "age", "phone", "_component_el_select", "proid", "size", "_createElementBlock", "_Fragment", "_renderList", "professionalsList", "item", "_createBlock", "_component_el_option", "key", "proname", "value", "_component_el_avatar", "src", "spic", "shape", "fit", "_ctx", "_component_el_button", "type", "onClick", "$options", "showUpload", "save", "loading", "btnLoading", "icon", "_component_el_dialog", "uploadVisible", "title", "onClose", "closeDialog", "_createElementVNode", "_component_el_upload", "action", "drag", "limit", "handlePreview", "handleRemove", "fileList", "handleExceed", "name", "fileListChange", "id", "_hoisted_1", "hideUpload", "handleConfirm"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sinfo.vue"], "sourcesContent": ["<template>\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\n    <el-form-item label=\"姓名\" prop=\"sname\">\n      <el-input v-model=\"formData.sname\" placeholder=\"姓名\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"性别\" prop=\"gender\">\n      <el-radio-group v-model=\"formData.gender\">\n        <el-radio label=\"男\"> 男 </el-radio>\n        <el-radio label=\"女\"> 女 </el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item label=\"年龄\" prop=\"age\">\n      <el-input v-model=\"formData.age\" placeholder=\"年龄\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"手机号码\" prop=\"phone\">\n      <el-input v-model=\"formData.phone\" placeholder=\"手机号码\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"专业\" prop=\"proid\">\n      <el-select v-model=\"formData.proid\" placeholder=\"请选择\" size=\"small\">\n        <el-option v-for=\"item in professionalsList\" :key=\"item.proid\" :label=\"item.proname\"\n          :value=\"item.proid\"></el-option>\n      </el-select>\n    </el-form-item>\n    <el-form-item prop=\"spic\" label=\"照片\" min-width=\"20%\">\n      <el-avatar :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" shape=\"square\" :size=\"100\"\n        :fit=\"fit\"></el-avatar>\n      <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n    </el-form-item>\n\n    <el-form-item>\n      <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">保 存</el-button>\n    </el-form-item>\n  </el-form>\n  <el-dialog v-model=\"uploadVisible\" title=\"附件上传\" custom-class=\"el-dialog-widthSmall\" @close=\"closeDialog\">\n    <div>\n      <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n    </div>\n    <el-upload action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n      style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\" drag :limit=\"1\"\n      :on-preview=\"handlePreview\" :on-remove=\"handleRemove\" :file-list=\"fileList\" :on-exceed=\"handleExceed\"\n      :auto-upload=\"false\" name=\"file\" :on-change=\"fileListChange\">\n      <i class=\"el-icon-upload\"></i>\n      <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n      <div class=\"el-upload__tip\">\n        <div style=\"display: inline; color: #d70000; font-size: 14px\" class=\"uploadFileWarning\" id=\"uploadFileWarning\">\n        </div>\n      </div>\n    </el-upload>\n    <span class=\"dialog-footer\">\n      <el-button @click=\"hideUpload\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n    </span>\n  </el-dialog>\n</template>\n<script>\n  import request, { base } from '../../../utils/http';\n  export default {\n    name: 'Sinfo',\n    data() {\n      return {\n        formData: {},\n        professionalsList: [], // 专业列表\n\n        rules: {\n          sno: [{ required: true, message: '请输入账号', trigger: 'blur' }],\n          sname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\n          gender: [{ required: true, message: '请输入性别', trigger: 'blur' }],\n          phone: [\n            { required: true, message: '请输入手机号码', trigger: 'blur' },\n            { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\n          ],\n          proid: [{ required: true, message: '请选择专业', trigger: 'onchange' }],\n          spic: [{ required: true, message: '请上传照片', trigger: 'blur' }],\n          sflag: [{ required: true, message: '请输入就业状态', trigger: 'blur' }],\n        },\n\n        btnLoading: false, //按钮是否在加载中\n        uploadVisible: false, //上传弹出框\n      };\n    },\n    created() {\n      this.getinfo();\n      this.getProfessionals();\n    },\n    methods: {\n      //得到用户信息\n      getinfo() {\n        var lname = sessionStorage.getItem('lname');\n        let url = base + '/students/get?id=' + lname; //请求地址\n        request.post(url).then((res) => {\n          //请求接口\n          if (res.code == 200) {\n            this.formData = res.resdata;\n          } else {\n            this.$message({\n              message: '服务器错误',\n              type: 'error',\n              offset: 320,\n            });\n          }\n        });\n      },\n\n      //注册\n      save() {\n        //表单验证\n        this.$refs['formDataRef'].validate((valid) => {\n          if (valid) {\n            var lname = sessionStorage.getItem('lname');\n            let url = base + '/students/update?sno=' + lname; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => {\n              //请求接口\n              if (res.code == 200) {\n                this.$message({\n                  message: '操作成功！',\n                  type: 'success',\n                  offset: 320,\n                });\n              } else {\n                this.$message({\n                  message: '服务器错误',\n                  type: 'error',\n                  offset: 320,\n                });\n              }\n            });\n          }\n        });\n      },\n      //显示上传框\n      showUpload() {\n        this.uploadVisible = true;\n      },\n\n      //隐藏上传框\n      hideUpload() {\n        this.uploadVisible = false;\n      },\n      //上传\n      handleRemove(file, fileList) {\n        this.fileList = fileList;\n      },\n      handlePreview(file) {\n        console.log(file);\n      },\n      handleExceed(files, fileList) {\n        this.$message({\n          duration: 1000,\n          message: '只能上传一个文件',\n          type: 'error',\n          offset: 320,\n        });\n      },\n      // 判断上传文件后缀\n      fileListChange(file, fileList) {\n        let extendFileName = 'png,jpg';\n        let extendFileNames = extendFileName.split(',');\n        let regExpRules = [];\n        for (let i = 0; i < extendFileNames.length; i++) {\n          regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n        }\n        let fileNames = [];\n        let files = [];\n        let that = this;\n        fileList.forEach(function (key, val) {\n          let ret = false;\n          for (let i = 0; i < regExpRules.length; i++) {\n            ret = ret || regExpRules[i].test(key['name']);\n          }\n          if (!ret) {\n            console.log(key['name'] + ':' + ret);\n            that.$message({\n              duration: 1000,\n              message: '上传的文件后缀必须为' + extendFileName + '格式！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          if (fileNames.indexOf(key['name']) != -1) {\n            that.$message({\n              duration: 1000,\n              message: '上传的文件重复！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          //只能上传一个文件，用最后上传的覆盖\n          if (!that.multiFiles) {\n            files = [];\n            fileNames = [];\n          }\n          files.push(key);\n          fileNames.push(key['name']);\n          if (fileNames !== '') {\n            // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n          }\n          //$(\".uploadFileWarning\").text(\"\");\n        });\n        this.files = fileNames;\n        this.fileList = files;\n      },\n      /**\n       * 确认按钮\n       */\n      handleConfirm() {\n        let filePath = this.fileList;\n        if (filePath.length === 0) {\n          this.$message({\n            duration: 1000,\n            message: '请选择文件！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        let formData = new FormData();\n        this.fileList.forEach((file) => {\n          formData.append('file', file.raw, file.raw.name);\n        });\n        let url = base + '/common/uploadFile';\n        console.log('url=' + url);\n        request.post(url, formData).then((res) => {\n          console.log(res);\n          let furl = res.resdata.filePath;\n          this.formData.spic = furl; // 上传文件的路径\n          this.hideUpload();\n          console.log(res);\n        });\n      },\n\n      // 获取专业列表\n      getProfessionals() {\n        let url = base + '/professionals/list';\n        request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\n          if (res.code == 200) {\n            this.professionalsList = res.resdata;\n          }\n        });\n      },\n    },\n  };\n</script>\n\n<style></style>"], "mappings": ";;EAgDUA,KAAK,EAAC;AAAe;;;;;;;;;;;;;6DA/C7BC,YAAA,CA+BUC,kBAAA;IA/BAC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAG,KAAK;IAAEC,KAAK,EAAC;;sBAClF,MAEe,CAFfP,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAAkF,CAAlFV,YAAA,CAAkFW,mBAAA;oBAA/DR,KAAA,CAAAC,QAAQ,CAACQ,KAAK;mEAAdT,KAAA,CAAAC,QAAQ,CAACQ,KAAK,GAAAC,MAAA;QAAEC,WAAW,EAAC,IAAI;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAEtDf,YAAA,CAKeQ,uBAAA;MALDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAGiB,CAHjBV,YAAA,CAGiBgB,yBAAA;oBAHQb,KAAA,CAAAC,QAAQ,CAACa,MAAM;mEAAfd,KAAA,CAAAC,QAAQ,CAACa,MAAM,GAAAJ,MAAA;;0BACtC,MAAkC,CAAlCb,YAAA,CAAkCkB,mBAAA;UAAxBT,KAAK,EAAC;QAAG;4BAAC,MAAGU,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;YACvBnB,YAAA,CAAkCkB,mBAAA;UAAxBT,KAAK,EAAC;QAAG;4BAAC,MAAGU,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;;;;QAG3BnB,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAAgF,CAAhFV,YAAA,CAAgFW,mBAAA;oBAA7DR,KAAA,CAAAC,QAAQ,CAACgB,GAAG;mEAAZjB,KAAA,CAAAC,QAAQ,CAACgB,GAAG,GAAAP,MAAA;QAAEC,WAAW,EAAC,IAAI;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAEpDf,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAoF,CAApFV,YAAA,CAAoFW,mBAAA;oBAAjER,KAAA,CAAAC,QAAQ,CAACiB,KAAK;mEAAdlB,KAAA,CAAAC,QAAQ,CAACiB,KAAK,GAAAR,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAExDf,YAAA,CAKeQ,uBAAA;MALDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAGY,CAHZV,YAAA,CAGYsB,oBAAA;oBAHQnB,KAAA,CAAAC,QAAQ,CAACmB,KAAK;mEAAdpB,KAAA,CAAAC,QAAQ,CAACmB,KAAK,GAAAV,MAAA;QAAEC,WAAW,EAAC,KAAK;QAACU,IAAI,EAAC;;0BAC9C,MAAiC,E,kBAA5CC,mBAAA,CACkCC,SAAA,QAAAC,WAAA,CADRxB,KAAA,CAAAyB,iBAAiB,EAAzBC,IAAI;+BAAtBC,YAAA,CACkCC,oBAAA;YADYC,GAAG,EAAEH,IAAI,CAACN,KAAK;YAAGd,KAAK,EAAEoB,IAAI,CAACI,OAAO;YAChFC,KAAK,EAAEL,IAAI,CAACN;;;;;;QAGnBvB,YAAA,CAIeQ,uBAAA;MAJDE,IAAI,EAAC,MAAM;MAACD,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC;;wBAC7C,MACyB,CADzBT,YAAA,CACyBmC,oBAAA;QADbC,GAAG,8CAA8CjC,KAAA,CAAAC,QAAQ,CAACiC,IAAI;QAAEC,KAAK,EAAC,QAAQ;QAAEd,IAAI,EAAE,GAAG;QAClGe,GAAG,EAAEC,IAAA,CAAAD;+CACRvC,YAAA,CAAyEyC,oBAAA;QAA9DC,IAAI,EAAC,SAAS;QAAClB,IAAI,EAAC,OAAO;QAAEmB,OAAK,EAAEC,QAAA,CAAAC;;0BAAY,MAAE1B,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;QAG/DnB,YAAA,CAEeQ,uBAAA;wBADb,MAAgH,CAAhHR,YAAA,CAAgHyC,oBAAA;QAArGC,IAAI,EAAC,SAAS;QAAClB,IAAI,EAAC,OAAO;QAAEmB,OAAK,EAAEC,QAAA,CAAAE,IAAI;QAAGC,OAAO,EAAE5C,KAAA,CAAA6C,UAAU;QAAEC,IAAI,EAAC;;0BAAiB,MAAG9B,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;;;;yCAGxGnB,YAAA,CAmBYkD,oBAAA;gBAnBQ/C,KAAA,CAAAgD,aAAa;+DAAbhD,KAAA,CAAAgD,aAAa,GAAAtC,MAAA;IAAEuC,KAAK,EAAC,MAAM;IAAC,cAAY,EAAC,sBAAsB;IAAEC,OAAK,EAAEb,IAAA,CAAAc;;sBAC1F,MAEM,C,4BAFNC,mBAAA,CAEM,cADJA,mBAAA,CAAkC,WAA/B,wBAA2B,E,sBAEhCvD,YAAA,CAUYwD,oBAAA;MAVDC,MAAM,EAAC,mDAAmD;MACnE1C,KAAqF,EAArF;QAAA;QAAA;QAAA;QAAA;MAAA,CAAqF;MAAC2C,IAAI,EAAJ,EAAI;MAAEC,KAAK,EAAE,CAAC;MACnG,YAAU,EAAEf,QAAA,CAAAgB,aAAa;MAAG,WAAS,EAAEhB,QAAA,CAAAiB,YAAY;MAAG,WAAS,EAAErB,IAAA,CAAAsB,QAAQ;MAAG,WAAS,EAAElB,QAAA,CAAAmB,YAAY;MACnG,aAAW,EAAE,KAAK;MAAEC,IAAI,EAAC,MAAM;MAAE,WAAS,EAAEpB,QAAA,CAAAqB;;wBAC7C,MAA8B9C,MAAA,SAAAA,MAAA,QAA9BoC,mBAAA,CAA8B;QAA3BxD,KAAK,EAAC;MAAgB,4BACzBwD,mBAAA,CAA2D;QAAtDxD,KAAK,EAAC;MAAiB,I,iBAAC,aAAW,GAAAwD,mBAAA,CAAa,YAAT,MAAI,E,qBAChDA,mBAAA,CAGM;QAHDxD,KAAK,EAAC;MAAgB,IACzBwD,mBAAA,CACM;QADDxC,KAAwD,EAAxD;UAAA;UAAA;UAAA;QAAA,CAAwD;QAAChB,KAAK,EAAC,mBAAmB;QAACmE,EAAE,EAAC;;;2FAI/FX,mBAAA,CAGO,QAHPY,UAGO,GAFLnE,YAAA,CAA8CyC,oBAAA;MAAlCE,OAAK,EAAEC,QAAA,CAAAwB;IAAU;wBAAE,MAAGjD,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;oCAClCnB,YAAA,CAAgEyC,oBAAA;MAArDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEC,QAAA,CAAAyB;;wBAAe,MAAGlD,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E", "ignoreList": []}]}