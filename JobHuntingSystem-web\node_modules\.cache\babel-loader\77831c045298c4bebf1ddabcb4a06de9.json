{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\store\\index.js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\store\\index.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlU3RvcmUgfSBmcm9tICd2dWV4JzsKZXhwb3J0IGRlZmF1bHQgY3JlYXRlU3RvcmUoewogIHN0YXRlOiB7CiAgICBjdXJyZW50TWVudTogMAogIH0sCiAgbXV0YXRpb25zOiB7CiAgICBzZXRTdGF0ZShzdGF0ZSwgbikgewogICAgICBzdGF0ZS5jdXJyZW50TWVudSA9IG47CiAgICB9CiAgfSwKICBhY3Rpb25zOiB7fSwKICBtb2R1bGVzOiB7fQp9KTs="}, {"version": 3, "names": ["createStore", "state", "currentMenu", "mutations", "setState", "n", "actions", "modules"], "sources": ["I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\n\nexport default createStore({\n  state: {\n    currentMenu:0\n  },\n  mutations: {\n    setState (state, n) {\n      state.currentMenu = n\n    }\n  },\n  actions: {\n  },\n  modules: {\n  }\n})\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,MAAM;AAElC,eAAeA,WAAW,CAAC;EACzBC,KAAK,EAAE;IACLC,WAAW,EAAC;EACd,CAAC;EACDC,SAAS,EAAE;IACTC,QAAQA,CAAEH,KAAK,EAAEI,CAAC,EAAE;MAClBJ,KAAK,CAACC,WAAW,GAAGG,CAAC;IACvB;EACF,CAAC;EACDC,OAAO,EAAE,CACT,CAAC;EACDC,OAAO,EAAE,CACT;AACF,CAAC,CAAC", "ignoreList": []}]}