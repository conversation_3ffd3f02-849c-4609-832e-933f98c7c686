{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\utils\\http.js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\utils\\http.js", "mtime": 1741618591488}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "base", "ZHIPUAI_API", "API_KEY", "request", "create", "timeout", "chatWithAI", "messages", "response", "method", "url", "headers", "data", "model", "role", "content", "temperature", "top_p", "max_tokens", "choices", "message", "error", "console", "interceptors", "use", "config", "Promise", "reject", "res", "responseType", "JSON", "parse"], "sources": ["I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/utils/http.js"], "sourcesContent": ["import axios from 'axios';\r\n//axios.defaults.withCredentials = true\r\nexport const base = 'http://127.0.0.1:8088/JobHuntingSystem/api';\r\n\r\n// 智谱AI配置\r\nconst ZHIPUAI_API = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';\r\nconst API_KEY = 'cdbe3f5e608394a1353e9ce27397f27b.yOqjVhOmozryAm3f';\r\n\r\nconst request = axios.create({\r\n    timeout: 5000\r\n})\r\n\r\n// AI聊天方法\r\nexport const chatWithAI = async (messages) => {\r\n    try {\r\n        const response = await axios({\r\n            method: 'post',\r\n            url: ZHIPUAI_API,\r\n            headers: {\r\n                'Authorization': `Bearer ${API_KEY}`,\r\n                'Content-Type': 'application/json'\r\n            },\r\n            data: {\r\n                model: \"glm-4\",\r\n                messages: [\r\n                    {\r\n                        role: \"system\",\r\n                        content: `你是一个专业的求职顾问，具有丰富的人力资源和职业发展经验。你需要：\r\n1. 提供专业、实用的求职建议和职业规划指导\r\n2. 帮助优化简历和求职信\r\n3. 分享面试技巧和行业洞察\r\n4. 根据用户的具体情况给出个性化建议\r\n5. 使用专业但友好的语气，让用户感到温暖和被重视\r\n6. 回答要条理清晰，适当使用markdown格式增加可读性\r\n7. 建议要具体且可执行，避免泛泛而谈\r\n8. 对于不确定的问题，坦诚承认并建议咨询专业人士\r\n9. 保持积极正面的态度，鼓励求职者`\r\n                    },\r\n                    ...messages\r\n                ],\r\n                temperature: 0.7,\r\n                top_p: 0.95,\r\n                max_tokens: 2000,\r\n            }\r\n        });\r\n\r\n        return response.data.choices[0].message.content;\r\n\r\n    } catch (error) {\r\n        console.error('AI对话失败:', error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n\r\n\r\nrequest.interceptors.request.use(config => {\r\n    config.headers['Content-Type'] = 'application/json;charset=utf-8';\r\n    return config\r\n}, error => {\r\n    return Promise.reject(error)\r\n});\r\n\r\nrequest.interceptors.response.use(\r\n    response => {\r\n        let res = response.data;\r\n        if (response.config.responseType === 'blob') {\r\n            return res\r\n        }\r\n        if (typeof res === 'string') {\r\n            res = res ? JSON.parse(res) : res\r\n        }\r\n        return res;\r\n    },\r\n    error => {\r\n        return Promise.reject(error)\r\n    }\r\n)\r\n\r\nexport default request\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,OAAO,MAAMC,IAAI,GAAG,4CAA4C;;AAEhE;AACA,MAAMC,WAAW,GAAG,uDAAuD;AAC3E,MAAMC,OAAO,GAAG,mDAAmD;AAEnE,MAAMC,OAAO,GAAGJ,KAAK,CAACK,MAAM,CAAC;EACzBC,OAAO,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,UAAU,GAAG,MAAOC,QAAQ,IAAK;EAC1C,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMT,KAAK,CAAC;MACzBU,MAAM,EAAE,MAAM;MACdC,GAAG,EAAET,WAAW;MAChBU,OAAO,EAAE;QACL,eAAe,EAAE,UAAUT,OAAO,EAAE;QACpC,cAAc,EAAE;MACpB,CAAC;MACDU,IAAI,EAAE;QACFC,KAAK,EAAE,OAAO;QACdN,QAAQ,EAAE,CACN;UACIO,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACoB,CAAC,EACD,GAAGR,QAAQ,CACd;QACDS,WAAW,EAAE,GAAG;QAChBC,KAAK,EAAE,IAAI;QACXC,UAAU,EAAE;MAChB;IACJ,CAAC,CAAC;IAEF,OAAOV,QAAQ,CAACI,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACL,OAAO;EAEnD,CAAC,CAAC,OAAOM,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,MAAMA,KAAK;EACf;AACJ,CAAC;AAIDlB,OAAO,CAACoB,YAAY,CAACpB,OAAO,CAACqB,GAAG,CAACC,MAAM,IAAI;EACvCA,MAAM,CAACd,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC;EACjE,OAAOc,MAAM;AACjB,CAAC,EAAEJ,KAAK,IAAI;EACR,OAAOK,OAAO,CAACC,MAAM,CAACN,KAAK,CAAC;AAChC,CAAC,CAAC;AAEFlB,OAAO,CAACoB,YAAY,CAACf,QAAQ,CAACgB,GAAG,CAC7BhB,QAAQ,IAAI;EACR,IAAIoB,GAAG,GAAGpB,QAAQ,CAACI,IAAI;EACvB,IAAIJ,QAAQ,CAACiB,MAAM,CAACI,YAAY,KAAK,MAAM,EAAE;IACzC,OAAOD,GAAG;EACd;EACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzBA,GAAG,GAAGA,GAAG,GAAGE,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC,GAAGA,GAAG;EACrC;EACA,OAAOA,GAAG;AACd,CAAC,EACDP,KAAK,IAAI;EACL,OAAOK,OAAO,CAACC,MAAM,CAACN,KAAK,CAAC;AAChC,CACJ,CAAC;AAED,eAAelB,OAAO", "ignoreList": []}]}