{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Ai.vue?vue&type=template&id=ba236c06&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Ai.vue", "mtime": 1741618492749}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_toDisplayString", "$options", "formatTime", "Date", "_Fragment", "_renderList", "$data", "messages", "msg", "index", "key", "_normalizeClass", "type", "_hoisted_7", "_hoisted_8", "innerHTML", "formatMessage", "content", "_hoisted_10", "time", "isLoading", "_hoisted_11", "_cache", "_hoisted_12", "_hoisted_13", "_createVNode", "_component_el_input", "userInput", "$event", "rows", "placeholder", "disabled", "onKeyup", "handleSend", "_component_el_button", "loading", "trim", "onClick", "length", "_hoisted_14", "suggestedQuestions", "question", "_createBlock", "_component_el_tag", "quickAsk", "effect"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Ai.vue"], "sourcesContent": ["<template>\r\n    <div class=\"ai-advisor\">\r\n        <!-- 聊天容器 -->\r\n        <div class=\"chat-container\">\r\n            <!-- 聊天头部 -->\r\n            <div class=\"chat-header\">\r\n                <div class=\"advisor-info\">\r\n                    <i class=\"fas fa-robot advisor-avatar\"></i>\r\n                    <div class=\"advisor-details\">\r\n                        <h2>AI求职顾问</h2>\r\n                        <span class=\"status online\">\r\n                            <i class=\"fas fa-circle\"></i> 在线\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 聊天消息区域 -->\r\n            <div class=\"chat-messages\" ref=\"messageContainer\">\r\n                <!-- 欢迎消息 -->\r\n                <div class=\"message-item ai-message\">\r\n                    <div class=\"message-avatar\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\">\r\n                            <p>👋 你好！我是你的AI求职顾问。</p>\r\n                            <p>我可以为你提供以下帮助：</p>\r\n                            <ul>\r\n                                <li>📝 简历优化和求职信写作指导</li>\r\n                                <li>🎯 职业规划和发展建议</li>\r\n                                <li>🤝 面试技巧和模拟面试</li>\r\n                                <li>💼 行业动态和求职策略</li>\r\n                                <li>❓ 解答求职过程中的各类问题</li>\r\n                            </ul>\r\n                            <p>请告诉我你需要什么帮助？</p>\r\n                        </div>\r\n                        <span class=\"message-time\">{{ formatTime(new Date()) }}</span>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 动态消息列表 -->\r\n                <div v-for=\"(msg, index) in messages\" :key=\"index\"\r\n                    :class=\"['message-item', msg.type === 'user' ? 'user-message' : 'ai-message']\">\r\n                    <div class=\"message-avatar\">\r\n                        <i :class=\"msg.type === 'user' ? 'fas fa-user' : 'fas fa-robot'\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\" v-html=\"formatMessage(msg.content)\"></div>\r\n                        <span class=\"message-time\">{{ formatTime(msg.time) }}</span>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 加载动画 -->\r\n                <div class=\"message-item ai-message\" v-if=\"isLoading\">\r\n                    <div class=\"message-avatar\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"typing-indicator\">\r\n                            <span></span>\r\n                            <span></span>\r\n                            <span></span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 输入区域 -->\r\n            <div class=\"chat-input\">\r\n                <div class=\"input-wrapper\">\r\n                    <el-input v-model=\"userInput\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入你的问题...\"\r\n                        :disabled=\"isLoading\" @keyup.enter.native.exact=\"handleSend\"\r\n                        @keyup.ctrl.enter.native=\"handleSend\">\r\n                    </el-input>\r\n                    <el-button type=\"primary\" :loading=\"isLoading\" :disabled=\"!userInput.trim() || isLoading\"\r\n                        @click=\"handleSend\">\r\n                        <i class=\"fas fa-paper-plane\"></i>\r\n                        发送\r\n                    </el-button>\r\n                </div>\r\n                <!-- 快捷问题建议 -->\r\n                <div class=\"quick-questions\" v-if=\"messages.length <= 1\">\r\n                    <el-tag v-for=\"(question, index) in suggestedQuestions\" :key=\"index\" @click=\"quickAsk(question)\"\r\n                        :disabled=\"isLoading\" effect=\"light\" class=\"question-tag\">\r\n                        <i class=\"fas fa-question-circle\"></i>\r\n                        {{ question }}\r\n                    </el-tag>\r\n                </div>\r\n                <div class=\"input-tips\">\r\n                    <span>按Enter换行，Ctrl + Enter发送</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import { chatWithAI } from \"../../../utils/http\";\r\n\r\n\r\n    export default {\r\n        name: \"Ai\",\r\n        data() {\r\n            return {\r\n                messages: [],\r\n                userInput: \"\",\r\n                isLoading: false,\r\n                chatHistory: [],\r\n                suggestedQuestions: [\r\n                    \"如何写一份优秀的简历？\",\r\n                    \"面试常见问题及回答技巧\",\r\n                    \"职业规划该怎么做？\",\r\n                    \"如何准备一份好的求职信？\",\r\n                    \"面试着装和礼仪要求\",\r\n                    \"如何谈薪资待遇？\"\r\n                ]\r\n            };\r\n        },\r\n        mounted() {\r\n            // 检查登录状态\r\n            const lname = sessionStorage.getItem(\"lname\");\r\n            if (!lname) {\r\n                this.$message({\r\n                    message: \"请先登录\",\r\n                    type: \"warning\",\r\n                    offset: 320,\r\n                });\r\n                this.$router.push(\"/slogin\");\r\n                return;\r\n            }\r\n        },\r\n        methods: {\r\n            async handleSend() {\r\n                if (!this.userInput.trim() || this.isLoading) return;\r\n\r\n                // 添加用户消息\r\n                const userMessage = this.userInput.trim();\r\n                this.addMessage(userMessage, 'user');\r\n                this.userInput = '';\r\n                this.isLoading = true;\r\n\r\n                try {\r\n                    // 准备对话历史\r\n                    this.chatHistory.push({ role: \"user\", content: userMessage });\r\n\r\n                    // 调用AI接口\r\n                    const response = await chatWithAI(this.chatHistory);\r\n\r\n                    // 保存AI回复到历史记录\r\n                    this.chatHistory.push({ role: \"assistant\", content: response });\r\n\r\n                    // 显示AI回复\r\n                    this.addMessage(response, 'ai');\r\n                } catch (error) {\r\n                    console.error('AI回复出错:', error);\r\n                    this.addMessage('抱歉，我遇到了一些问题，请稍后再试。', 'ai');\r\n                } finally {\r\n                    this.isLoading = false;\r\n                    this.scrollToBottom();\r\n                }\r\n            },\r\n\r\n            addMessage(content, type) {\r\n                this.messages.push({\r\n                    content,\r\n                    type,\r\n                    time: new Date()\r\n                });\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n            },\r\n\r\n            formatTime(date) {\r\n                return new Date(date).toLocaleTimeString('zh-CN', {\r\n                    hour: '2-digit',\r\n                    minute: '2-digit'\r\n                });\r\n            },\r\n\r\n            scrollToBottom() {\r\n                this.$nextTick(() => {\r\n                    const container = this.$refs.messageContainer;\r\n                    container.scrollTop = container.scrollHeight;\r\n                });\r\n            },\r\n\r\n            formatMessage(message) {\r\n                // 将换行符转换为<br>标签\r\n                return message.replace(/\\n/g, '<br>');\r\n            },\r\n\r\n            quickAsk(question) {\r\n                if (!this.isLoading) {\r\n                    this.userInput = question;\r\n                    this.handleSend();\r\n                }\r\n            }\r\n        }\r\n    };\r\n</script>\r\n\r\n<style scoped>\r\n    .ai-advisor {\r\n        padding: 20px;\r\n        height: calc(100vh - 180px);\r\n        display: flex;\r\n        justify-content: center;\r\n        background-color: #f5f7fa;\r\n    }\r\n\r\n    .chat-container {\r\n        width: 100%;\r\n        max-width: 900px;\r\n        background: #fff;\r\n        border-radius: 16px;\r\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: hidden;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 20px;\r\n        background: linear-gradient(135deg, #3498db, #2c3e50);\r\n        color: white;\r\n    }\r\n\r\n    .advisor-info {\r\n        display: flex;\r\n        align-items: center;\r\n    }\r\n\r\n    .advisor-avatar {\r\n        font-size: 32px;\r\n        margin-right: 15px;\r\n        color: #fff;\r\n    }\r\n\r\n    .advisor-details h2 {\r\n        margin: 0;\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n    }\r\n\r\n    .status {\r\n        font-size: 14px;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 5px;\r\n    }\r\n\r\n    .status.online i {\r\n        color: #2ecc71;\r\n        font-size: 10px;\r\n    }\r\n\r\n    .chat-messages {\r\n        flex: 1;\r\n        padding: 20px;\r\n        overflow-y: auto;\r\n        background: #f8f9fa;\r\n    }\r\n\r\n    .message-item {\r\n        display: flex;\r\n        margin-bottom: 20px;\r\n        align-items: flex-start;\r\n        animation: fadeIn 0.3s ease;\r\n    }\r\n\r\n    .message-avatar {\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        background: #e9ecef;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-right: 12px;\r\n        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    .ai-message .message-avatar {\r\n        background: linear-gradient(135deg, #3498db, #2980b9);\r\n        color: white;\r\n    }\r\n\r\n    .user-message {\r\n        flex-direction: row-reverse;\r\n    }\r\n\r\n    .user-message .message-avatar {\r\n        margin-right: 0;\r\n        margin-left: 12px;\r\n        background: linear-gradient(135deg, #2ecc71, #27ae60);\r\n        color: white;\r\n    }\r\n\r\n    .message-content {\r\n        max-width: 70%;\r\n    }\r\n\r\n    .user-message .message-content {\r\n        align-items: flex-end;\r\n    }\r\n\r\n    .message-text {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        background: white;\r\n        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\r\n        font-size: 15px;\r\n        line-height: 1.6;\r\n        white-space: pre-wrap;\r\n    }\r\n\r\n    .message-text :deep(br) {\r\n        margin-bottom: 8px;\r\n    }\r\n\r\n    .user-message .message-text {\r\n        background: #3498db;\r\n        color: white;\r\n    }\r\n\r\n    .message-time {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-top: 5px;\r\n        display: block;\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 20px;\r\n        background: white;\r\n        border-top: 1px solid #eaeaea;\r\n    }\r\n\r\n    .input-wrapper {\r\n        display: flex;\r\n        gap: 10px;\r\n    }\r\n\r\n    .input-wrapper .el-button {\r\n        height: auto;\r\n        padding: 12px 24px;\r\n    }\r\n\r\n    .input-wrapper .el-button i {\r\n        margin-right: 8px;\r\n    }\r\n\r\n    .quick-questions {\r\n        margin-top: 15px;\r\n        display: flex;\r\n        gap: 10px;\r\n        flex-wrap: wrap;\r\n    }\r\n\r\n    .question-tag {\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        padding: 8px 12px;\r\n    }\r\n\r\n    .question-tag i {\r\n        margin-right: 6px;\r\n    }\r\n\r\n    .question-tag:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    .input-tips {\r\n        margin-top: 10px;\r\n        font-size: 12px;\r\n        color: #666;\r\n        display: flex;\r\n        justify-content: space-between;\r\n    }\r\n\r\n    /* 修改输入提示文本 */\r\n    .input-tips span:last-child {\r\n        display: none;\r\n    }\r\n\r\n    /* 打字动画 */\r\n    .typing-indicator {\r\n        display: flex;\r\n        padding: 12px 16px;\r\n        background: white;\r\n        border-radius: 12px;\r\n        gap: 4px;\r\n    }\r\n\r\n    .typing-indicator span {\r\n        width: 8px;\r\n        height: 8px;\r\n        background: #3498db;\r\n        border-radius: 50%;\r\n        animation: typing 1s infinite ease-in-out;\r\n    }\r\n\r\n    .typing-indicator span:nth-child(1) {\r\n        animation-delay: 0.2s;\r\n    }\r\n\r\n    .typing-indicator span:nth-child(2) {\r\n        animation-delay: 0.3s;\r\n    }\r\n\r\n    .typing-indicator span:nth-child(3) {\r\n        animation-delay: 0.4s;\r\n    }\r\n\r\n    @keyframes typing {\r\n\r\n        0%,\r\n        100% {\r\n            transform: translateY(0);\r\n        }\r\n\r\n        50% {\r\n            transform: translateY(-10px);\r\n        }\r\n    }\r\n\r\n    @keyframes fadeIn {\r\n        from {\r\n            opacity: 0;\r\n            transform: translateY(10px);\r\n        }\r\n\r\n        to {\r\n            opacity: 1;\r\n            transform: translateY(0);\r\n        }\r\n    }\r\n\r\n    /* 消息内容样式 */\r\n    .message-text :deep(p) {\r\n        margin: 0 0 10px;\r\n    }\r\n\r\n    .message-text :deep(ul) {\r\n        margin: 10px 0;\r\n        padding-left: 20px;\r\n    }\r\n\r\n    .message-text :deep(li) {\r\n        margin: 5px 0;\r\n    }\r\n\r\n    .message-text :deep(code) {\r\n        background: rgba(0, 0, 0, 0.05);\r\n        padding: 2px 4px;\r\n        border-radius: 4px;\r\n        font-family: monospace;\r\n    }\r\n\r\n    .user-message .message-text :deep(code) {\r\n        background: rgba(255, 255, 255, 0.2);\r\n    }\r\n\r\n    /* 滚动条样式 */\r\n    .chat-messages::-webkit-scrollbar {\r\n        width: 6px;\r\n    }\r\n\r\n    .chat-messages::-webkit-scrollbar-track {\r\n        background: #f1f1f1;\r\n        border-radius: 3px;\r\n    }\r\n\r\n    .chat-messages::-webkit-scrollbar-thumb {\r\n        background: #888;\r\n        border-radius: 3px;\r\n    }\r\n\r\n    .chat-messages::-webkit-scrollbar-thumb:hover {\r\n        background: #555;\r\n    }\r\n\r\n    /* 响应式设计 */\r\n    @media (max-width: 768px) {\r\n        .ai-advisor {\r\n            padding: 10px;\r\n            height: calc(100vh - 140px);\r\n        }\r\n\r\n        .message-content {\r\n            max-width: 85%;\r\n        }\r\n\r\n        .quick-questions {\r\n            display: grid;\r\n            grid-template-columns: repeat(2, 1fr);\r\n            gap: 8px;\r\n        }\r\n    }\r\n</style>"], "mappings": ";;EACSA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAgB;;EAelBA,KAAK,EAAC,eAAe;EAACC,GAAG,EAAC;;;EAEtBD,KAAK,EAAC;AAAyB;;EAI3BA,KAAK,EAAC;AAAiB;;EAalBA,KAAK,EAAC;AAAc;;EAOzBA,KAAK,EAAC;AAAgB;;EAGtBA,KAAK,EAAC;AAAiB;;;EAElBA,KAAK,EAAC;AAAc;;;EAK7BA,KAAK,EAAC;;;EAeVA,KAAK,EAAC;AAAY;;EACdA,KAAK,EAAC;AAAe;;;EAYrBA,KAAK,EAAC;;;;;;uBAjFvBE,mBAAA,CA6FM,OA7FNC,UA6FM,GA5FFC,mBAAA,UAAa,EACbC,mBAAA,CA0FM,OA1FNC,UA0FM,GAzFFF,mBAAA,UAAa,E,kZAabA,mBAAA,YAAe,EACfC,mBAAA,CAgDM,OAhDNE,UAgDM,GA/CFH,mBAAA,UAAa,EACbC,mBAAA,CAmBM,OAnBNG,UAmBM,G,0BAlBFH,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAgB,IACvBK,mBAAA,CAA4B;IAAzBL,KAAK,EAAC;EAAc,G,sBAE3BK,mBAAA,CAcM,OAdNI,UAcM,G,0BAbFJ,mBAAA,CAWM;IAXDL,KAAK,EAAC;EAAc,IACrBK,mBAAA,CAAwB,WAArB,mBAAiB,GACpBA,mBAAA,CAAmB,WAAhB,cAAY,GACfA,mBAAA,CAMK,aALDA,mBAAA,CAAwB,YAApB,iBAAe,GACnBA,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAuB,YAAnB,gBAAc,E,GAEtBA,mBAAA,CAAmB,WAAhB,cAAY,E,sBAEnBA,mBAAA,CAA8D,QAA9DK,UAA8D,EAAAC,gBAAA,CAAhCC,QAAA,CAAAC,UAAU,KAAKC,IAAI,oB,KAIzDV,mBAAA,YAAe,G,kBACfF,mBAAA,CASMa,SAAA,QAAAC,WAAA,CATsBC,KAAA,CAAAC,QAAQ,GAAvBC,GAAG,EAAEC,KAAK;yBAAvBlB,mBAAA,CASM;MATiCmB,GAAG,EAAED,KAAK;MAC5CpB,KAAK,EAAAsB,eAAA,kBAAmBH,GAAG,CAACI,IAAI;QACjClB,mBAAA,CAEM,OAFNmB,UAEM,GADFnB,mBAAA,CAAqE;MAAjEL,KAAK,EAAAsB,eAAA,CAAEH,GAAG,CAACI,IAAI;+BAEvBlB,mBAAA,CAGM,OAHNoB,UAGM,GAFFpB,mBAAA,CAAoE;MAA/DL,KAAK,EAAC,cAAc;MAAC0B,SAAmC,EAA3Bd,QAAA,CAAAe,aAAa,CAACR,GAAG,CAACS,OAAO;yCAC3DvB,mBAAA,CAA4D,QAA5DwB,WAA4D,EAAAlB,gBAAA,CAA9BC,QAAA,CAAAC,UAAU,CAACM,GAAG,CAACW,IAAI,kB;kCAIzD1B,mBAAA,UAAa,EAC8Ba,KAAA,CAAAc,SAAS,I,cAApD7B,mBAAA,CAWM,OAXN8B,WAWM,EAAAC,MAAA,QAAAA,MAAA,OAVF5B,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAgB,IACvBK,mBAAA,CAA4B;IAAzBL,KAAK,EAAC;EAAc,G,qBAE3BK,mBAAA,CAMM;IANDL,KAAK,EAAC;EAAiB,IACxBK,mBAAA,CAIM;IAJDL,KAAK,EAAC;EAAkB,IACzBK,mBAAA,CAAa,SACbA,mBAAA,CAAa,SACbA,mBAAA,CAAa,Q,uFAM7BD,mBAAA,UAAa,EACbC,mBAAA,CAuBM,OAvBN6B,WAuBM,GAtBF7B,mBAAA,CAUM,OAVN8B,WAUM,GATFC,YAAA,CAGWC,mBAAA;gBAHQpB,KAAA,CAAAqB,SAAS;+DAATrB,KAAA,CAAAqB,SAAS,GAAAC,MAAA;IAAEhB,IAAI,EAAC,UAAU;IAAEiB,IAAI,EAAE,CAAC;IAAEC,WAAW,EAAC,YAAY;IAC3EC,QAAQ,EAAEzB,KAAA,CAAAc,SAAS;IAAGY,OAAK,G,yBAAqB/B,QAAA,CAAAgC,UAAU,oC,yBACjChC,QAAA,CAAAgC,UAAU;kEAExCR,YAAA,CAIYS,oBAAA;IAJDtB,IAAI,EAAC,SAAS;IAAEuB,OAAO,EAAE7B,KAAA,CAAAc,SAAS;IAAGW,QAAQ,GAAGzB,KAAA,CAAAqB,SAAS,CAACS,IAAI,MAAM9B,KAAA,CAAAc,SAAS;IACnFiB,OAAK,EAAEpC,QAAA,CAAAgC;;sBACR,MAAkCX,MAAA,QAAAA,MAAA,OAAlC5B,mBAAA,CAAkC;MAA/BL,KAAK,EAAC;IAAoB,4B,iBAAK,MAEtC,E;;2DAEJI,mBAAA,YAAe,EACoBa,KAAA,CAAAC,QAAQ,CAAC+B,MAAM,S,cAAlD/C,mBAAA,CAMM,OANNgD,WAMM,I,kBALFhD,mBAAA,CAISa,SAAA,QAAAC,WAAA,CAJ2BC,KAAA,CAAAkC,kBAAkB,GAAtCC,QAAQ,EAAEhC,KAAK;yBAA/BiC,YAAA,CAISC,iBAAA;MAJgDjC,GAAG,EAAED,KAAK;MAAG4B,OAAK,EAAAT,MAAA,IAAE3B,QAAA,CAAA2C,QAAQ,CAACH,QAAQ;MACzFV,QAAQ,EAAEzB,KAAA,CAAAc,SAAS;MAAEyB,MAAM,EAAC,OAAO;MAACxD,KAAK,EAAC;;wBAC3C,MAAsC,C,0BAAtCK,mBAAA,CAAsC;QAAnCL,KAAK,EAAC;MAAwB,6B,iBAAK,GACtC,GAAAW,gBAAA,CAAGyC,QAAQ,iB;;;mGAGnB/C,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAY,IACnBK,mBAAA,CAAoC,cAA9B,yBAAuB,E", "ignoreList": []}]}