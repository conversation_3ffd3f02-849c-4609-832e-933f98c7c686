{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsView.vue?vue&type=template&id=7f1421ea&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsView.vue", "mtime": 1741617175000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsView.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE;UACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC;UACE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7E,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/PositionsView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"position-detail\">\r\n    <div class=\"detail-card\">\r\n      <div class=\"position-header\">\r\n        <h1 class=\"position-title\">{{ formData.pname }}</h1>\r\n        <div class=\"position-meta\">\r\n          <span class=\"salary\">{{ formData.streatment }}</span>\r\n          <span class=\"location\"\r\n            ><i class=\"el-icon-location\"></i>工作地点：{{ formData.wlocation }}</span\r\n          >\r\n          <span class=\"number\"><i class=\"el-icon-user\"></i>招聘人数：{{ formData.rnumber }}人</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"position-body\">\r\n        <div class=\"info-section\">\r\n          <h2 class=\"section-title\"><i class=\"el-icon-office-building\"></i>职位要求</h2>\r\n          <div class=\"section-content\" v-html=\"formData.prequirements\"></div>\r\n        </div>\r\n\r\n        <div class=\"info-section\">\r\n          <h2 class=\"section-title\"><i class=\"el-icon-info\"></i>其他信息</h2>\r\n          <div class=\"section-content\">\r\n            <p>发布时间：{{ formData.ptime }}</p>\r\n            <p>职位状态：{{ formData.pflag }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"position-footer\">\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"large\"\r\n          @click=\"handleDelivery\"\r\n          :disabled=\"formData.pflag !== '开放'\"\r\n        >\r\n          投递简历\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 简历选择对话框 -->\r\n    <el-dialog title=\"选择简历\" v-model=\"dialogVisible\" width=\"500px\">\r\n      <div v-if=\"resumeList.length > 0\">\r\n        <el-radio-group v-model=\"selectedResumeId\">\r\n          <div v-for=\"resume in resumeList\" :key=\"resume.rid\" class=\"resume-item\">\r\n            <el-radio :label=\"resume.rid\">\r\n              {{ resume.resumename }}\r\n              <span class=\"resume-date\">创建时间：{{ resume.createdat }}</span>\r\n            </el-radio>\r\n          </div>\r\n        </el-radio-group>\r\n      </div>\r\n      <div v-else class=\"no-resume\">\r\n        <el-empty description=\"暂无简历\">\r\n          <el-button type=\"primary\" @click=\"goToAddResume\">创建简历</el-button>\r\n        </el-empty>\r\n      </div>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitDelivery\" :disabled=\"!selectedResumeId\">\r\n            确 定\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from '../../../utils/http';\r\n\r\nexport default {\r\n  name: 'PositionsView',\r\n  data() {\r\n    return {\r\n      id: '',\r\n      formData: {},\r\n      dialogVisible: false,\r\n      resumeList: [],\r\n      selectedResumeId: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getPositionData();\r\n  },\r\n  methods: {\r\n    // 获取职位详情\r\n    async getPositionData() {\r\n      try {\r\n        let url = base + '/positions/get?id=' + this.id;\r\n        const res = await request.post(url);\r\n        if (res.code === 200) {\r\n          this.formData = res.resdata;\r\n          // 获取职位信息后记录浏览记录\r\n          await this.recordBrowsingHistory();\r\n        }\r\n      } catch (error) {\r\n        console.error('获取职位详情失败:', error);\r\n        this.$message({\r\n          message: '获取职位信息失败，请重试',\r\n          type: 'error',\r\n          offset: 320,\r\n        });\r\n      }\r\n    },\r\n\r\n    // 检查是否已经投递过\r\n    async checkIfDelivered() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      let url = base + '/resumedelivery/list';\r\n      try {\r\n        const res = await request.post(\r\n          url,\r\n          { sno: lname, pid: this.id },\r\n          { params: { currentPage: 1, pageSize: 1 } }\r\n        );\r\n        if (res.code === 200 && res.resdata.length > 0) {\r\n          return true;\r\n        }\r\n        return false;\r\n      } catch (error) {\r\n        console.error('检查投递记录失败:', error);\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 修改处理投递简历按钮点击方法\r\n    async handleDelivery() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      if (!lname) {\r\n        this.$message({\r\n          message: '请先登录',\r\n          type: 'warning',\r\n          offset: 320,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 检查是否已投递\r\n      const hasDelivered = await this.checkIfDelivered();\r\n      if (hasDelivered) {\r\n        this.$message({\r\n          message: '您已经投递过该职位',\r\n          type: 'warning',\r\n          offset: 320,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 获取用户简历列表\r\n      this.getResumeList();\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n    // 获取用户简历列表\r\n    getResumeList() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      let url = base + '/resume/list';\r\n      request\r\n        .post(url, { sno: lname }, { params: { currentPage: 1, pageSize: 100 } })\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            this.resumeList = res.resdata;\r\n          }\r\n        });\r\n    },\r\n\r\n    // 跳转到创建简历页面\r\n    goToAddResume() {\r\n      this.dialogVisible = false;\r\n      this.$router.push('/resume_add');\r\n    },\r\n\r\n    // 提交简历投递\r\n    submitDelivery() {\r\n      if (!this.selectedResumeId) {\r\n        this.$message({\r\n          message: '请选择要投递的简历',\r\n          type: 'warning',\r\n          offset: 320,\r\n        });\r\n        return;\r\n      }\r\n\r\n      const lname = sessionStorage.getItem('lname');\r\n      const data = {\r\n        pid: this.id,\r\n        sno: lname,\r\n        rid: this.selectedResumeId,\r\n      };\r\n\r\n      let url = base + '/resumedelivery/add';\r\n      data.auditstatus = '待审核';\r\n      request.post(url, data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message({\r\n            message: '简历投递成功',\r\n            type: 'success',\r\n            offset: 320,\r\n          });\r\n          this.dialogVisible = false;\r\n        } else {\r\n          this.$message({\r\n            message: res.msg || '投递失败，请重试',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 记录浏览记录\r\n    async recordBrowsingHistory() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      if (!lname) {\r\n        return; // 未登录不记录浏览记录\r\n      }\r\n\r\n      const data = {\r\n        pid: this.id,\r\n        sno: lname,\r\n        btime: new Date().toLocaleString(),\r\n      };\r\n\r\n      try {\r\n        let url = base + '/browsingrecords/add';\r\n        const res = await request.post(url, data);\r\n        if (res.code !== 200) {\r\n          console.error('记录浏览记录失败:', res.msg);\r\n        }\r\n      } catch (error) {\r\n        console.error('记录浏览记录失败:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.position-detail {\r\n  max-width: 1000px;\r\n  margin: 20px auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n.detail-card {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 30px;\r\n}\r\n\r\n.position-header {\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.position-title {\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.position-meta {\r\n  display: flex;\r\n  gap: 20px;\r\n  color: #666;\r\n}\r\n\r\n.salary {\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n}\r\n\r\n.location,\r\n.number {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.info-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.section-content {\r\n  color: #666;\r\n  line-height: 1.8;\r\n}\r\n\r\n.position-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.resume-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.resume-date {\r\n  margin-left: 10px;\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.no-resume {\r\n  text-align: center;\r\n  padding: 30px 0;\r\n}\r\n</style>\r\n"]}]}