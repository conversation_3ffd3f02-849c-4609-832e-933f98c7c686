{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue?vue&type=style&index=0&id=3c83f0b7&lang=css", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue", "mtime": 1741615257361}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue"], "names": [], "mappings": ";EAkGE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B;;IAEA,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;;IAEA,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/components/Left.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"col-xxl-4 col-xl-4 col-lg-4\">\r\n    <div class=\"animated-list\">\r\n      <h2 class=\"section-title\">职位分类</h2>\r\n      <ul class=\"fen-list\">\r\n        <li v-for=\"(item, index) in list1\" :key=\"index\">\r\n          <a :href=\"'positionsList?catid=' + item.catid\">{{ item.catname }}</a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"animated-list\">\r\n      <h2 class=\"section-title\">最新职位</h2>\r\n      <ul class=\"news-list\">\r\n        <li class=\"fade-in\" v-for=\"(item, index) in list2\" :key=\"index\">\r\n          <a :href=\"'positionsView?id=' + item.pid\"><i class=\"fas fa-star\"></i>{{ item.pname }}</a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"animated-gallery\">\r\n      <h2 class=\"section-title\">最新企业</h2>\r\n      <div class=\"image-grid\">\r\n        <div class=\"image-item zoom-in\" v-for=\"(item, index) in list3\" :key=\"index\">\r\n          <a :href=\"'companyView?id=' + item.cid\">\r\n            <img :src=\"'http://localhost:8088/JobHuntingSystem/' + item.logo\" style=\"width: 165px; height: 123px\" />\r\n            <p class=\"image-caption\">{{ item.comname }}</p>\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../utils/http';\r\n  export default {\r\n    name: 'Left',\r\n    data() {\r\n      return {\r\n        list1: '',\r\n        list2: '',\r\n        list3: '',\r\n        isLoggedIn: false,\r\n      };\r\n    },\r\n    created() {\r\n      this.checkLoginStatus();\r\n      this.getlist1();\r\n      this.getlist2();\r\n      this.getlist3();\r\n    },\r\n    methods: {\r\n      // 检查登录状态\r\n      checkLoginStatus() {\r\n        const lname = sessionStorage.getItem('lname');\r\n        this.isLoggedIn = !!lname;\r\n      },\r\n\r\n      // 获取职位分类\r\n      getlist1() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + '/jobcategories/list?currentPage=1&pageSize=100';\r\n        request.post(url, para).then((res) => {\r\n          this.list1 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 获取最新职位\r\n      getlist2() {\r\n        let para = {\r\n          pflag: '开放', // 只获取开放状态的职位\r\n          pflag2: '审核通过',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/positions/list?currentPage=1&pageSize=10';\r\n        request.post(url, para).then((res) => {\r\n          this.list2 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 获取最新企业\r\n      getlist3() {\r\n        let para = {\r\n          cflag: '审核通过',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/company/list?currentPage=1&pageSize=6';\r\n        request.post(url, para).then((res) => {\r\n          this.list3 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style>\r\n  .fen-list {\r\n    list-style-type: none;\r\n    padding: 0;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .fen-list li {\r\n    width: calc(50% - 10px);\r\n    margin-bottom: 15px;\r\n    opacity: 0;\r\n    animation: fadeIn 0.5s ease-in forwards;\r\n    font-size: 16px;\r\n    color: #555;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .fen-list li:hover {\r\n    transform: translateY(-5px);\r\n  }\r\n\r\n  .fen-list li a {\r\n    color: #333;\r\n    text-decoration: none;\r\n    display: block;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 100%;\r\n    padding: 10px;\r\n    background-color: #f0f0f0;\r\n    border-radius: 5px;\r\n    transition: background-color 0.3s ease;\r\n  }\r\n\r\n  .fen-list li a:hover {\r\n    color: #3498db;\r\n    background-color: #e0e0e0;\r\n  }\r\n\r\n  .fen-list li i {\r\n    margin-right: 10px;\r\n    color: #3498db;\r\n  }\r\n\r\n  @keyframes fadeIn {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(20px);\r\n    }\r\n\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .animated-list,\r\n  .animated-gallery {\r\n    margin-bottom: 40px;\r\n    background: #f9f9f9;\r\n    border-radius: 10px;\r\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 18px;\r\n    margin-bottom: 20px;\r\n    color: #333;\r\n    border-bottom: 2px solid #3498db;\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .news-list {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  .news-list li {\r\n    margin-bottom: 15px;\r\n    opacity: 0;\r\n    animation: fadeIn 0.5s ease-in forwards;\r\n    display: flex;\r\n    flex-direction: column;\r\n    font-size: 16px;\r\n    color: #555;\r\n  }\r\n\r\n  .news-list li a {\r\n    color: #333;\r\n    text-decoration: none;\r\n    display: flex;\r\n    align-items: center;\r\n    display: block;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .news-list li a:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .news-list li i {\r\n    margin-right: 10px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .news-date {\r\n    font-size: 14px;\r\n    color: #888;\r\n    margin-top: 5px;\r\n    margin-left: 24px;\r\n  }\r\n\r\n  .section-divider {\r\n    border: 0;\r\n    height: 1px;\r\n    background: #e0e0e0;\r\n    margin: 30px 0;\r\n  }\r\n\r\n  .image-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 20px;\r\n  }\r\n\r\n  .image-item {\r\n    position: relative;\r\n    overflow: hidden;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .image-item img {\r\n    width: 100%;\r\n    height: auto;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .image-item:hover img {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  .image-caption {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: #fff;\r\n    padding: 10px;\r\n    margin: 0;\r\n    font-size: 13px;\r\n    text-align: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n  }\r\n\r\n  .image-item:hover .image-caption {\r\n    opacity: 1;\r\n  }\r\n\r\n  @keyframes fadeIn {\r\n    to {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .fade-in {\r\n    animation-delay: calc(var(--item-index) * 0.1s);\r\n  }\r\n\r\n  .zoom-in {\r\n    animation: zoomIn 0.5s ease-out;\r\n  }\r\n\r\n  @keyframes zoomIn {\r\n    from {\r\n      transform: scale(0.8);\r\n      opacity: 0;\r\n    }\r\n\r\n    to {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .user-menu {\r\n    list-style-type: none;\r\n    padding: 0;\r\n    margin: 0;\r\n  }\r\n\r\n  .user-menu li {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .user-menu a {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 10px 15px;\r\n    color: #333;\r\n    text-decoration: none;\r\n    border-radius: 5px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .user-menu a:hover {\r\n    background-color: #3bc0c3;\r\n    color: #fff;\r\n  }\r\n\r\n  .user-menu a.router-link-active {\r\n    background-color: #3bc0c3;\r\n    color: #fff;\r\n  }\r\n\r\n  .user-menu i {\r\n    margin-right: 10px;\r\n    font-size: 18px;\r\n  }\r\n</style>"]}]}