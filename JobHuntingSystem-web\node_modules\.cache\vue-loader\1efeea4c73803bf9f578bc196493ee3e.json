{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsDetail.vue", "mtime": 1741615889951}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCiAgaW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOw0KICBleHBvcnQgZGVmYXVsdCB7DQogICAgbmFtZTogJ1N0dWRlbnRzRGV0YWlsJywNCiAgICBjb21wb25lbnRzOiB7DQogICAgfSwNCiAgICBkYXRhKCkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBmb3JtRGF0YToge30sIC8v6KGo5Y2V5pWw5o2uICAgICAgICAgDQoNCiAgICAgIH07DQogICAgfSwNCiAgICBjcmVhdGVkKCkgew0KICAgICAgdGhpcy5pZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOyAvL+iOt+WPluWPguaVsA0KICAgICAgdGhpcy5nZXREYXRhcygpOw0KICAgIH0sDQoNCg0KICAgIG1ldGhvZHM6IHsNCg0KICAgICAgLy/ojrflj5bliJfooajmlbDmja4NCiAgICAgIGdldERhdGFzKCkgew0KICAgICAgICBsZXQgcGFyYSA9IHsNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7DQogICAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9zdHVkZW50cy9nZXQ/aWQ9IiArIHRoaXMuaWQ7DQogICAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHBhcmEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMuZm9ybURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJlcy5yZXNkYXRhKSk7DQogICAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9KTsNCiAgICAgIH0sDQoNCiAgICAgIC8vIOi/lOWbng0KICAgICAgYmFjaygpIHsNCiAgICAgICAgLy/ov5Tlm57kuIrkuIDpobUNCiAgICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsNCiAgICAgIH0sDQoNCiAgICB9LA0KICB9DQoNCg=="}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsDetail.vue"], "names": [], "mappings": ";;EA8CE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,EAAE,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;;IAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;MAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;;IAEH,CAAC;EACH", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/students/StudentsDetail.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">求职者管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">求职者详情</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">求职者详情</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n      <el-form-item label=\"账号\">\r\n        {{ formData.sno }}</el-form-item>\r\n      <el-form-item label=\"密码\">\r\n        {{ formData.password }}</el-form-item>\r\n      <el-form-item label=\"姓名\">\r\n        {{ formData.sname }}</el-form-item>\r\n      <el-form-item label=\"性别\">\r\n        {{ formData.gender }}</el-form-item>\r\n      <el-form-item label=\"年龄\">\r\n        {{ formData.age }}</el-form-item>\r\n      <el-form-item label=\"手机号码\">\r\n        {{ formData.phone }}</el-form-item>\r\n      <el-form-item label=\"专业\">\r\n        {{ formData.proname }}</el-form-item>\r\n      <el-form-item label=\"照片\" prop=\"spic\">\r\n        <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" style=\"width: 150px;height: 150px\" />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"注册时间\">\r\n        {{ formData.regtime }}</el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\n\r\n  import request, { base } from \"../../../../utils/http\";\r\n  export default {\r\n    name: 'StudentsDetail',\r\n    components: {\r\n    },\r\n    data() {\r\n      return {\r\n        id: '',\r\n        formData: {}, //表单数据         \r\n\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id; //获取参数\r\n      this.getDatas();\r\n    },\r\n\r\n\r\n    methods: {\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/students/get?id=\" + this.id;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 返回\r\n      back() {\r\n        //返回上一页\r\n        this.$router.go(-1);\r\n      },\r\n\r\n    },\r\n  }\r\n\r\n</script>\r\n<style scoped></style>"]}]}