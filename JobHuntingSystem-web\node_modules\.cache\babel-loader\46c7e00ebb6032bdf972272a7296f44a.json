{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sreg.vue?vue&type=template&id=27582d56", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sreg.vue", "mtime": 1741615898664}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_input", "sno", "$event", "placeholder", "style", "type", "password", "password2", "sname", "_component_el_radio_group", "gender", "_component_el_radio", "_cache", "age", "phone", "_component_el_select", "proid", "size", "_createElementBlock", "_Fragment", "_renderList", "professionalsList", "item", "_createBlock", "_component_el_option", "key", "proname", "value", "spic", "readonly", "_component_el_button", "onClick", "$options", "showUpload", "save", "loading", "btnLoading", "icon", "_component_el_dialog", "uploadVisible", "title", "onClose", "_ctx", "closeDialog", "_createElementVNode", "_component_el_upload", "action", "drag", "limit", "handlePreview", "handleRemove", "fileList", "handleExceed", "name", "fileListChange", "id", "_hoisted_1", "hideUpload", "handleConfirm"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sreg.vue"], "sourcesContent": ["<template>\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n    <el-form-item label=\"账号\" prop=\"sno\">\n      <el-input v-model=\"formData.sno\" placeholder=\"账号\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"密码\" prop=\"password\">\n      <el-input\n        type=\"password\"\n        v-model=\"formData.password\"\n        placeholder=\"密码\"\n        style=\"width: 50%\"\n      ></el-input>\n    </el-form-item>\n    <el-form-item label=\"确认密码\" prop=\"password2\">\n      <el-input\n        type=\"password\"\n        v-model=\"formData.password2\"\n        placeholder=\"确认密码\"\n        style=\"width: 50%\"\n      ></el-input>\n    </el-form-item>\n    <el-form-item label=\"姓名\" prop=\"sname\">\n      <el-input v-model=\"formData.sname\" placeholder=\"姓名\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"性别\" prop=\"gender\">\n      <el-radio-group v-model=\"formData.gender\">\n        <el-radio label=\"男\"> 男 </el-radio>\n        <el-radio label=\"女\"> 女 </el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item label=\"年龄\" prop=\"age\">\n      <el-input v-model=\"formData.age\" placeholder=\"年龄\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"手机号码\" prop=\"phone\">\n      <el-input v-model=\"formData.phone\" placeholder=\"手机号码\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"专业\" prop=\"proid\">\n      <el-select v-model=\"formData.proid\" placeholder=\"请选择\" size=\"small\">\n        <el-option\n          v-for=\"item in professionalsList\"\n          :key=\"item.proid\"\n          :label=\"item.proname\"\n          :value=\"item.proid\"\n        ></el-option>\n      </el-select>\n    </el-form-item>\n    <el-form-item prop=\"spic\" label=\"照片\" min-width=\"20%\">\n      <el-input\n        v-model=\"formData.spic\"\n        placeholder=\"照片\"\n        readonly=\"true\"\n        style=\"width: 50%\"\n      ></el-input>\n      <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n    </el-form-item>\n\n    <el-form-item>\n      <el-button\n        type=\"primary\"\n        size=\"small\"\n        @click=\"save\"\n        :loading=\"btnLoading\"\n        icon=\"el-icon-upload\"\n        >注 册</el-button\n      >\n    </el-form-item>\n  </el-form>\n  <el-dialog\n    v-model=\"uploadVisible\"\n    title=\"附件上传\"\n    custom-class=\"el-dialog-widthSmall\"\n    @close=\"closeDialog\"\n  >\n    <div>\n      <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n    </div>\n    <el-upload\n      action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n      style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\"\n      drag\n      :limit=\"1\"\n      :on-preview=\"handlePreview\"\n      :on-remove=\"handleRemove\"\n      :file-list=\"fileList\"\n      :on-exceed=\"handleExceed\"\n      :auto-upload=\"false\"\n      name=\"file\"\n      :on-change=\"fileListChange\"\n    >\n      <i class=\"el-icon-upload\"></i>\n      <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n      <div class=\"el-upload__tip\">\n        <div\n          style=\"display: inline; color: #d70000; font-size: 14px\"\n          class=\"uploadFileWarning\"\n          id=\"uploadFileWarning\"\n        ></div>\n      </div>\n    </el-upload>\n    <span class=\"dialog-footer\">\n      <el-button @click=\"hideUpload\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n    </span>\n  </el-dialog>\n</template>\n<script>\nimport request, { base } from '../../../utils/http';\nexport default {\n  name: 'Sreg',\n  data() {\n    return {\n      formData: {},\n      professionalsList: [], // 专业列表\n\n      addrules: {\n        sno: [{ required: true, message: '请输入账号', trigger: 'blur' }],\n        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\n        password2: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          {\n            validator: (rule, value, callback) => {\n              if (value !== this.formData.password) {\n                callback(new Error('两次输入密码不一致!'));\n              } else {\n                callback();\n              }\n            },\n            trigger: 'blur',\n          },\n        ],\n        sname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' }],\n        phone: [\n          { required: true, message: '请输入手机号码', trigger: 'blur' },\n          { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\n        ],\n        proid: [{ required: true, message: '请选择专业', trigger: 'onchange' }],\n        spic: [{ required: true, message: '请上传照片', trigger: 'blur' }],\n      },\n\n      btnLoading: false, //按钮是否在加载中\n      uploadVisible: false, //上传弹出框\n    };\n  },\n  created() {\n    this.getProfessionals();\n  },\n  methods: {\n    // 获取专业列表\n    getProfessionals() {\n      let url = base + '/professionals/list';\n      request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\n        if (res.code == 200) {\n          this.professionalsList = res.resdata;\n        }\n      });\n    },\n    //注册\n    save() {\n      //表单验证\n      this.$refs['formDataRef'].validate((valid) => {\n        if (valid) {\n          let url = base + '/students/add'; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          request.post(url, this.formData).then((res) => {\n            //请求接口\n            if (res.code == 200) {\n              this.$message({\n                message: '恭喜您，注册成功！',\n                type: 'success',\n                offset: 320,\n              });\n              this.$router.push('/slogin');\n            } else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: 'error',\n                offset: 320,\n              });\n              this.btnLoading = false;\n            } else {\n              this.$message({\n                message: '服务器错误',\n                type: 'error',\n                offset: 320,\n              });\n              this.btnLoading = false;\n            }\n          });\n        }\n      });\n    },\n    //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: '只能上传一个文件',\n        type: 'error',\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = 'png,jpg';\n      let extendFileNames = extendFileName.split(',');\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key['name']);\n        }\n        if (!ret) {\n          console.log(key['name'] + ':' + ret);\n          that.$message({\n            duration: 1000,\n            message: '上传的文件后缀必须为' + extendFileName + '格式！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key['name']) != -1) {\n          that.$message({\n            duration: 1000,\n            message: '上传的文件重复！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key['name']);\n        if (fileNames !== '') {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: '请选择文件！',\n          type: 'error',\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append('file', file.raw, file.raw.name);\n      });\n      let url = base + '/common/uploadFile';\n      console.log('url=' + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.spic = furl; // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n  },\n};\n</script>\n\n<style></style>\n"], "mappings": ";;EAmGUA,KAAK,EAAC;AAAe;;;;;;;;;;;;6DAlG7BC,YAAA,CAiEUC,kBAAA;IAjEAC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAEC,KAAK,EAAC;;sBACrF,MAEe,CAFfR,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAAgF,CAAhFX,YAAA,CAAgFY,mBAAA;oBAA7DT,KAAA,CAAAC,QAAQ,CAACS,GAAG;mEAAZV,KAAA,CAAAC,QAAQ,CAACS,GAAG,GAAAC,MAAA;QAAEC,WAAW,EAAC,IAAI;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAEpDhB,YAAA,CAOeS,uBAAA;MAPDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAKY,CALZX,YAAA,CAKYY,mBAAA;QAJVK,IAAI,EAAC,UAAU;oBACNd,KAAA,CAAAC,QAAQ,CAACc,QAAQ;mEAAjBf,KAAA,CAAAC,QAAQ,CAACc,QAAQ,GAAAJ,MAAA;QAC1BC,WAAW,EAAC,IAAI;QAChBC,KAAkB,EAAlB;UAAA;QAAA;;;QAGJhB,YAAA,CAOeS,uBAAA;MAPDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAKY,CALZX,YAAA,CAKYY,mBAAA;QAJVK,IAAI,EAAC,UAAU;oBACNd,KAAA,CAAAC,QAAQ,CAACe,SAAS;mEAAlBhB,KAAA,CAAAC,QAAQ,CAACe,SAAS,GAAAL,MAAA;QAC3BC,WAAW,EAAC,MAAM;QAClBC,KAAkB,EAAlB;UAAA;QAAA;;;QAGJhB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAAkF,CAAlFX,YAAA,CAAkFY,mBAAA;oBAA/DT,KAAA,CAAAC,QAAQ,CAACgB,KAAK;mEAAdjB,KAAA,CAAAC,QAAQ,CAACgB,KAAK,GAAAN,MAAA;QAAEC,WAAW,EAAC,IAAI;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAEtDhB,YAAA,CAKeS,uBAAA;MALDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAGiB,CAHjBX,YAAA,CAGiBqB,yBAAA;oBAHQlB,KAAA,CAAAC,QAAQ,CAACkB,MAAM;mEAAfnB,KAAA,CAAAC,QAAQ,CAACkB,MAAM,GAAAR,MAAA;;0BACtC,MAAkC,CAAlCd,YAAA,CAAkCuB,mBAAA;UAAxBb,KAAK,EAAC;QAAG;4BAAC,MAAGc,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;YACvBxB,YAAA,CAAkCuB,mBAAA;UAAxBb,KAAK,EAAC;QAAG;4BAAC,MAAGc,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;;;;QAG3BxB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAAgF,CAAhFX,YAAA,CAAgFY,mBAAA;oBAA7DT,KAAA,CAAAC,QAAQ,CAACqB,GAAG;mEAAZtB,KAAA,CAAAC,QAAQ,CAACqB,GAAG,GAAAX,MAAA;QAAEC,WAAW,EAAC,IAAI;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAEpDhB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAoF,CAApFX,YAAA,CAAoFY,mBAAA;oBAAjET,KAAA,CAAAC,QAAQ,CAACsB,KAAK;mEAAdvB,KAAA,CAAAC,QAAQ,CAACsB,KAAK,GAAAZ,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAExDhB,YAAA,CASeS,uBAAA;MATDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAOY,CAPZX,YAAA,CAOY2B,oBAAA;oBAPQxB,KAAA,CAAAC,QAAQ,CAACwB,KAAK;mEAAdzB,KAAA,CAAAC,QAAQ,CAACwB,KAAK,GAAAd,MAAA;QAAEC,WAAW,EAAC,KAAK;QAACc,IAAI,EAAC;;0BAEvD,MAAiC,E,kBADnCC,mBAAA,CAKaC,SAAA,QAAAC,WAAA,CAJI7B,KAAA,CAAA8B,iBAAiB,EAAzBC,IAAI;+BADbC,YAAA,CAKaC,oBAAA;YAHVC,GAAG,EAAEH,IAAI,CAACN,KAAK;YACflB,KAAK,EAAEwB,IAAI,CAACI,OAAO;YACnBC,KAAK,EAAEL,IAAI,CAACN;;;;;;QAInB5B,YAAA,CAQeS,uBAAA;MARDE,IAAI,EAAC,MAAM;MAACD,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC;;wBAC7C,MAKY,CALZV,YAAA,CAKYY,mBAAA;oBAJDT,KAAA,CAAAC,QAAQ,CAACoC,IAAI;mEAAbrC,KAAA,CAAAC,QAAQ,CAACoC,IAAI,GAAA1B,MAAA;QACtBC,WAAW,EAAC,IAAI;QAChB0B,QAAQ,EAAC,MAAM;QACfzB,KAAkB,EAAlB;UAAA;QAAA;+CAEFhB,YAAA,CAAyE0C,oBAAA;QAA9DzB,IAAI,EAAC,SAAS;QAACY,IAAI,EAAC,OAAO;QAAEc,OAAK,EAAEC,QAAA,CAAAC;;0BAAY,MAAErB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;QAG/DxB,YAAA,CASeS,uBAAA;wBARb,MAOC,CAPDT,YAAA,CAOC0C,oBAAA;QANCzB,IAAI,EAAC,SAAS;QACdY,IAAI,EAAC,OAAO;QACXc,OAAK,EAAEC,QAAA,CAAAE,IAAI;QACXC,OAAO,EAAE5C,KAAA,CAAA6C,UAAU;QACpBC,IAAI,EAAC;;0BACJ,MAAGzB,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;;;;yCAIVxB,YAAA,CAoCYkD,oBAAA;gBAnCD/C,KAAA,CAAAgD,aAAa;+DAAbhD,KAAA,CAAAgD,aAAa,GAAArC,MAAA;IACtBsC,KAAK,EAAC,MAAM;IACZ,cAAY,EAAC,sBAAsB;IAClCC,OAAK,EAAEC,IAAA,CAAAC;;sBAER,MAEM,C,4BAFNC,mBAAA,CAEM,cADJA,mBAAA,CAAkC,WAA/B,wBAA2B,E,sBAEhCxD,YAAA,CAsBYyD,oBAAA;MArBVC,MAAM,EAAC,mDAAmD;MAC1D1C,KAAqF,EAArF;QAAA;QAAA;QAAA;QAAA;MAAA,CAAqF;MACrF2C,IAAI,EAAJ,EAAI;MACHC,KAAK,EAAE,CAAC;MACR,YAAU,EAAEhB,QAAA,CAAAiB,aAAa;MACzB,WAAS,EAAEjB,QAAA,CAAAkB,YAAY;MACvB,WAAS,EAAER,IAAA,CAAAS,QAAQ;MACnB,WAAS,EAAEnB,QAAA,CAAAoB,YAAY;MACvB,aAAW,EAAE,KAAK;MACnBC,IAAI,EAAC,MAAM;MACV,WAAS,EAAErB,QAAA,CAAAsB;;wBAEZ,MAA8B1C,MAAA,SAAAA,MAAA,QAA9BgC,mBAAA,CAA8B;QAA3BzD,KAAK,EAAC;MAAgB,4BACzByD,mBAAA,CAA2D;QAAtDzD,KAAK,EAAC;MAAiB,I,iBAAC,aAAW,GAAAyD,mBAAA,CAAa,YAAT,MAAI,E,qBAChDA,mBAAA,CAMM;QANDzD,KAAK,EAAC;MAAgB,IACzByD,mBAAA,CAIO;QAHLxC,KAAwD,EAAxD;UAAA;UAAA;UAAA;QAAA,CAAwD;QACxDjB,KAAK,EAAC,mBAAmB;QACzBoE,EAAE,EAAC;;;2FAITX,mBAAA,CAGO,QAHPY,UAGO,GAFLpE,YAAA,CAA8C0C,oBAAA;MAAlCC,OAAK,EAAEC,QAAA,CAAAyB;IAAU;wBAAE,MAAG7C,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;oCAClCxB,YAAA,CAAgE0C,oBAAA;MAArDzB,IAAI,EAAC,SAAS;MAAE0B,OAAK,EAAEC,QAAA,CAAA0B;;wBAAe,MAAG9C,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E", "ignoreList": []}]}