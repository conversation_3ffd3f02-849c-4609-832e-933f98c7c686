{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue", "mtime": 1741616960745}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "ElMessage", "name", "data", "islogin", "lname", "ishow", "key", "searchDialogVisible", "searchKeyword", "mobileMenuActive", "activePath", "mounted", "sessionStorage", "getItem", "window", "location", "pathname", "methods", "exit", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "href", "catch", "showSearchDialog", "handleSearch", "warning", "toggleMobileMenu"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue"], "sourcesContent": ["<template>\r\n  <div class=\"top-menu\">\r\n    <!-- 顶部信息栏 -->\r\n    <div class=\"top-bar\">\r\n      <div class=\"container\">\r\n        <div class=\"top-bar-content\">\r\n          <div class=\"contact-info\">\r\n            <a href=\"mailto:<EMAIL>\" class=\"contact-item\">\r\n              <i class=\"fas fa-envelope\"></i>\r\n              <span><EMAIL></span>\r\n            </a>\r\n            <a href=\"tel:01066666666\" class=\"contact-item\">\r\n              <i class=\"fas fa-phone\"></i>\r\n              <span>010 6666 6666</span>\r\n            </a>\r\n          </div>\r\n          <div class=\"user-info\">\r\n            <template v-if=\"islogin\">\r\n              <a href=\"/Sreg\" class=\"user-link\">求职者注册</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"/Slogin\" class=\"user-link\">求职者登录</a>\r\n            </template>\r\n            <template v-else>\r\n              <span class=\"welcome-text\">欢迎您：</span>\r\n              <a href=\"/sweclome\" class=\"user-name\">{{ lname }}</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"javascript:void(0);\" @click=\"exit\" class=\"logout-link\">退出登录</a>\r\n            </template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主导航栏 -->\r\n    <div class=\"main-nav\">\r\n      <div class=\"container\">\r\n        <div class=\"nav-content\">\r\n          <!-- Logo -->\r\n          <a href=\"/index\" class=\"logo\">\r\n            <img src=\"../assets/images/main-logo.png\" alt=\"求职系统\" />\r\n          </a>\r\n\r\n          <!-- 移动端菜单按钮 -->\r\n          <div class=\"mobile-toggle\" @click=\"toggleMobileMenu\">\r\n            <span></span>\r\n            <span></span>\r\n            <span></span>\r\n          </div>\r\n\r\n          <!-- 导航菜单 -->\r\n          <div class=\"nav-menu\" :class=\"{ 'active': mobileMenuActive }\">\r\n            <ul class=\"nav-list\">\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/index' }\">\r\n                <a href=\"/index\" class=\"nav-link\">网站首页</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/positionsList' }\">\r\n                <a href=\"/positionsList\" class=\"nav-link\">招聘职位</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/companyList' }\">\r\n                <a href=\"/companyList\" class=\"nav-link\">企业展示</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/bbs' }\">\r\n                <a href=\"/bbs\" class=\"nav-link\">交流论坛</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/ai' }\">\r\n                <a href=\"/ai\" class=\"nav-link\">AI顾问</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/sweclome' }\">\r\n                <a href=\"/sweclome\" class=\"nav-link\">个人中心</a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- 搜索按钮 -->\r\n          <div class=\"search-btn\" @click=\"showSearchDialog\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索对话框 -->\r\n    <el-dialog v-model=\"searchDialogVisible\" title=\"职位搜索\" width=\"30%\" class=\"search-dialog\">\r\n      <el-form>\r\n        <el-form-item>\r\n          <el-input v-model=\"searchKeyword\" placeholder=\"请输入职位关键词\" prefix-icon=\"el-icon-search\"\r\n            @keyup.enter=\"handleSearch\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"searchDialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">搜 索</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../utils/http';\r\n  import '../assets/css/font-awesome.min.css';\r\n  import '../assets/css/qbootstrap.min.css';\r\n  import '../assets/css/qanimate.css';\r\n  import '../assets/css/owl.carousel.min.css';\r\n  import '../assets/css/owl.theme.default.min.css';\r\n  import '../assets/css/qmain.css';\r\n  import { ElMessage } from 'element-plus';\r\n\r\n  export default {\r\n    name: 'TopMenu',\r\n    data() {\r\n      return {\r\n        islogin: true,\r\n        lname: '',\r\n        ishow: false,\r\n        key: '',\r\n        searchDialogVisible: false,\r\n        searchKeyword: '',\r\n        mobileMenuActive: false,\r\n        activePath: ''\r\n      };\r\n    },\r\n    mounted() {\r\n      this.lname = sessionStorage.getItem('lname');\r\n      if (this.lname) {\r\n        this.islogin = false;\r\n      }\r\n      // 获取当前路径\r\n      this.activePath = window.location.pathname;\r\n    },\r\n    methods: {\r\n      exit: function () {\r\n        var _this = this;\r\n        this.$confirm('确认退出吗?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        })\r\n          .then(() => {\r\n            sessionStorage.removeItem('lname');\r\n            location.href = '/index';\r\n          })\r\n          .catch(() => { });\r\n      },\r\n      showSearchDialog() {\r\n        this.searchDialogVisible = true;\r\n      },\r\n      handleSearch() {\r\n        if (!this.searchKeyword) {\r\n          ElMessage.warning('请输入搜索关键词');\r\n          return;\r\n        }\r\n        location.href = '/positionsList?keyword=' + this.searchKeyword;\r\n      },\r\n      toggleMobileMenu() {\r\n        this.mobileMenuActive = !this.mobileMenuActive;\r\n      }\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 整体容器 */\r\n  .top-menu {\r\n    font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\r\n  }\r\n\r\n  /* 顶部信息栏 */\r\n  .top-bar {\r\n    background-color: #f8f9fa;\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .top-bar-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .contact-info {\r\n    display: flex;\r\n  }\r\n\r\n  .contact-item {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #666;\r\n    margin-right: 20px;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .contact-item:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .contact-item i {\r\n    margin-right: 8px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-info {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .user-link,\r\n  .user-name,\r\n  .logout-link {\r\n    color: #666;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .user-link:hover,\r\n  .user-name:hover,\r\n  .logout-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-name {\r\n    color: #3498db;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .welcome-text {\r\n    color: #666;\r\n  }\r\n\r\n  .divider {\r\n    margin: 0 10px;\r\n    color: #ccc;\r\n  }\r\n\r\n  /* 主导航栏 */\r\n  .main-nav {\r\n    background-color: #fff;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n    position: relative;\r\n    z-index: 100;\r\n  }\r\n\r\n  .nav-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 0;\r\n    position: relative;\r\n  }\r\n\r\n  .logo {\r\n    display: block;\r\n    margin-right: 30px;\r\n  }\r\n\r\n  .logo img {\r\n    height: 50px;\r\n    display: block;\r\n  }\r\n\r\n  .mobile-toggle {\r\n    display: none;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    width: 30px;\r\n    height: 22px;\r\n    cursor: pointer;\r\n    z-index: 101;\r\n  }\r\n\r\n  .mobile-toggle span {\r\n    display: block;\r\n    height: 3px;\r\n    width: 100%;\r\n    background-color: #333;\r\n    border-radius: 3px;\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  .nav-menu {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .nav-list {\r\n    display: flex;\r\n    list-style: none;\r\n    margin: 0;\r\n    padding: 0;\r\n  }\r\n\r\n  .nav-item {\r\n    position: relative;\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .nav-link {\r\n    display: block;\r\n    padding: 10px 15px;\r\n    color: #333;\r\n    font-weight: 500;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .nav-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active .nav-link {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -15px;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 3px;\r\n    background-color: #3498db;\r\n  }\r\n\r\n  .search-btn {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f8f9fa;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.3s;\r\n    margin-left: 15px;\r\n  }\r\n\r\n  .search-btn:hover {\r\n    background-color: #e9ecef;\r\n  }\r\n\r\n  .search-btn i {\r\n    color: #666;\r\n    font-size: 18px;\r\n  }\r\n\r\n  /* 搜索对话框 */\r\n  .search-dialog :deep(.el-dialog__header) {\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__body) {\r\n    padding: 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__footer) {\r\n    border-top: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n  @media (max-width: 992px) {\r\n    .mobile-toggle {\r\n      display: flex;\r\n    }\r\n\r\n    .nav-menu {\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100vh;\r\n      background-color: rgba(255, 255, 255, 0.95);\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      align-items: center;\r\n      transform: translateX(-100%);\r\n      transition: transform 0.3s ease;\r\n      z-index: 100;\r\n    }\r\n\r\n    .nav-menu.active {\r\n      transform: translateX(0);\r\n    }\r\n\r\n    .nav-list {\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n\r\n    .nav-item {\r\n      margin: 10px 0;\r\n    }\r\n\r\n    .nav-link {\r\n      font-size: 18px;\r\n      padding: 10px 20px;\r\n    }\r\n\r\n    .nav-item.active::after {\r\n      display: none;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .top-bar-content {\r\n      flex-direction: column;\r\n    }\r\n\r\n    .contact-info {\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .logo img {\r\n      height: 40px;\r\n    }\r\n  }\r\n</style>"], "mappings": "AAoGE,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAChD,OAAO,oCAAoC;AAC3C,OAAO,kCAAkC;AACzC,OAAO,4BAA4B;AACnC,OAAO,oCAAoC;AAC3C,OAAO,yCAAyC;AAChD,OAAO,yBAAyB;AAChC,SAASC,SAAQ,QAAS,cAAc;AAExC,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,EAAE;MACPC,mBAAmB,EAAE,KAAK;MAC1BC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,KAAK;MACvBC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACP,KAAI,GAAIQ,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;IAC5C,IAAI,IAAI,CAACT,KAAK,EAAE;MACd,IAAI,CAACD,OAAM,GAAI,KAAK;IACtB;IACA;IACA,IAAI,CAACO,UAAS,GAAII,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAC5C,CAAC;EACDC,OAAO,EAAE;IACPC,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,IAAIC,KAAI,GAAI,IAAI;MAChB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACVZ,cAAc,CAACa,UAAU,CAAC,OAAO,CAAC;QAClCV,QAAQ,CAACW,IAAG,GAAI,QAAQ;MAC1B,CAAC,EACAC,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IACDC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACrB,mBAAkB,GAAI,IAAI;IACjC,CAAC;IACDsB,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC,IAAI,CAACrB,aAAa,EAAE;QACvBR,SAAS,CAAC8B,OAAO,CAAC,UAAU,CAAC;QAC7B;MACF;MACAf,QAAQ,CAACW,IAAG,GAAI,yBAAwB,GAAI,IAAI,CAAClB,aAAa;IAChE,CAAC;IACDuB,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACtB,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD;EACF;AACF,CAAC", "ignoreList": []}]}