{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1741619105687}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\LeftMenu.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>,CAAC,CAAC,CAAC,<PERSON>AC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IACH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;sBAE5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;oBAIZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;oBAKZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7E,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1E,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3E,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/components/LeftMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"leftside-menu menuitem-active\">\n    <a href=\"\" class=\"logo logo-light\">\n      <span class=\"logo-lg\" style=\"font-size: 18px\"> 求职系统 </span>\n      <span class=\"logo-sm\" style=\"font-size: 18px\"> 求职系统 </span>\n    </a>\n    <a href=\"\" class=\"logo logo-dark\">\n      <span class=\"logo-lg\" style=\"font-size: 18px\"> 求职系统 </span>\n      <span class=\"logo-sm\" style=\"font-size: 18px\"> 求职系统 </span>\n    </a>\n    <div class=\"h-100 show\" id=\"leftside-menu-container\" data-simplebar=\"init\">\n      <div class=\"simplebar-wrapper\" style=\"margin: 0px\">\n        <div class=\"simplebar-height-auto-observer-wrapper\">\n          <div class=\"simplebar-height-auto-observer\"></div>\n        </div>\n        <div class=\"simplebar-mask\">\n          <div class=\"simplebar-offset\" style=\"right: 0px; bottom: 0px\">\n            <div class=\"simplebar-content-wrapper\" tabindex=\"0\" role=\"region\" aria-label=\"scrollable content\"\n              style=\"height: 100%; overflow: hidden\">\n              <div class=\"simplebar-content\" style=\"padding: 0px\">\n                <ul class=\"side-nav\">\n                  <li class=\"side-nav-title\">功能菜单</li>\n\n                  <el-menu style=\"width: 240px\" router unique-opened=\"true\" active-text-color=\"#36A9B0\"\n                    background-color=\"#1A2942\" text-color=\"#70809A\" class=\"el-menu-vertical-demo\" v-if=\"role == '管理员'\">\n                    <el-submenu index=\"8\">\n                      <template #title>\n                        <i class=\"el-icon-reading\"></i>\n                        <span>专业管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/professionalsAdd\"><i class=\"el-icon-position\"></i>添加专业</el-menu-item>\n                        <el-menu-item index=\"/professionalsManage\"><i class=\"el-icon-position\"></i>管理专业</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"6\">\n                      <template #title>\n                        <i class=\"el-icon-user\"></i>\n                        <span>求职者管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/studentsAdd\"><i class=\"el-icon-position\"></i>添加求职者</el-menu-item>\n                        <el-menu-item index=\"/studentsManage\"><i class=\"el-icon-position\"></i>管理求职者</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"7\">\n                      <template #title>\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>企业管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/companyManage2\"><i class=\"el-icon-position\"></i>审核企业</el-menu-item>\n                        <el-menu-item index=\"/companyManage\"><i class=\"el-icon-position\"></i>管理企业</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"5\">\n                      <template #title>\n                        <i class=\"el-icon-collection\"></i>\n                        <span>职位分类管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/jobcategoriesAdd\"><i class=\"el-icon-position\"></i>添加职位分类</el-menu-item>\n                        <el-menu-item index=\"/jobcategoriesManage\"><i class=\"el-icon-position\"></i>管理职位分类</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"2\">\n                      <template #title>\n                        <i class=\"el-icon-suitcase\"></i>\n                        <span>职位管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/positionsManage3\"><i class=\"el-icon-position\"></i>审核职位</el-menu-item>\n                        <el-menu-item index=\"/positionsManage2\"><i class=\"el-icon-position\"></i>管理职位</el-menu-item>\n\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"4\">\n                      <template #title>\n                        <i class=\"el-icon-document\"></i>\n                        <span>简历管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/resumeManage\"><i class=\"el-icon-position\"></i>管理简历</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"10\">\n                      <template #title>\n                        <i class=\"el-icon-message\"></i>\n                        <span>简历投递管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/resumedeliveryManage\"><i\n                            class=\"el-icon-position\"></i>管理简历投递</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"11\"> \n                      <template #title> \n                        <i class=\"el-icon-dish\"></i>\n                        <span>论坛管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/bbsManage\"><i class=\"el-icon-position\"></i>帖子管理</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n\n\n                    <el-submenu index=\"1\">\n                      <template #title>\n                        <i class=\"el-icon-setting\"></i>\n                        <span>系统管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"password\">修改密码</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n                  </el-menu>\n\n                  <el-menu style=\"width: 240px\" router unique-opened=\"true\" active-text-color=\"#36A9B0\"\n                    background-color=\"#1A2942\" text-color=\"#70809A\" class=\"el-menu-vertical-demo\" v-if=\"role == '企业'\">\n                    <el-submenu index=\"2\">\n                      <template #title>\n                        <i class=\"el-icon-suitcase\"></i>\n                        <span>职位管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/positionsAdd\"><i class=\"el-icon-position\"></i>添加职位</el-menu-item>\n                        <el-menu-item index=\"/positionsManage\"><i class=\"el-icon-position\"></i>管理职位</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"10\">\n                      <template #title>\n                        <i class=\"el-icon-notebook-2\"></i>\n                        <span>简历投递管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/resumedeliveryManage2\"><i\n                            class=\"el-icon-position\"></i>简历投递列表</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n\n         \n\n                    <el-submenu index=\"1\">\n                      <template #title>\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>企业中心</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/companyInfo\"><i class=\"el-icon-position\"></i>信息维护</el-menu-item>\n                        <el-menu-item index=\"password\">\n                          <i class=\"el-icon-position\"></i>修改密码\n                        </el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n                  </el-menu>\n                </ul>\n                <div class=\"clearfix\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"simplebar-placeholder\" style=\"width: auto; height: 680px\"></div>\n      </div>\n      <div class=\"simplebar-track simplebar-horizontal\" style=\"visibility: hidden\">\n        <div class=\"simplebar-scrollbar\" style=\"width: 0px; display: none\"></div>\n      </div>\n      <div class=\"simplebar-track simplebar-vertical\" style=\"visibility: hidden\">\n        <div class=\"simplebar-scrollbar\" style=\"height: 0px; display: none\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\n  export default {\n    name: 'LeftMenu',\n    data() {\n      return {\n        userLname: '',\n        role: '',\n      };\n    },\n    mounted() {\n      this.userLname = sessionStorage.getItem('userLname');\n      this.role = sessionStorage.getItem('role');\n    },\n    created() { },\n  };\n</script>\n\n<style scoped></style>"]}]}