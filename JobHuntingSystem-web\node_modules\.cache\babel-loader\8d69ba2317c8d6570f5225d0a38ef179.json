{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsManage.vue", "mtime": 1741615884662}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCByZXF1ZXN0LCB7IGJhc2UgfSBmcm9tICcuLi8uLi8uLi8uLi91dGlscy9odHRwJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdzdHVkZW50cycsCiAgY29tcG9uZW50czoge30sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZpbHRlcnM6IHsKICAgICAgICAvL+WIl+ihqOafpeivouWPguaVsAogICAgICAgIHNubzogJycsCiAgICAgICAgc25hbWU6ICcnLAogICAgICAgIHBob25lOiAnJywKICAgICAgICBwcm9pZDogJycKICAgICAgfSwKICAgICAgcGFnZTogewogICAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICAgIC8vIOW9k+W<PERSON><PERSON>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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "sno", "sname", "phone", "proid", "page", "currentPage", "pageSize", "totalCount", "isClear", "professionalsList", "listLoading", "btnLoading", "datalist", "created", "getDatas", "getprofessionalsList", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "para", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "id", "handleEdit"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsManage.vue"], "sourcesContent": ["<template>\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">求职者管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">求职者列表</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">求职者列表</h4>\r\n      </div>\r\n    </div>\r\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\r\n      <el-form :inline=\"true\" :model=\"filters\">\r\n        <el-form-item>\r\n          <el-input v-model=\"filters.sno\" placeholder=\"账号\" size=\"small\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-input v-model=\"filters.sname\" placeholder=\"姓名\" size=\"small\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-input v-model=\"filters.phone\" placeholder=\"手机号码\" size=\"small\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"专业\" prop=\"proid\">\r\n          <el-select v-model=\"filters.proid\" placeholder=\"请选择\" size=\"small\">\r\n            <el-option label=\"全部\" value=\"\"></el-option>\r\n            <el-option v-for=\"item in professionalsList\" :key=\"item.proid\" :label=\"item.proname\"\r\n              :value=\"item.proid\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-col>\r\n\r\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\r\n      max-height=\"600\" size=\"small\">\r\n      <el-table-column prop=\"sno\" label=\"账号\" align=\"center\"></el-table-column>\r\n      <el-table-column prop=\"password\" label=\"密码\" align=\"center\"></el-table-column>\r\n      <el-table-column prop=\"sname\" label=\"姓名\" align=\"center\"></el-table-column>\r\n      <el-table-column prop=\"gender\" label=\"性别\" align=\"center\"></el-table-column>\r\n      <el-table-column prop=\"age\" label=\"年龄\" align=\"center\"></el-table-column>\r\n      <el-table-column prop=\"phone\" label=\"手机号码\" align=\"center\"></el-table-column>\r\n      <el-table-column prop=\"proname\" label=\"专业\" align=\"center\"></el-table-column>\r\n      <el-table-column prop=\"spic\" label=\"照片\" width=\"70\" align=\"center\">\r\n        <template #default=\"scope\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + scope.row.spic\" style=\"width: 50px; height: 50px\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\r\n        <template #default=\"scope\">\r\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\r\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\r\n          <el-button type=\"success\" size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\" icon=\"el-icon-edit\"\r\n            style=\"padding: 3px 6px 3px 6px\">编辑</el-button>\r\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\r\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n  </div>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../../../utils/http';\r\n  export default {\r\n    name: 'students',\r\n    components: {},\r\n    data() {\r\n      return {\r\n        filters: {\r\n          //列表查询参数\r\n          sno: '',\r\n          sname: '',\r\n          phone: '',\r\n          proid: '',\r\n        },\r\n\r\n        page: {\r\n          currentPage: 1, // 当前页\r\n          pageSize: 10, // 每页显示条目个数\r\n          totalCount: 0, // 总条目数\r\n        },\r\n        isClear: false,\r\n        professionalsList: [], //专业\r\n\r\n        listLoading: false, //列表加载状态\r\n        btnLoading: false, //保存按钮加载状态\r\n        datalist: [], //表格数据\r\n      };\r\n    },\r\n    created() {\r\n      this.getDatas();\r\n      this.getprofessionalsList();\r\n    },\r\n\r\n    methods: {\r\n      // 删除求职者\r\n      handleDelete(index, row) {\r\n        this.$confirm('确认删除该记录吗?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        })\r\n          .then(() => {\r\n            this.listLoading = true;\r\n            let url = base + '/students/del?id=' + row.sno;\r\n            request.post(url).then((res) => {\r\n              this.listLoading = false;\r\n\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success',\r\n                offset: 320,\r\n              });\r\n              this.getDatas();\r\n            });\r\n          })\r\n          .catch(() => { });\r\n      },\r\n\r\n      // 分页\r\n      handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n      },\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n          sno: this.filters.sno,\r\n          sname: this.filters.sname,\r\n          phone: this.filters.phone,\r\n          proid: this.filters.proid,\r\n        };\r\n        this.listLoading = true;\r\n        let url =\r\n          base +\r\n          '/students/list?currentPage=' +\r\n          this.page.currentPage +\r\n          '&pageSize=' +\r\n          this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n          if (res.resdata.length > 0) {\r\n            this.isPage = true;\r\n          } else {\r\n            this.isPage = false;\r\n          }\r\n          this.page.totalCount = res.count;\r\n          this.datalist = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n      //查询\r\n      query() {\r\n        this.getDatas();\r\n      },\r\n\r\n      getprofessionalsList() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + '/professionals/list?currentPage=1&pageSize=1000';\r\n        request.post(url, para).then((res) => {\r\n          this.professionalsList = res.resdata;\r\n        });\r\n      },\r\n\r\n      // 查看\r\n      handleShow(index, row) {\r\n        this.$router.push({\r\n          path: '/StudentsDetail',\r\n          query: {\r\n            id: row.sno,\r\n          },\r\n        });\r\n      },\r\n\r\n      // 编辑\r\n      handleEdit(index, row) {\r\n        this.$router.push({\r\n          path: '/StudentsEdit',\r\n          query: {\r\n            id: row.sno,\r\n          },\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style scoped></style>"], "mappings": ";AAoEE,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE,CAAC,CAAC;EACdC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;QACP;QACAC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACT,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,OAAO,EAAE,KAAK;MACdC,iBAAiB,EAAE,EAAE;MAAE;;MAEvBC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE,CAAE;IAChB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACd,WAAU,GAAI,IAAI;QACvB,IAAIe,GAAE,GAAI9B,IAAG,GAAI,mBAAkB,GAAIwB,GAAG,CAACnB,GAAG;QAC9CN,OAAO,CAACgC,IAAI,CAACD,GAAG,CAAC,CAACD,IAAI,CAAEG,GAAG,IAAK;UAC9B,IAAI,CAACjB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACkB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfN,IAAI,EAAE,SAAS;YACfO,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAChB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAiB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAAC7B,IAAI,CAACC,WAAU,GAAI4B,GAAG;MAC3B,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAIoB,IAAG,GAAI;QACTlC,GAAG,EAAE,IAAI,CAACD,OAAO,CAACC,GAAG;QACrBC,KAAK,EAAE,IAAI,CAACF,OAAO,CAACE,KAAK;QACzBC,KAAK,EAAE,IAAI,CAACH,OAAO,CAACG,KAAK;QACzBC,KAAK,EAAE,IAAI,CAACJ,OAAO,CAACI;MACtB,CAAC;MACD,IAAI,CAACO,WAAU,GAAI,IAAI;MACvB,IAAIe,GAAE,GACJ9B,IAAG,GACH,6BAA4B,GAC5B,IAAI,CAACS,IAAI,CAACC,WAAU,GACpB,YAAW,GACX,IAAI,CAACD,IAAI,CAACE,QAAQ;MACpBZ,OAAO,CAACgC,IAAI,CAACD,GAAG,EAAES,IAAI,CAAC,CAACV,IAAI,CAAEG,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACQ,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAACjC,IAAI,CAACG,UAAS,GAAIoB,GAAG,CAACW,KAAK;QAChC,IAAI,CAAC1B,QAAO,GAAIe,GAAG,CAACQ,OAAO;QAC3B,IAAI,CAACzB,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACD;IACA6B,KAAKA,CAAA,EAAG;MACN,IAAI,CAACzB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,IAAImB,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACxB,WAAU,GAAI,IAAI;MACvB,IAAIe,GAAE,GAAI9B,IAAG,GAAI,iDAAiD;MAClED,OAAO,CAACgC,IAAI,CAACD,GAAG,EAAES,IAAI,CAAC,CAACV,IAAI,CAAEG,GAAG,IAAK;QACpC,IAAI,CAAClB,iBAAgB,GAAIkB,GAAG,CAACQ,OAAO;MACtC,CAAC,CAAC;IACJ,CAAC;IAED;IACAK,UAAUA,CAACtB,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACsB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,iBAAiB;QACvBJ,KAAK,EAAE;UACLK,EAAE,EAAEzB,GAAG,CAACnB;QACV;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACA6C,UAAUA,CAAC3B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACsB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,eAAe;QACrBJ,KAAK,EAAE;UACLK,EAAE,EAAEzB,GAAG,CAACnB;QACV;MACF,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}