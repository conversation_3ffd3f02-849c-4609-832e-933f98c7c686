{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyDetail.vue", "mtime": 1741615257361}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0NvbXBhbnlEZXRhaWwnLAogIGNvbXBvbmVudHM6IHt9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpZDogJycsCiAgICAgIGZvcm1EYXRhOiB7fSAvL+ihqOWNleaVsOaNriAgICAgICAgIAogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7IC8v6I635Y+W5Y+C5pWwCiAgICB0aGlzLmdldERhdGFzKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+iOt+WPluWIl+ihqOaVsOaNrgogICAgZ2V0RGF0YXMoKSB7CiAgICAgIGxldCBwYXJhID0ge307CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOwogICAgICBsZXQgdXJsID0gYmFzZSArICIvY29tcGFueS9nZXQ/aWQ9IiArIHRoaXMuaWQ7CiAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHBhcmEpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmZvcm1EYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyZXMucmVzZGF0YSkpOwogICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6L+U5ZueCiAgICBiYWNrKCkgewogICAgICAvL+i/lOWbnuS4iuS4gOmhtQogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "formData", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "back", "$router", "go"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyDetail.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">企业详情</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">企业详情</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n      <el-form-item label=\"企业id\">\r\n        {{ formData.cid }}</el-form-item>\r\n      <el-form-item label=\"企业账号\">\r\n        {{ formData.clname }}</el-form-item>\r\n      <el-form-item label=\"登录密码\">\r\n        {{ formData.password }}</el-form-item>\r\n      <el-form-item label=\"企业名称\">\r\n        {{ formData.comname }}</el-form-item>\r\n      <el-form-item label=\"企业规模\">\r\n        {{ formData.scale }}</el-form-item>\r\n      <el-form-item label=\"企业性质\">\r\n        {{ formData.nature }}</el-form-item>\r\n      <el-form-item label=\"联系方式\">\r\n        {{ formData.contact }}</el-form-item>\r\n      <el-form-item label=\"联系地址\">\r\n        {{ formData.address }}</el-form-item>\r\n      <el-form-item label=\"企业logo\" prop=\"logo\">\r\n        <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.logo\" style=\"width: 150px;height: 150px\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"企业介绍\" prop=\"introduction\">\r\n        <div v-html=\"formData.introduction\"></div>\r\n      </el-form-item>\r\n      <el-form-item label=\"注册时间\">\r\n        {{ formData.addtime }}</el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\n\r\n  import request, { base } from \"../../../../utils/http\";\r\n  export default {\r\n    name: 'CompanyDetail',\r\n    components: {\r\n    },\r\n    data() {\r\n      return {\r\n        id: '',\r\n        formData: {}, //表单数据         \r\n\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id; //获取参数\r\n      this.getDatas();\r\n    },\r\n\r\n\r\n    methods: {\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/company/get?id=\" + this.id;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 返回\r\n      back() {\r\n        //返回上一页\r\n        this.$router.go(-1);\r\n      },\r\n\r\n    },\r\n  }\r\n\r\n</script>\r\n<style scoped></style>"], "mappings": "AAkDE,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE,CACZ,CAAC;EACDC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,QAAQ,EAAE,CAAC,CAAC,CAAE;IAEhB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACF,EAAC,GAAI,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,EAAE,EAAE;IAChC,IAAI,CAACK,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAEP;IACAD,QAAQA,CAAA,EAAG;MACT,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIb,IAAG,GAAI,kBAAiB,GAAI,IAAI,CAACI,EAAE;MAC7CL,OAAO,CAACe,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACX,QAAO,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IAED;IACAS,IAAIA,CAAA,EAAG;MACL;MACA,IAAI,CAACC,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB;EAEF;AACF", "ignoreList": []}]}