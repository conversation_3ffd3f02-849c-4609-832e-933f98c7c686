{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\router\\index.js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\router\\index.js", "mtime": 1741618309111}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["createRouter", "createWebHistory", "routes", "path", "name", "component", "meta", "requireAuth", "redirect", "children", "title", "requiresAuth", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "sessionStorage", "removeItem", "currentUser", "getItem", "console", "log"], "sources": ["I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router';\r\n\r\nconst routes = [\r\n  {\r\n    path: '/login',\r\n    name: 'Login',\r\n    component: () => import('../views/Login'),\r\n    meta: {\r\n      requireAuth: false,\r\n    },\r\n  },\r\n\r\n  {\r\n    path: '/main',\r\n    name: 'Main',\r\n    component: () => import('../views/Main'),\r\n    redirect: '/home',\r\n    children: [\r\n      {\r\n        path: '/home',\r\n        name: 'Home',\r\n        component: () => import('../views/admin/Home'),\r\n        meta: {\r\n          requireAuth: true,\r\n          title: '首页',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/positionsAdd',\r\n        name: 'PositionsAdd',\r\n        component: () => import('../views/admin/positions/PositionsAdd'),\r\n        meta: { requiresAuth: true, title: '职位添加' },\r\n      },\r\n      {\r\n        path: '/positionsEdit',\r\n        name: 'PositionsEdit',\r\n        component: () => import('../views/admin/positions/PositionsEdit'),\r\n        meta: { requiresAuth: true, title: '职位修改' },\r\n      },\r\n      {\r\n        path: '/positionsManage',\r\n        name: 'PositionsManage',\r\n        component: () => import('../views/admin/positions/PositionsManage'),\r\n        meta: { requiresAuth: true, title: '职位管理' },\r\n      },\r\n      {\r\n        path: '/positionsManage2',\r\n        name: 'PositionsManage2',\r\n        component: () => import('../views/admin/positions/PositionsManage2'),\r\n        meta: { requiresAuth: true, title: '管理职位' },\r\n      },\r\n      {\r\n        path: '/positionsManage3',\r\n        name: 'PositionsManage3',\r\n        component: () => import('../views/admin/positions/PositionsManage3'),\r\n        meta: { requiresAuth: true, title: '审核职位' },\r\n      },\r\n\r\n      {\r\n        path: '/positionsDetail',\r\n        name: 'PositionsDetail',\r\n        component: () => import('../views/admin/positions/PositionsDetail'),\r\n        meta: { requiresAuth: true, title: '职位详情' },\r\n      },\r\n\r\n      {\r\n        path: '/resumeManage',\r\n        name: 'ResumeManage',\r\n        component: () => import('../views/admin/resume/ResumeManage'),\r\n        meta: { requiresAuth: true, title: '简历管理' },\r\n      },\r\n      {\r\n        path: '/resumeDetail',\r\n        name: 'ResumeDetail',\r\n        component: () => import('../views/admin/resume/ResumeDetail'),\r\n        meta: { requiresAuth: true, title: '简历详情' },\r\n      },\r\n      {\r\n        path: '/jobcategoriesAdd',\r\n        name: 'JobcategoriesAdd',\r\n        component: () => import('../views/admin/jobcategories/JobcategoriesAdd'),\r\n        meta: { requiresAuth: true, title: '职位分类添加' },\r\n      },\r\n      {\r\n        path: '/jobcategoriesEdit',\r\n        name: 'JobcategoriesEdit',\r\n        component: () => import('../views/admin/jobcategories/JobcategoriesEdit'),\r\n        meta: { requiresAuth: true, title: '职位分类修改' },\r\n      },\r\n      {\r\n        path: '/jobcategoriesManage',\r\n        name: 'JobcategoriesManage',\r\n        component: () => import('../views/admin/jobcategories/JobcategoriesManage'),\r\n        meta: { requiresAuth: true, title: '职位分类管理' },\r\n      },\r\n      {\r\n        path: '/studentsAdd',\r\n        name: 'StudentsAdd',\r\n        component: () => import('../views/admin/students/StudentsAdd'),\r\n        meta: { requiresAuth: true, title: '求职者添加' },\r\n      },\r\n      {\r\n        path: '/studentsEdit',\r\n        name: 'StudentsEdit',\r\n        component: () => import('../views/admin/students/StudentsEdit'),\r\n        meta: { requiresAuth: true, title: '求职者修改' },\r\n      },\r\n      {\r\n        path: '/studentsManage',\r\n        name: 'StudentsManage',\r\n        component: () => import('../views/admin/students/StudentsManage'),\r\n        meta: { requiresAuth: true, title: '求职者管理' },\r\n      },\r\n      {\r\n        path: '/studentsDetail',\r\n        name: 'StudentsDetail',\r\n        component: () => import('../views/admin/students/StudentsDetail'),\r\n        meta: { requiresAuth: true, title: '求职者详情' },\r\n      },\r\n\r\n      {\r\n        path: '/companyEdit',\r\n        name: 'CompanyEdit',\r\n        component: () => import('../views/admin/company/CompanyEdit'),\r\n        meta: { requiresAuth: true, title: '企业修改' },\r\n      },\r\n      {\r\n        path: '/companyManage',\r\n        name: 'CompanyManage',\r\n        component: () => import('../views/admin/company/CompanyManage'),\r\n        meta: { requiresAuth: true, title: '企业管理' },\r\n      },\r\n      {\r\n        path: '/companyManage2',\r\n        name: 'CompanyManage2',\r\n        component: () => import('../views/admin/company/CompanyManage2'),\r\n        meta: { requiresAuth: true, title: '审核企业' },\r\n      },\r\n\r\n      {\r\n        path: '/companyDetail',\r\n        name: 'CompanyDetail',\r\n        component: () => import('../views/admin/company/CompanyDetail'),\r\n        meta: { requiresAuth: true, title: '企业详情' },\r\n      },\r\n\r\n      {\r\n        path: '/companyInfo',\r\n        name: 'CompanyInfo',\r\n        component: () => import('../views/admin/company/CompanyInfo'),\r\n        meta: { requiresAuth: true, title: '信息维护' },\r\n      },\r\n      {\r\n        path: '/professionalsAdd',\r\n        name: 'ProfessionalsAdd',\r\n        component: () => import('../views/admin/professionals/ProfessionalsAdd'),\r\n        meta: { requiresAuth: true, title: '专业添加' },\r\n      },\r\n      {\r\n        path: '/professionalsEdit',\r\n        name: 'ProfessionalsEdit',\r\n        component: () => import('../views/admin/professionals/ProfessionalsEdit'),\r\n        meta: { requiresAuth: true, title: '专业修改' },\r\n      },\r\n      {\r\n        path: '/professionalsManage',\r\n        name: 'ProfessionalsManage',\r\n        component: () => import('../views/admin/professionals/ProfessionalsManage'),\r\n        meta: { requiresAuth: true, title: '专业管理' },\r\n      },\r\n      {\r\n        path: '/resumedeliveryManage',\r\n        name: 'ResumedeliveryManage',\r\n        component: () => import('../views/admin/resumedelivery/ResumedeliveryManage'),\r\n        meta: { requiresAuth: true, title: '简历投递管理' },\r\n      },\r\n      {\r\n        path: '/resumedeliveryManage2',\r\n        name: 'ResumedeliveryManage2',\r\n        component: () => import('../views/admin/resumedelivery/ResumedeliveryManage2'),\r\n        meta: { requiresAuth: true, title: '简历投递列表' },\r\n      },\r\n      {\r\n        path: '/resumedeliveryDetail',\r\n        name: 'ResumedeliveryDetail',\r\n        component: () => import('../views/admin/resumedelivery/ResumedeliveryDetail'),\r\n        meta: { requiresAuth: true, title: '简历投递详情' },\r\n      },\r\n      {\r\n        path: '/bbsManage',\r\n        name: 'BbsManage',\r\n        component: () => import('../views/admin/bbs/BbsManage'),\r\n        meta: { requiresAuth: true, title: '帖子管理' },\r\n      },\r\n      {\r\n        path: '/bbsDetail',\r\n        name: 'BbsDetail',\r\n        component: () => import('../views/admin/bbs/BbsDetail'),\r\n        meta: { requiresAuth: true, title: '帖子详情' },\r\n      },\r\n  \r\n      {\r\n        path: '/password',\r\n        name: 'Password',\r\n        component: () => import('../views/admin/system/Password'),\r\n        meta: {\r\n          requireAuth: true,\r\n          title: '修改密码',\r\n        },\r\n      },\r\n    ],\r\n  },\r\n\r\n  {\r\n    path: '/',\r\n    name: '/',\r\n    component: () => import('../views/Index'),\r\n    redirect: '/default',\r\n    children: [\r\n      {\r\n        path: '/default',\r\n        name: 'Default',\r\n        component: () => import('../views/web/Default'),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '/index',\r\n    name: 'Index',\r\n    component: () => import('../views/Index'),\r\n    redirect: '/default',\r\n    children: [\r\n      {\r\n        path: '/default',\r\n        name: 'Default',\r\n        component: () => import('../views/web/Default'),\r\n      },\r\n      {\r\n        path: '/resume_manage',\r\n        name: 'Resume_Manage',\r\n        component: () => import('../views/web/Resume_Manage'),\r\n        meta: {\r\n          title: '简历管理',\r\n        },\r\n      },\r\n      {\r\n        path: '/resume_add',\r\n        name: 'Resume_Add',\r\n        component: () => import('../views/web/Resume_Add'),\r\n        meta: {\r\n          title: '添加简历',\r\n        },\r\n      },\r\n      {\r\n        path: '/resume_edit',\r\n        name: 'Resume_Edit',\r\n        component: () => import('../views/web/Resume_Edit'),\r\n        meta: {\r\n          title: '编辑简历',\r\n        },\r\n      },\r\n      {\r\n        path: '/resume_preview',\r\n        name: 'ResumePreview',\r\n        component: () => import('../views/web/ResumePreview'),\r\n        meta: {\r\n          title: '简历预览',\r\n        },\r\n      },\r\n      {\r\n        path: '/sinfo',\r\n        name: 'Sinfo',\r\n        component: () => import('../views/web/Sinfo'),\r\n        meta: {\r\n          title: '个人信息',\r\n        },\r\n      },\r\n      {\r\n        path: '/sweclome',\r\n        name: 'Sweclome',\r\n        component: () => import('../views/web/Sweclome'),\r\n        meta: {\r\n          title: '欢迎页面',\r\n        },\r\n      },\r\n      {\r\n        path: '/spassword',\r\n        name: 'Spassword',\r\n        component: () => import('../views/web/Spassword'),\r\n        meta: {\r\n          title: '修改密码',\r\n        },\r\n      },\r\n      {\r\n        path: '/resumedelivery_manage',\r\n        name: 'Resumedelivery_Manage',\r\n        component: () => import('../views/web/Resumedelivery_Manage'),\r\n        meta: {\r\n          title: '投递记录',\r\n        },\r\n      },\r\n      {\r\n        path: '/browsingrecords_manage',\r\n        name: 'Browsingrecords_Manage',\r\n        component: () => import('../views/web/Browsingrecords_Manage'),\r\n        meta: {\r\n          title: '浏览记录',\r\n        },\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '/web',\r\n    name: 'web',\r\n    component: () => import('../views/web/Leftnav'),\r\n    children: [\r\n      {\r\n        path: '/sreg',\r\n        name: 'Sreg',\r\n        component: () => import('../views/web/Sreg'),\r\n        meta: {\r\n          title: '求职者注册',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/slogin',\r\n        name: 'Slogin',\r\n        component: () => import('../views/web/Slogin'),\r\n        meta: {\r\n          title: '求职者登录',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/companylist',\r\n        name: 'CompanyList',\r\n        component: () => import('../views/web/CompanyList'),\r\n        meta: {\r\n          title: '企业展示',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/positionslist',\r\n        name: 'PositionsList',\r\n        component: () => import('../views/web/PositionsList'),\r\n        meta: {\r\n          title: '职位列表',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/companyview',\r\n        name: 'CompanyView',\r\n        component: () => import('../views/web/CompanyView'),\r\n        meta: {\r\n          title: '企业详情',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/positionsview',\r\n        name: 'PositionsView',\r\n        component: () => import('../views/web/PositionsView'),\r\n        meta: {\r\n          title: '职位详情',\r\n        },\r\n      },\r\n\r\n\r\n      {\r\n        path: '/bbs',\r\n        name: 'Bbs',\r\n        component: () => import('../views/web/Bbs'),\r\n        meta: {\r\n          title: '帖子列表',\r\n        }\r\n      },\r\n\r\n      {\r\n        path: '/bbsview',\r\n        name: 'BbsView',\r\n        component: () => import('../views/web/BbsView'),\r\n        meta: {\r\n          title: '帖子详情',\r\n        }\r\n      },\r\n\r\n      {\r\n        path: '/ai',\r\n        name: 'Ai',\r\n        component: () => import('../views/web/Ai'),\r\n        meta: {\r\n          title: 'AI顾问',\r\n        }\r\n      },\r\n      \r\n    ],\r\n  },\r\n\r\n  {\r\n    path: '/menunav',\r\n    name: 'Menunav',\r\n    component: () => import('../views/web/Menunav'),\r\n    children: [\r\n      {\r\n        path: '/sweclome',\r\n        name: 'Sweclome',\r\n        component: () => import('../views/web/Sweclome'),\r\n        meta: {\r\n          title: '欢迎页面',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/sinfo',\r\n        name: 'Sinfo',\r\n        component: () => import('../views/web/Sinfo'),\r\n        meta: {\r\n          title: '修改个人信息',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/spassword',\r\n        name: 'Spassword',\r\n        component: () => import('../views/web/Spassword'),\r\n        meta: {\r\n          title: '修改密码',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/resume_add',\r\n        name: 'Resume_Add',\r\n        component: () => import('../views/web/Resume_Add'),\r\n        meta: {\r\n          title: '创建简历',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/resume_manage',\r\n        name: 'Resume_Manage',\r\n        component: () => import('../views/web/Resume_Manage'),\r\n        meta: {\r\n          title: '简历管理',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/resume_edit',\r\n        name: 'Resume_Edit',\r\n        component: () => import('../views/web/Resume_Edit'),\r\n        meta: {\r\n          title: '简历编辑',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/resumedelivery_manage',\r\n        name: 'Resumedelivery_Manage',\r\n        component: () => import('../views/web/Resumedelivery_Manage'),\r\n        meta: {\r\n          title: '我的投递记录',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/resumedelivery_show',\r\n        name: 'Resumedelivery_Show',\r\n        component: () => import('../views/web/Resumedelivery_Show'),\r\n        meta: {\r\n          title: '简历投递详情',\r\n        },\r\n      },\r\n\r\n \r\n\r\n\r\n      {\r\n        path: '/browsingrecords_manage',\r\n        name: 'Browsingrecords_Manage',\r\n        component: () => import('../views/web/Browsingrecords_Manage'),\r\n        meta: {\r\n          title: '我的浏览记录',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/resume_preview',\r\n        name: 'ResumePreview',\r\n        component: () => import('../views/web/ResumePreview'),\r\n        meta: {\r\n          title: '简历预览',\r\n        },\r\n      },\r\n\r\n      {\r\n        path: '/bbs_manage',\r\n        name: 'Bbs_Manage',\r\n        component: () => import('../views/web/Bbs_Manage'),\r\n        meta: {\r\n          title: '我的帖子',\r\n        }\r\n      },\r\n\r\n      {\r\n        path: '/bbs_edit',\r\n        name: 'Bbs_Edit',\r\n        component: () => import('../views/web/Bbs_Edit'),\r\n        meta: {\r\n          title: '帖子编辑',\r\n        }\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(process.env.BASE_URL),\r\n  routes,\r\n});\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n  if (to.path == '/') {\r\n    sessionStorage.removeItem('userLname');\r\n    sessionStorage.removeItem('role');\r\n  }\r\n  let currentUser = sessionStorage.getItem('userLname');\r\n  console.log(to + '  to.meta.requireAuth');\r\n\r\n  if (to.meta.requireAuth) {\r\n    if (!currentUser && to.path != '/login') {\r\n      next({ path: '/login' });\r\n    } else {\r\n      next();\r\n    }\r\n  } else {\r\n    next();\r\n  }\r\n});\r\n\r\nexport default router;\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAE3D,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC;EACzCC,IAAI,EAAE;IACJC,WAAW,EAAE;EACf;AACF,CAAC,EAED;EACEJ,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC;EACxCG,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC;IAC9CC,IAAI,EAAE;MACJC,WAAW,EAAE,IAAI;MACjBG,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC;IAChEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC;IACjEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC;IACnEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC;IACpEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC;IACpEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EAED;IACEP,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC;IACnEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EAED;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC;IACxEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC;IACzEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD,CAAC;IAC3EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC;IAC9DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAQ;EAC7C,CAAC,EACD;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;IAC/DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAQ;EAC7C,CAAC,EACD;IACEP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC;IACjEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAQ;EAC7C,CAAC,EACD;IACEP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC;IACjEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAQ;EAC7C,CAAC,EAED;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;IAC/DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC;IAChEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EAED;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;IAC/DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EAED;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC;IACxEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC;IACzEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD,CAAC;IAC3EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oDAAoD,CAAC;IAC7EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qDAAqD,CAAC;IAC9EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oDAAoD,CAAC;IAC7EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;IACvDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;IACvDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EAED;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MACJC,WAAW,EAAE,IAAI;MACjBG,KAAK,EAAE;IACT;EACF,CAAC;AAEL,CAAC,EAED;EACEP,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,GAAG;EACTC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC;EACzCG,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB;EAChD,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC;EACzCG,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB;EAChD,CAAC,EACD;IACEF,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEP,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC;IAClDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;IACnDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC;IAC7CC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IAChDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;IACjDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEP,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEP,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,wBAAwB;IAC9BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC;IAC9DC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC;AAEL,CAAC,EACD;EACEP,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;EAC/CI,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC;IAC5CC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC;IAC9CC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;IACnDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;IACnDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAGD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC;IAC3CC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;IAC/CC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC;IAC1CC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC;AAGL,CAAC,EAED;EACEP,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;EAC/CI,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IAChDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC;IAC7CC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;IACjDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC;IAClDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;IACnDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAKD;IACEP,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,wBAAwB;IAC9BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC;IAC9DC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC;IAClDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC,EAED;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IAChDC,IAAI,EAAE;MACJI,KAAK,EAAE;IACT;EACF,CAAC;AAEL,CAAC,CACF;AAED,MAAME,MAAM,GAAGZ,YAAY,CAAC;EAC1Ba,OAAO,EAAEZ,gBAAgB,CAACa,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/Cd;AACF,CAAC,CAAC;AAEFU,MAAM,CAACK,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,IAAIF,EAAE,CAACf,IAAI,IAAI,GAAG,EAAE;IAClBkB,cAAc,CAACC,UAAU,CAAC,WAAW,CAAC;IACtCD,cAAc,CAACC,UAAU,CAAC,MAAM,CAAC;EACnC;EACA,IAAIC,WAAW,GAAGF,cAAc,CAACG,OAAO,CAAC,WAAW,CAAC;EACrDC,OAAO,CAACC,GAAG,CAACR,EAAE,GAAG,uBAAuB,CAAC;EAEzC,IAAIA,EAAE,CAACZ,IAAI,CAACC,WAAW,EAAE;IACvB,IAAI,CAACgB,WAAW,IAAIL,EAAE,CAACf,IAAI,IAAI,QAAQ,EAAE;MACvCiB,IAAI,CAAC;QAAEjB,IAAI,EAAE;MAAS,CAAC,CAAC;IAC1B,CAAC,MAAM;MACLiB,IAAI,CAAC,CAAC;IACR;EACF,CAAC,MAAM;IACLA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeR,MAAM", "ignoreList": []}]}