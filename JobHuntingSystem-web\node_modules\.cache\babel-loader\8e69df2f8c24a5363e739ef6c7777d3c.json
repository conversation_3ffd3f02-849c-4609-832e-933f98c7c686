{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs.vue", "mtime": 1741614414000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "page", "currentPage", "pageSize", "totalCount", "bbslist", "formData", "rules", "btitle", "required", "message", "trigger", "bdetail", "created", "keyword", "$route", "query", "getDatas", "methods", "handleCurrentChange", "val", "para", "listLoading", "url", "post", "then", "res", "resdata", "length", "isPage", "count", "save", "lname", "sessionStorage", "getItem", "$message", "type", "$refs", "formDataRef", "validate", "valid", "sno", "btotal", "btnLoading", "code", "$nextTick", "editor", "txt", "html", "msg", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs.vue"], "sourcesContent": ["<template>\r\n  <table style=\"width: 100%;line-height:32px;font-size:13px\" class=\"bbs\" v-for=\"item in bbslist\" :key=\"item.bid\">\r\n    <tr>\r\n      <td>\r\n        <a :href=\"'bbsView?id=' + item.bid\">{{ item.btitle }}\r\n        </a>\r\n      </td>\r\n      <td width=\"400\">\r\n        <span style=\"float:right;\">\r\n          {{ item.addtime }}\r\n        </span>\r\n        <span class=\"cu-tag line-red\" style=\"height:28px;float:right;  margin-right: 10px;\">点击量：{{\r\n            item.btotal\r\n          }}</span>\r\n        <span class=\"cu-tag line-green light\" style=\"float:right; margin-right: 10px; height:28px; line-height:28px \">\r\n          发布人：{{ item.sno }}\r\n        </span>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n\r\n  <div style=\"width: 100%;display: inline-table;\">\r\n    <el-pagination @current-change=\"handleCurrentChange\"\r\n                   :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" background\r\n                   layout=\"total, prev, pager, next, jumper\"\r\n                   :total=\"page.totalCount\" style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n  </div>\r\n\r\n\r\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\r\n    <el-form-item label=\"帖子标题\" prop=\"btitle\">\r\n      <el-input v-model=\"formData.btitle\" placeholder=\"帖子标题\" style=\"width:50%;\"></el-input>\r\n    </el-form-item>\r\n    <el-form-item label=\"帖子内容\" prop=\"bdetail\">\r\n      <WangEditor ref=\"wangEditorRef\" v-model=\"formData.bdetail\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n                  @change=\"editorChange\" height=\"200\"></WangEditor>\r\n    </el-form-item>\r\n    <el-form-item label=\"\">\r\n      <el-button type=\"primary\" @click=\"save\" :loading=\"btnLoading\">提 交</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport request, {base} from \"../../../utils/http\";\r\nimport WangEditor from \"../../components/WangEditor\";\r\n\r\nexport default {\r\n  name: \"bbs\",\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 15, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n      },\r\n      bbslist: \"\",\r\n\r\n      formData: {}, //表单数据\r\n      rules: {\r\n        btitle: [\r\n          {required: true, message: \"请输入帖子标题\", trigger: \"blur\"},\r\n        ],\r\n        bdetail: [\r\n          {required: true, message: \"请输入帖子内容\", trigger: \"blur\"},\r\n        ],\r\n      },\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.keyword = this.$route.query.keyword;\r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 分页\r\n    handleCurrentChange(val) {\r\n      this.page.currentPage = val;\r\n\r\n      this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {      \r\n\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/bbs/list?currentPage=\" + this.page.currentPage + \"&pageSize=\" + this.page.pageSize;\r\n      request.post(url, para).then((res) => {\r\n        if (res.resdata.length > 0) {\r\n          this.isPage = true;\r\n        } else {\r\n          this.isPage = false;\r\n        }\r\n        this.page.totalCount = res.count;\r\n        this.bbslist = res.resdata;\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n\r\n    //保存\r\n    save() {\r\n\r\n      var lname = sessionStorage.getItem(\"lname\");\r\n      if (lname == null) {\r\n        this.$message({\r\n          message: \"请先登录\",\r\n          type: \"error\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs.formDataRef.validate((valid) => {\r\n        if (valid) {\r\n          let para = {\r\n            bdetail: this.formData.bdetail,\r\n            btitle: this.formData.btitle,\r\n            sno: lname,\r\n            btotal: 0,\r\n          };\r\n          this.btnLoading = true;\r\n          let url = base + \"/bbs/add\";\r\n          request.post(url, para).then((res) => {\r\n            this.btnLoading = false;\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: \"提交成功\",\r\n                type: \"success\",\r\n              });\r\n              this.formData = {};\r\n\r\n              //设置富文本编辑器内容\r\n              this.$nextTick(() => {\r\n                this.$refs[\"wangEditorRef\"].editor.txt.html(\"\");\r\n              });\r\n\r\n              this.getDatas();\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.bdetail = val;\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 200px;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": "AA4CA,OAAOA,OAAO,IAAGC,IAAI,QAAO,qBAAqB;AACjD,OAAOC,UAAS,MAAO,6BAA6B;AAEpD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,OAAO,EAAE,EAAE;MAEXC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,KAAK,EAAE;QACLC,MAAM,EAAE,CACN;UAACC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CACtD;QACDC,OAAO,EAAE,CACP;UAACH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC;MAEzD;IAEF,CAAC;EACH,CAAC;EACDE,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,OAAM,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,OAAO;IACxC,IAAI,CAACG,QAAQ,CAAC,CAAC;EAEjB,CAAC;EACDC,OAAO,EAAE;IAEP;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAACnB,IAAI,CAACC,WAAU,GAAIkB,GAAG;MAE3B,IAAI,CAACH,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAII,IAAG,GAAI,CAEX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAI3B,IAAG,GAAI,wBAAuB,GAAI,IAAI,CAACK,IAAI,CAACC,WAAU,GAAI,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MACrGR,OAAO,CAAC6B,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAAC5B,IAAI,CAACG,UAAS,GAAIsB,GAAG,CAACI,KAAK;QAChC,IAAI,CAACzB,OAAM,GAAIqB,GAAG,CAACC,OAAO;QAC1B,IAAI,CAACL,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IAED;IACAS,IAAIA,CAAA,EAAG;MAEL,IAAIC,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAI,IAAK,IAAI,EAAE;QACjB,IAAI,CAACG,QAAQ,CAAC;UACZzB,OAAO,EAAE,MAAM;UACf0B,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF;MAEA,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,QAAQ,CAAEC,KAAK,IAAK;QACzC,IAAIA,KAAK,EAAE;UACT,IAAInB,IAAG,GAAI;YACTT,OAAO,EAAE,IAAI,CAACN,QAAQ,CAACM,OAAO;YAC9BJ,MAAM,EAAE,IAAI,CAACF,QAAQ,CAACE,MAAM;YAC5BiC,GAAG,EAAET,KAAK;YACVU,MAAM,EAAE;UACV,CAAC;UACD,IAAI,CAACC,UAAS,GAAI,IAAI;UACtB,IAAIpB,GAAE,GAAI3B,IAAG,GAAI,UAAU;UAC3BD,OAAO,CAAC6B,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;YACpC,IAAI,CAACiB,UAAS,GAAI,KAAK;YACvB,IAAIjB,GAAG,CAACkB,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACT,QAAQ,CAAC;gBACZzB,OAAO,EAAE,MAAM;gBACf0B,IAAI,EAAE;cACR,CAAC,CAAC;cACF,IAAI,CAAC9B,QAAO,GAAI,CAAC,CAAC;;cAElB;cACA,IAAI,CAACuC,SAAS,CAAC,MAAM;gBACnB,IAAI,CAACR,KAAK,CAAC,eAAe,CAAC,CAACS,MAAM,CAACC,GAAG,CAACC,IAAI,CAAC,EAAE,CAAC;cACjD,CAAC,CAAC;cAEF,IAAI,CAAC/B,QAAQ,CAAC,CAAC;YACjB,OAAO;cACL,IAAI,CAACkB,QAAQ,CAAC;gBACZzB,OAAO,EAAEgB,GAAG,CAACuB,GAAG;gBAChBb,IAAI,EAAE;cACR,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,OAAO;UACL,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAc,YAAYA,CAAC9B,GAAG,EAAE;MAChB,IAAI,CAACd,QAAQ,CAACM,OAAM,GAAIQ,GAAG;IAC7B;EAEF;AACF,CAAC", "ignoreList": []}]}