{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Slogin.vue?vue&type=template&id=a95ee1aa", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Slogin.vue", "mtime": 1741614867096}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICAgPGVsLWZvcm0gOm1vZGVsPSJmb3JtRGF0YSIgbGFiZWwtd2lkdGg9IjIwJSIgcmVmPSJmb3JtRGF0YVJlZiIgOnJ1bGVzPSJhZGRydWxlcyIgIGFsaWduPSJsZWZ0Ij4NCjxlbC1mb3JtLWl0ZW0gbGFiZWw9Iui0puWPtyIgcHJvcD0ic25vIj4NCjxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtRGF0YS5zbm8iIHBsYWNlaG9sZGVyPSLotKblj7ciICBzdHlsZT0id2lkdGg6NTAlOyIgPjwvZWwtaW5wdXQ+DQo8L2VsLWZvcm0taXRlbT4NCjxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWvhueggSIgcHJvcD0icGFzc3dvcmQiPg0KPGVsLWlucHV0IHR5cGU9InBhc3N3b3JkIiB2LW1vZGVsPSJmb3JtRGF0YS5wYXNzd29yZCIgcGxhY2Vob2xkZXI9IuWvhueggSIgIHN0eWxlPSJ3aWR0aDo1MCU7IiA+PC9lbC1pbnB1dD4NCjwvZWwtZm9ybS1pdGVtPg0KPGVsLWZvcm0taXRlbT4NCjxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgIHNpemU9InNtYWxsIiBAY2xpY2s9ImxvZ2luIiA6bG9hZGluZz0iYnRuTG9hZGluZyIgICBpY29uPSJlbC1pY29uLXVwbG9hZCIgPueZuyDlvZU8L2VsLWJ1dHRvbj4NCjwvZWwtZm9ybS1pdGVtPg0KPC9lbC1mb3JtPg0KDQoNCg=="}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Slogin.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Slogin.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"账号\" prop=\"sno\">\r\n<el-input v-model=\"formData.sno\" placeholder=\"账号\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"密码\" prop=\"password\">\r\n<el-input type=\"password\" v-model=\"formData.password\" placeholder=\"密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\"  size=\"small\" @click=\"login\" :loading=\"btnLoading\"   icon=\"el-icon-upload\" >登 录</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"Slogin\",\r\n  data() {\r\n    return {\r\n      formData:{},\r\n\r\n      addrules: {\r\n          sno: [{ required: true, message: '请输入账号', trigger: 'blur' }],\r\n          password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\r\n\r\n      },\r\n\r\n\r\n      btnLoading: false, //按钮是否在加载中\r\n\r\n    };\r\n  },\r\n  created() {\r\n    \r\n  },\r\n  methods: {  \r\n    //登录\r\nlogin() {\r\n    //表单验证\r\n    this.$refs[\"formDataRef\"].validate((valid) => {\r\n\r\n        if (valid) {\r\n            let url = base + \"/students/login\"; //请求地址\r\n            this.btnLoading = true; //按钮加载状态\r\n            request.post(url, this.formData).then((res) => { //请求接口             \r\n                if (res.code == 200) {\r\n                    this.$message({\r\n                        message: \"登录成功\",\r\n                        type: \"success\",\r\n                        offset: 320,\r\n                    });                   \r\n                    sessionStorage.setItem(\"lname\", this.formData.sno); //保存用户信息\r\n                    this.$router.push(\"/sweclome\"); //跳转到个人中心首页\r\n                }\r\n                else if (res.code == 201) {\r\n                    this.$message({\r\n                        message: res.msg,\r\n                        type: \"error\",\r\n                        offset: 320,\r\n                    });\r\n                    this.btnLoading = false;\r\n                }\r\n                else {\r\n                    this.$message({\r\n                        message: \"服务器错误\",\r\n                        type: \"error\",\r\n                        offset: 320,\r\n                    });\r\n                    this.btnLoading = false;\r\n                }\r\n            });\r\n        }\r\n    });\r\n},\r\n\r\n\r\n\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n\r\n\r\n"]}]}