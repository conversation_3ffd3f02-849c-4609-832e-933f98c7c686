{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsEdit.vue?vue&type=template&id=fabf92b6", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsEdit.vue", "mtime": 1741603061000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsEdit.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/positions/PositionsEdit.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">职位管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">编辑职位</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">编辑职位</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\r\n      <el-form-item label=\"职位名称\" prop=\"pname\">\r\n        <el-input v-model=\"formData.pname\" placeholder=\"职位名称\" style=\"width: 50%\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"职位分类\" prop=\"catid\">\r\n        <el-select v-model=\"formData.catid\" placeholder=\"请选择\" size=\"small\" style=\"width: 50%\">\r\n          <el-option\r\n            v-for=\"item in jobcategoriesList\"\r\n            :key=\"item.catid\"\r\n            :label=\"item.catname\"\r\n            :value=\"item.catid\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"工作地点\" prop=\"wlocation\">\r\n        <el-input v-model=\"formData.wlocation\" placeholder=\"工作地点\" style=\"width: 50%\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"招聘人数\" prop=\"rnumber\">\r\n        <el-input v-model=\"formData.rnumber\" placeholder=\"招聘人数\" style=\"width: 50%\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"薪资待遇\" prop=\"streatment\">\r\n        <el-input\r\n          v-model=\"formData.streatment\"\r\n          placeholder=\"薪资待遇\"\r\n          style=\"width: 50%\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"职位要求\" prop=\"prequirements\">\r\n        <WangEditor\r\n          ref=\"wangEditorRef\"\r\n          v-model=\"formData.prequirements\"\r\n          :config=\"editorConfig\"\r\n          :isClear=\"isClear\"\r\n          @change=\"editorChange\"\r\n        ></WangEditor>\r\n      </el-form-item>\r\n      <el-form-item label=\"招聘状态\" prop=\"pflag\">\r\n        <el-radio-group v-model=\"formData.pflag\">\r\n          <el-radio label=\"开放\"> 开放 </el-radio>\r\n          <el-radio label=\"关闭\"> 关闭 </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          @click=\"save\"\r\n          :loading=\"btnLoading\"\r\n          icon=\"el-icon-upload\"\r\n          >提 交</el-button\r\n        >\r\n        <el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport request, { base } from '../../../../utils/http';\r\nimport WangEditor from '../../../components/WangEditor';\r\nexport default {\r\n  name: 'PositionsEdit',\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      id: '',\r\n      isClear: false,\r\n      uploadVisible: false,\r\n      btnLoading: false, //保存按钮加载状态\r\n      formData: {}, //表单数据\r\n      jobcategoriesList: [], // 职位分类列表\r\n      addrules: {\r\n        pname: [{ required: true, message: '请输入职位名称', trigger: 'blur' }],\r\n        catid: [{ required: true, message: '请选择职位分类', trigger: 'onchange' }],\r\n        wlocation: [{ required: true, message: '请输入工作地点', trigger: 'blur' }],\r\n        rnumber: [{ required: true, message: '请输入招聘人数', trigger: 'blur' }],\r\n        streatment: [{ required: true, message: '请输入薪资待遇', trigger: 'blur' }],\r\n        pflag: [{ required: true, message: '请输入招聘状态', trigger: 'blur' }],\r\n        cid: [{ required: true, message: '请输入企业id', trigger: 'blur' }],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getDatas();\r\n    this.getJobCategories();\r\n  },\r\n\r\n  methods: {\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + '/positions/get?id=' + this.id;\r\n      request.post(url, para).then((res) => {\r\n        this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n        this.listLoading = false;\r\n        this.$refs['wangEditorRef'].editor.txt.html(this.formData.prequirements);\r\n      });\r\n    },\r\n\r\n    // 获取职位分类列表\r\n    getJobCategories() {\r\n      let url = base + '/jobcategories/list';\r\n      request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.jobcategoriesList = res.resdata;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 添加\r\n    save() {\r\n      this.$refs['formDataRef'].validate((valid) => {\r\n        //验证表单\r\n        if (valid) {\r\n          let url = base + '/positions/update';\r\n          this.btnLoading = true;\r\n\r\n          request.post(url, this.formData).then((res) => {\r\n            //发送请求\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: '操作成功',\r\n                type: 'success',\r\n                offset: 320,\r\n              });\r\n              this.$router.push({\r\n                path: '/PositionsManage',\r\n              });\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: 'error',\r\n                offset: 320,\r\n              });\r\n            }\r\n            this.btnLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 返回\r\n    goBack() {\r\n      this.$router.push({\r\n        path: '/PositionsManage',\r\n      });\r\n    },\r\n\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.prequirements = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped></style>\r\n"]}]}