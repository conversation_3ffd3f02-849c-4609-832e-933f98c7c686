{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Login.vue", "mtime": 1741615327525}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "year", "Date", "getFullYear", "loginModel", "username", "password", "role", "loginModel2", "add", "formVisible", "formData", "add<PERSON><PERSON>", "clname", "required", "message", "trigger", "password2", "validator", "rule", "value", "callback", "Error", "comname", "scale", "nature", "contact", "address", "logo", "introduction", "btnLoading", "uploadVisible", "loading", "mounted", "created", "methods", "login", "url", "aname", "post", "then", "res", "code", "console", "log", "JSON", "stringify", "resdata", "sessionStorage", "setItem", "$router", "push", "$message", "msg", "type", "toreg", "isClear", "rules", "$nextTick", "$refs", "resetFields", "reg", "validate", "valid", "offset", "clearValidate", "showUpload", "hideUpload", "handleRemove", "file", "fileList", "handlePreview", "handleExceed", "files", "duration", "fileListChange", "extendFileName", "extendFileNames", "split", "regExpRules", "i", "length", "RegExp", "fileNames", "that", "for<PERSON>ach", "key", "val", "ret", "test", "indexOf", "multiFiles", "handleConfirm", "filePath", "FormData", "append", "raw", "furl"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-form\">\n        <div class=\"login-header\">\n          <h2>求职系统</h2>\n          <p class=\"subtitle\">Job Hunting System</p>\n        </div>\n\n        <el-form :model=\"loginModel\" class=\"login-form-body\">\n          <el-form-item>\n            <el-input v-model=\"loginModel.username\" prefix-icon=\"User\" placeholder=\"请输入账号\" size=\"large\" />\n          </el-form-item>\n\n          <el-form-item>\n            <el-input v-model=\"loginModel.password\" prefix-icon=\"Lock\" type=\"password\" placeholder=\"请输入密码\" size=\"large\"\n              show-password />\n          </el-form-item>\n\n          <el-form-item class=\"role-select\">\n            <el-radio v-model=\"loginModel.role\" label=\"管理员\">管理员</el-radio>\n            <el-radio v-model=\"loginModel.role\" label=\"企业\">企业用户</el-radio>\n          </el-form-item>\n\n          <el-form-item>\n            <el-button type=\"primary\" class=\"login-button\" :loading=\"loading\" @click=\"login\" size=\"large\">\n              登 录\n            </el-button>\n          </el-form-item>\n        </el-form>\n\n        <div class=\"login-footer\">\n          <el-link type=\"primary\" @click=\"toreg\" href=\"#\">企业用户注册</el-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- 保留原有的注册对话框代码 -->\n    <el-dialog title=\"企业注册\" v-model=\"formVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n      <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\n        <el-form-item label=\"企业账号\" prop=\"clname\">\n          <el-input v-model=\"formData.clname\" placeholder=\"企业账号\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"登录密码\" prop=\"password\">\n          <el-input type=\"password\" v-model=\"formData.password\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"确认密码\" prop=\"password2\">\n          <el-input type=\"password\" v-model=\"formData.password2\" placeholder=\"确认密码\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"企业名称\" prop=\"comname\">\n          <el-input v-model=\"formData.comname\" placeholder=\"企业名称\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"企业规模\" prop=\"scale\">\n          <el-input v-model=\"formData.scale\" placeholder=\"企业规模\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"企业性质\" prop=\"nature\">\n          <el-input v-model=\"formData.nature\" placeholder=\"企业性质\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"联系方式\" prop=\"contact\">\n          <el-input v-model=\"formData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"联系地址\" prop=\"address\">\n          <el-input v-model=\"formData.address\" placeholder=\"联系地址\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item prop=\"logo\" label=\"企业logo\" min-width=\"20%\">\n          <el-input v-model=\"formData.logo\" placeholder=\"企业logo\" readonly=\"true\" style=\"width:50%;\"></el-input>\n          <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n        </el-form-item>\n        <el-form-item label=\"企业介绍\" prop=\"introduction\">\n          <el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.introduction\" placeholder=\"企业介绍\"\n            size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"reg\" :loading=\"btnLoading\">注 册</el-button>\n        </el-form-item>\n      </el-form>\n    </el-dialog>\n    <el-dialog v-model=\"uploadVisible\" title=\"附件上传\" custom-class=\"el-dialog-widthSmall\" @close=\"closeDialog\">\n      <div>\n        <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n      </div>\n      <el-upload action=\"http://localhost:8088/gouwu/api/common/uploadFile\" style=\"\n        margin: auto;\n        margin-top: 10px;\n        border: 1px solid #dcdfe6;\n        border-radius: 4px;\n      \" drag :limit=\"1\" :on-preview=\"handlePreview\" :on-remove=\"handleRemove\" :file-list=\"fileList\"\n        :on-exceed=\"handleExceed\" :auto-upload=\"false\" name=\"file\" :on-change=\"fileListChange\">\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">\n          将数据文件拖到此处，或<em>点击上传</em>\n        </div>\n        <div class=\"el-upload__tip\">\n          <div style=\"display: inline; color: #d70000; font-size: 14px\" class=\"uploadFileWarning\"\n            id=\"uploadFileWarning\"></div>\n        </div>\n      </el-upload>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"hideUpload\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import request, { base } from \"../../utils/http\";\n  export default {\n    name: \"Login\",\n    data() {\n      return {\n        year: new Date().getFullYear(),\n        loginModel: {\n          username: \"\",\n          password: \"\",\n          role: \"管理员\"\n        },\n        loginModel2: {},\n        add: true, //是否是添加\n        formVisible: false,\n        formData: {},\n\n        addrules: {\n          clname: [{ required: true, message: '请输入企业账号', trigger: 'blur' },],\n          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n          password2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n          comname: [{ required: true, message: '请输入企业名称', trigger: 'blur' },],\n          scale: [{ required: true, message: '请输入企业规模', trigger: 'blur' },],\n          nature: [{ required: true, message: '请输入企业性质', trigger: 'blur' },],\n          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },],\n          logo: [{ required: true, message: '请上传企业logo', trigger: 'blur' }],\n          introduction: [{ required: true, message: '请输入企业介绍', trigger: 'blur' },],\n        },\n\n        btnLoading: false, //按钮是否在加载中\n        uploadVisible: false, //上传弹出框\n        loading: false,\n      };\n    },\n    mounted() { },\n    created() {\n\n    },\n    methods: {\n      login() {\n        if (this.loginModel.role === \"管理员\") {\n          let url = base + \"/admin/login\";\n          this.loginModel2.aname = this.loginModel.username;\n          this.loginModel2.password = this.loginModel.password;\n          request.post(url, this.loginModel2).then((res) => {\n            this.loading = false;\n            if (res.code == 200) {\n              console.log(JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"userLname\", res.resdata.aname);\n              sessionStorage.setItem(\"role\", \"管理员\");\n              this.$router.push(\"/main\");\n            } else {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n              });\n            }\n          });\n        } else {\n          // 企业登录逻辑\n          let url = base + \"/company/login\";\n          this.loading = true;\n          this.loginModel2.clname = this.loginModel.username;\n          this.loginModel2.password = this.loginModel.password;\n          request.post(url, this.loginModel2).then((res) => {\n            this.loading = false;\n            if (res.code == 200) {\n              console.log(JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"userLname\", res.resdata.clname);\n              sessionStorage.setItem(\"role\", \"企业\");\n              this.$router.push(\"/main\");\n            } else {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n              });\n            }\n          });\n        }\n      },\n\n      toreg() {\n        this.formVisible = true;\n        this.add = true;\n        this.isClear = true;\n        this.rules = this.addrules;\n        this.$nextTick(() => {\n          this.$refs[\"formDataRef\"].resetFields();\n        });\n      },\n\n      //注册\n      reg() {\n        //表单验证\n        this.$refs[\"formDataRef\"].validate((valid) => {\n\n          if (valid) {\n            let url = base + \"/company/add\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口             \n              if (res.code == 200) {\n                this.$message({\n                  message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.formVisible = false; //关闭表单\n                this.btnLoading = false; //按钮加载状态\n                this.$refs[\"formDataRef\"].resetFields(); //重置表单\n                this.$refs[\"formDataRef\"].clearValidate();\n              }\n              else if (res.code == 201) {\n                this.$message({\n                  message: res.msg,\n                  type: \"error\",\n                  offset: 320,\n                });\n              }\n              else {\n                this.$message({\n                  message: \"服务器错误\",\n                  type: \"error\",\n                  offset: 320,\n                });\n              }\n            });\n          }\n        });\n      },\n      //显示上传框\n      showUpload() {\n        this.uploadVisible = true;\n      },\n\n      //隐藏上传框\n      hideUpload() {\n        this.uploadVisible = false;\n      },\n      //上传\n      handleRemove(file, fileList) {\n        this.fileList = fileList;\n      },\n      handlePreview(file) {\n        console.log(file);\n      },\n      handleExceed(files, fileList) {\n        this.$message({\n          duration: 1000,\n          message: \"只能上传一个文件\",\n          type: \"error\",\n          offset: 320,\n        });\n      },\n      // 判断上传文件后缀\n      fileListChange(file, fileList) {\n        let extendFileName = \"png,jpg\";\n        let extendFileNames = extendFileName.split(\",\");\n        let regExpRules = [];\n        for (let i = 0; i < extendFileNames.length; i++) {\n          regExpRules.push(\n            new RegExp(\"(.*).(\" + extendFileNames[i] + \")$\", \"gim\")\n          );\n        }\n        let fileNames = [];\n        let files = [];\n        let that = this;\n        fileList.forEach(function (key, val) {\n          let ret = false;\n          for (let i = 0; i < regExpRules.length; i++) {\n            ret = ret || regExpRules[i].test(key[\"name\"]);\n          }\n          if (!ret) {\n            console.log(key[\"name\"] + \":\" + ret);\n            that.$message({\n              duration: 1000,\n              message: \"上传的文件后缀必须为\" + extendFileName + \"格式！\",\n              type: \"error\",\n              offset: 320,\n            });\n            return false;\n          }\n          if (fileNames.indexOf(key[\"name\"]) != -1) {\n            that.$message({\n              duration: 1000,\n              message: \"上传的文件重复！\",\n              type: \"error\",\n              offset: 320,\n            });\n            return false;\n          }\n          //只能上传一个文件，用最后上传的覆盖\n          if (!that.multiFiles) {\n            files = [];\n            fileNames = [];\n          }\n          files.push(key);\n          fileNames.push(key[\"name\"]);\n          if (fileNames !== \"\") {\n            // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n          }\n          //$(\".uploadFileWarning\").text(\"\");\n        });\n        this.files = fileNames;\n        this.fileList = files;\n      },\n      /**\n       * 确认按钮\n       */\n      handleConfirm() {\n        let filePath = this.fileList;\n        if (filePath.length === 0) {\n          this.$message({\n            duration: 1000,\n            message: \"请选择文件！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        let formData = new FormData();\n        this.fileList.forEach((file) => {\n          formData.append(\"file\", file.raw, file.raw.name);\n        });\n        let url = base + \"/common/uploadFile\";\n        console.log(\"url=\" + url);\n        request.post(url, formData).then((res) => {\n          console.log(res);\n          let furl = res.resdata.filePath;\n          this.formData.logo = furl;  // 上传文件的路径\n          this.hideUpload();\n          console.log(res);\n        });\n      },\n    },\n  };\n</script>\n\n<style scoped>\n  .login-container {\n    min-height: 100vh;\n    background: linear-gradient(135deg, #1e4db7 0%, #1e4db7 100%);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 0;\n    padding: 0;\n  }\n\n  .login-box {\n    width: 420px;\n    padding: 35px;\n    background: rgba(255, 255, 255, 0.98);\n    border-radius: 12px;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  }\n\n  .login-header {\n    text-align: center;\n    margin-bottom: 35px;\n  }\n\n  .login-header h2 {\n    color: #1e4db7;\n    font-size: 26px;\n    margin: 0;\n    font-weight: 600;\n  }\n\n  .subtitle {\n    color: #666;\n    font-size: 14px;\n    margin-top: 8px;\n  }\n\n  .login-form-body {\n    margin-top: 20px;\n  }\n\n  .login-form-body :deep(.el-form-item) {\n    margin-bottom: 25px;\n  }\n\n  .role-select {\n    text-align: center;\n    margin: 5px 0 20px;\n  }\n\n  .role-select :deep(.el-radio) {\n    margin-right: 30px;\n  }\n\n  .role-select :deep(.el-radio__label) {\n    color: #666;\n  }\n\n  .role-select :deep(.el-radio__input.is-checked + .el-radio__label) {\n    color: #1e4db7;\n  }\n\n  .login-button {\n    width: 100%;\n    height: 44px;\n    font-size: 16px;\n    background: #1e4db7;\n    border-color: #1e4db7;\n  }\n\n  .login-button:hover {\n    background: #2857c1;\n    border-color: #2857c1;\n  }\n\n  .login-footer {\n    margin-top: 20px;\n    text-align: center;\n  }\n\n  :deep(.el-input__wrapper) {\n    box-shadow: 0 0 0 1px #dcdfe6 inset;\n    background: #f5f7fa;\n  }\n\n  :deep(.el-input__wrapper:hover) {\n    box-shadow: 0 0 0 1px #1e4db7 inset;\n  }\n\n  :deep(.el-input__wrapper.is-focus) {\n    box-shadow: 0 0 0 1px #1e4db7 inset !important;\n  }\n\n  :deep(.el-radio__inner) {\n    border-color: #dcdfe6;\n  }\n\n  :deep(.el-radio__input.is-checked .el-radio__inner) {\n    background: #1e4db7;\n    border-color: #1e4db7;\n  }\n</style>"], "mappings": ";;;;AA0GE,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAChD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9BC,UAAU,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE,CAAC,CAAC;MACfC,GAAG,EAAE,IAAI;MAAE;MACXC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE,CAAC,CAAC;MAEZC,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAClEV,QAAQ,EAAE,CAAC;UAAEQ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACnEC,SAAS,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EAAE;UAAEE,SAAS,EAAEA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;YAAE,IAAID,KAAI,KAAM,IAAI,CAACT,QAAQ,CAACL,QAAQ,EAAE;cAAEe,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;YAAE,OAAO;cAAED,QAAQ,CAAC,CAAC;YAAE;UAAE,CAAC;UAAEL,OAAO,EAAE;QAAO,CAAC,CAAE;QACvOO,OAAO,EAAE,CAAC;UAAET,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACnEQ,KAAK,EAAE,CAAC;UAAEV,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACjES,MAAM,EAAE,CAAC;UAAEX,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAClEU,OAAO,EAAE,CAAC;UAAEZ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACnEW,OAAO,EAAE,CAAC;UAAEb,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACnEY,IAAI,EAAE,CAAC;UAAEd,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACjEa,YAAY,EAAE,CAAC;UAAEf,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MACxE,CAAC;MAEDc,UAAU,EAAE,KAAK;MAAE;MACnBC,aAAa,EAAE,KAAK;MAAE;MACtBC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG,CAAE,CAAC;EACbC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAO,EAAE;IACPC,KAAKA,CAAA,EAAG;MACN,IAAI,IAAI,CAAChC,UAAU,CAACG,IAAG,KAAM,KAAK,EAAE;QAClC,IAAI8B,GAAE,GAAIvC,IAAG,GAAI,cAAc;QAC/B,IAAI,CAACU,WAAW,CAAC8B,KAAI,GAAI,IAAI,CAAClC,UAAU,CAACC,QAAQ;QACjD,IAAI,CAACG,WAAW,CAACF,QAAO,GAAI,IAAI,CAACF,UAAU,CAACE,QAAQ;QACpDT,OAAO,CAAC0C,IAAI,CAACF,GAAG,EAAE,IAAI,CAAC7B,WAAW,CAAC,CAACgC,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACT,OAAM,GAAI,KAAK;UACpB,IAAIS,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAACT,KAAK,CAAC;YACtDU,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;YACrC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAACC,QAAQ,CAAC;cACZrC,OAAO,EAAE0B,GAAG,CAACY,GAAG;cAChBC,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,OAAO;QACL;QACA,IAAIjB,GAAE,GAAIvC,IAAG,GAAI,gBAAgB;QACjC,IAAI,CAACkC,OAAM,GAAI,IAAI;QACnB,IAAI,CAACxB,WAAW,CAACK,MAAK,GAAI,IAAI,CAACT,UAAU,CAACC,QAAQ;QAClD,IAAI,CAACG,WAAW,CAACF,QAAO,GAAI,IAAI,CAACF,UAAU,CAACE,QAAQ;QACpDT,OAAO,CAAC0C,IAAI,CAACF,GAAG,EAAE,IAAI,CAAC7B,WAAW,CAAC,CAACgC,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACT,OAAM,GAAI,KAAK;UACpB,IAAIS,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAAClC,MAAM,CAAC;YACvDmC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;YACpC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAACC,QAAQ,CAAC;cACZrC,OAAO,EAAE0B,GAAG,CAACY,GAAG;cAChBC,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IAEDC,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC7C,WAAU,GAAI,IAAI;MACvB,IAAI,CAACD,GAAE,GAAI,IAAI;MACf,IAAI,CAAC+C,OAAM,GAAI,IAAI;MACnB,IAAI,CAACC,KAAI,GAAI,IAAI,CAAC7C,QAAQ;MAC1B,IAAI,CAAC8C,SAAS,CAAC,MAAM;QACnB,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,GAAGA,CAAA,EAAG;MACJ;MACA,IAAI,CAACF,KAAK,CAAC,aAAa,CAAC,CAACG,QAAQ,CAAEC,KAAK,IAAK;QAE5C,IAAIA,KAAK,EAAE;UACT,IAAI1B,GAAE,GAAIvC,IAAG,GAAI,cAAc,EAAE;UACjC,IAAI,CAACgC,UAAS,GAAI,IAAI,EAAE;UACxBjC,OAAO,CAAC0C,IAAI,CAACF,GAAG,EAAE,IAAI,CAAC1B,QAAQ,CAAC,CAAC6B,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACU,QAAQ,CAAC;gBACZrC,OAAO,EAAE,qBAAqB;gBAC9BuC,IAAI,EAAE,SAAS;gBACfU,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACtD,WAAU,GAAI,KAAK,EAAE;cAC1B,IAAI,CAACoB,UAAS,GAAI,KAAK,EAAE;cACzB,IAAI,CAAC6B,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;cACzC,IAAI,CAACD,KAAK,CAAC,aAAa,CAAC,CAACM,aAAa,CAAC,CAAC;YAC3C,OACK,IAAIxB,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACxB,IAAI,CAACU,QAAQ,CAAC;gBACZrC,OAAO,EAAE0B,GAAG,CAACY,GAAG;gBAChBC,IAAI,EAAE,OAAO;gBACbU,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,OACK;cACH,IAAI,CAACZ,QAAQ,CAAC;gBACZrC,OAAO,EAAE,OAAO;gBAChBuC,IAAI,EAAE,OAAO;gBACbU,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IACD;IACAE,UAAUA,CAAA,EAAG;MACX,IAAI,CAACnC,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACAoC,UAAUA,CAAA,EAAG;MACX,IAAI,CAACpC,aAAY,GAAI,KAAK;IAC5B,CAAC;IACD;IACAqC,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAO,GAAIA,QAAQ;IAC1B,CAAC;IACDC,aAAaA,CAACF,IAAI,EAAE;MAClB1B,OAAO,CAACC,GAAG,CAACyB,IAAI,CAAC;IACnB,CAAC;IACDG,YAAYA,CAACC,KAAK,EAAEH,QAAQ,EAAE;MAC5B,IAAI,CAAClB,QAAQ,CAAC;QACZsB,QAAQ,EAAE,IAAI;QACd3D,OAAO,EAAE,UAAU;QACnBuC,IAAI,EAAE,OAAO;QACbU,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD;IACAW,cAAcA,CAACN,IAAI,EAAEC,QAAQ,EAAE;MAC7B,IAAIM,cAAa,GAAI,SAAS;MAC9B,IAAIC,eAAc,GAAID,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC;MAC/C,IAAIC,WAAU,GAAI,EAAE;MACpB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,eAAe,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/CD,WAAW,CAAC5B,IAAI,CACd,IAAI+B,MAAM,CAAC,QAAO,GAAIL,eAAe,CAACG,CAAC,IAAI,IAAI,EAAE,KAAK,CACxD,CAAC;MACH;MACA,IAAIG,SAAQ,GAAI,EAAE;MAClB,IAAIV,KAAI,GAAI,EAAE;MACd,IAAIW,IAAG,GAAI,IAAI;MACfd,QAAQ,CAACe,OAAO,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnC,IAAIC,GAAE,GAAI,KAAK;QACf,KAAK,IAAIR,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3CQ,GAAE,GAAIA,GAAE,IAAKT,WAAW,CAACC,CAAC,CAAC,CAACS,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C;QACA,IAAI,CAACE,GAAG,EAAE;UACR7C,OAAO,CAACC,GAAG,CAAC0C,GAAG,CAAC,MAAM,IAAI,GAAE,GAAIE,GAAG,CAAC;UACpCJ,IAAI,CAAChC,QAAQ,CAAC;YACZsB,QAAQ,EAAE,IAAI;YACd3D,OAAO,EAAE,YAAW,GAAI6D,cAAa,GAAI,KAAK;YAC9CtB,IAAI,EAAE,OAAO;YACbU,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA,IAAImB,SAAS,CAACO,OAAO,CAACJ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;UACxCF,IAAI,CAAChC,QAAQ,CAAC;YACZsB,QAAQ,EAAE,IAAI;YACd3D,OAAO,EAAE,UAAU;YACnBuC,IAAI,EAAE,OAAO;YACbU,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA;QACA,IAAI,CAACoB,IAAI,CAACO,UAAU,EAAE;UACpBlB,KAAI,GAAI,EAAE;UACVU,SAAQ,GAAI,EAAE;QAChB;QACAV,KAAK,CAACtB,IAAI,CAACmC,GAAG,CAAC;QACfH,SAAS,CAAChC,IAAI,CAACmC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAIH,SAAQ,KAAM,EAAE,EAAE;UACpB;QAAA;QAEF;MACF,CAAC,CAAC;MACF,IAAI,CAACV,KAAI,GAAIU,SAAS;MACtB,IAAI,CAACb,QAAO,GAAIG,KAAK;IACvB,CAAC;IACD;;;IAGAmB,aAAaA,CAAA,EAAG;MACd,IAAIC,QAAO,GAAI,IAAI,CAACvB,QAAQ;MAC5B,IAAIuB,QAAQ,CAACZ,MAAK,KAAM,CAAC,EAAE;QACzB,IAAI,CAAC7B,QAAQ,CAAC;UACZsB,QAAQ,EAAE,IAAI;UACd3D,OAAO,EAAE,QAAQ;UACjBuC,IAAI,EAAE,OAAO;UACbU,MAAM,EAAE;QACV,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MACA,IAAIrD,QAAO,GAAI,IAAImF,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACxB,QAAQ,CAACe,OAAO,CAAEhB,IAAI,IAAK;QAC9B1D,QAAQ,CAACoF,MAAM,CAAC,MAAM,EAAE1B,IAAI,CAAC2B,GAAG,EAAE3B,IAAI,CAAC2B,GAAG,CAACjG,IAAI,CAAC;MAClD,CAAC,CAAC;MACF,IAAIsC,GAAE,GAAIvC,IAAG,GAAI,oBAAoB;MACrC6C,OAAO,CAACC,GAAG,CAAC,MAAK,GAAIP,GAAG,CAAC;MACzBxC,OAAO,CAAC0C,IAAI,CAACF,GAAG,EAAE1B,QAAQ,CAAC,CAAC6B,IAAI,CAAEC,GAAG,IAAK;QACxCE,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;QAChB,IAAIwD,IAAG,GAAIxD,GAAG,CAACM,OAAO,CAAC8C,QAAQ;QAC/B,IAAI,CAAClF,QAAQ,CAACiB,IAAG,GAAIqE,IAAI,EAAG;QAC5B,IAAI,CAAC9B,UAAU,CAAC,CAAC;QACjBxB,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}