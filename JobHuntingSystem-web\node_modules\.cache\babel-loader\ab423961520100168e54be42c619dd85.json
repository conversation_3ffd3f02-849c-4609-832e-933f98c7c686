{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1741615349295}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJzsKaW1wb3J0IHsgRWxNZXNzYWdlIH0gZnJvbSAnZWxlbWVudC1wbHVzJzsKaW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gJy4uLy4uLy4uL3V0aWxzL2h0dHAnOwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVzZXJMbmFtZTogJycsCiAgICAgIHJvbGU6ICcnLAogICAgICAvLyDnrqHnkIblkZjnu5/orqHmlbDmja4KICAgICAgc3R1ZGVudENvdW50OiAwLAogICAgICBjb21wYW55Q291bnQ6IDAsCiAgICAgIGpvYkNvdW50OiAwLAogICAgICByZXN1bWVDb3VudDogMCwKICAgICAgLy8g5LyB5Lia57uf6K6h5pWw5o2uCiAgICAgIGNvbXBhbnlKb2JDb3VudDogMCwKICAgICAgY29tcGFueVJlc3VtZUNvdW50OiAwCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMudXNlckxuYW1lID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndXNlckxuYW1lJyk7CiAgICB0aGlzLnJvbGUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdyb2xlJyk7CiAgICB0aGlzLmdldFN0YXRpc3RpY3MoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGdldFN0YXRpc3RpY3MoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgdXJsID0gYmFzZSArICh0aGlzLnJvbGUgPT09ICfnrqHnkIblkZgnID8gJy9zdGF0aXN0aWNzL2FkbWluJyA6ICcvc3RhdGlzdGljcy9jb21wYW55Jyk7CiAgICAgICAgbGV0IHBhcmFtcyA9IHt9OwogICAgICAgIGlmICh0aGlzLnJvbGUgPT09ICfkvIHkuJonKSB7CiAgICAgICAgICB2YXIgdXNlciA9IEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpKTsKICAgICAgICAgIHBhcmFtcyA9IHsKICAgICAgICAgICAgY2lkOiB1c2VyLmNpZAogICAgICAgICAgfTsKICAgICAgICB9CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0LmdldCh1cmwsIHsKICAgICAgICAgIHBhcmFtcwogICAgICAgIH0pOwogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgaWYgKHRoaXMucm9sZSA9PT0gJ+euoeeQhuWRmCcpIHsKICAgICAgICAgICAgdGhpcy5zdHVkZW50Q291bnQgPSBkYXRhLnN0dWRlbnRDb3VudDsKICAgICAgICAgICAgdGhpcy5jb21wYW55Q291bnQgPSBkYXRhLmNvbXBhbnlDb3VudDsKICAgICAgICAgICAgdGhpcy5qb2JDb3VudCA9IGRhdGEuam9iQ291bnQ7CiAgICAgICAgICAgIHRoaXMucmVzdW1lQ291bnQgPSBkYXRhLnJlc3VtZUNvdW50OwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy5jb21wYW55Sm9iQ291bnQgPSBkYXRhLmpvYkNvdW50OwogICAgICAgICAgICB0aGlzLmNvbXBhbnlSZXN1bWVDb3VudCA9IGRhdGEucmVzdW1lQ291bnQ7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIEVsTWVzc2FnZS5lcnJvcihyZXNwb25zZS5kYXRhLm1zZyB8fCAn6I635Y+W57uf6K6h5pWw5o2u5aSx6LSlJyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHZhciBfZXJyb3IkcmVzcG9uc2U7CiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W57uf6K6h5pWw5o2u5aSx6LSlOicsIGVycm9yKTsKICAgICAgICBFbE1lc3NhZ2UuZXJyb3IoKChfZXJyb3IkcmVzcG9uc2UgPSBlcnJvci5yZXNwb25zZSkgPT09IG51bGwgfHwgX2Vycm9yJHJlc3BvbnNlID09PSB2b2lkIDAgfHwgKF9lcnJvciRyZXNwb25zZSA9IF9lcnJvciRyZXNwb25zZS5kYXRhKSA9PT0gbnVsbCB8fCBfZXJyb3IkcmVzcG9uc2UgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9lcnJvciRyZXNwb25zZS5tc2cpIHx8ICfojrflj5bnu5/orqHmlbDmja7lpLHotKUnKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["axios", "ElMessage", "request", "base", "data", "userLname", "role", "studentCount", "companyCount", "jobCount", "resumeCount", "companyJobCount", "companyResumeCount", "mounted", "sessionStorage", "getItem", "getStatistics", "methods", "url", "params", "user", "JSON", "parse", "cid", "response", "get", "code", "error", "msg", "_error$response", "console"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">后台首页</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">欢迎页面</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">欢迎页面</h4>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n      <!-- 管理员显示4个统计数据 -->\r\n      <template v-if=\"role === '管理员'\">\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-pink\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-user widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Students\">求职者总数</h6>\r\n              <h2 class=\"my-2\">{{ studentCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">注册求职者总人数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-purple\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-office-building widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Companies\">企业总数</h6>\r\n              <h2 class=\"my-2\">{{ companyCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">企业总数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-info\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-suitcase widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Jobs\">职位总数</h6>\r\n              <h2 class=\"my-2\">{{ jobCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">发布职位总数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-primary\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-document widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Resumes\">简历投递数</h6>\r\n              <h2 class=\"my-2\">{{ resumeCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">简历投递总数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 企业显示2个统计数据 -->\r\n      <template v-if=\"role === '企业'\">\r\n        <div class=\"col-xxl-6 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-info\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-suitcase widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Jobs\">发布职位</h6>\r\n              <h2 class=\"my-2\">{{ companyJobCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">已发布职位数量</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-6 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-primary\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-document widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Resumes\">收到简历</h6>\r\n              <h2 class=\"my-2\">{{ companyResumeCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">收到简历投递数量</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div style=\"width: 100%; line-height: 30px; text-align: center; padding: 100px\">\r\n        账号：<b style=\"color: red\">{{ userLname }}</b>， 身份：<b style=\"color: red\">{{ role }}</b><br />\r\n        您好，欢迎使用求职系统！<br />\r\n        请在左侧菜单中选择您要进行的操作！\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import axios from 'axios';\r\n  import { ElMessage } from 'element-plus';\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    data() {\r\n      return {\r\n        userLname: '',\r\n        role: '',\r\n        // 管理员统计数据\r\n        studentCount: 0,\r\n        companyCount: 0,\r\n        jobCount: 0,\r\n        resumeCount: 0,\r\n        // 企业统计数据\r\n        companyJobCount: 0,\r\n        companyResumeCount: 0,\r\n      };\r\n    },\r\n    mounted() {\r\n      this.userLname = sessionStorage.getItem('userLname');\r\n      this.role = sessionStorage.getItem('role');\r\n      this.getStatistics();\r\n    },\r\n    methods: {\r\n      async getStatistics() {\r\n        try {\r\n          const url = base + (this.role === '管理员' ? '/statistics/admin' : '/statistics/company');\r\n\r\n          let params = {};\r\n          if (this.role === '企业') {\r\n            var user = JSON.parse(sessionStorage.getItem('user'));\r\n            params = {\r\n              cid: user.cid,\r\n            };\r\n          }\r\n\r\n          const response = await request.get(url, { params });\r\n\r\n          if (response.code === 200) {\r\n            const data = response.data;\r\n            if (this.role === '管理员') {\r\n              this.studentCount = data.studentCount;\r\n              this.companyCount = data.companyCount;\r\n              this.jobCount = data.jobCount;\r\n              this.resumeCount = data.resumeCount;\r\n            } else {\r\n              this.companyJobCount = data.jobCount;\r\n              this.companyResumeCount = data.resumeCount;\r\n            }\r\n          } else {\r\n            ElMessage.error(response.data.msg || '获取统计数据失败');\r\n          }\r\n        } catch (error) {\r\n          console.error('获取统计数据失败:', error);\r\n          ElMessage.error(error.response?.data?.msg || '获取统计数据失败');\r\n        }\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .widget-icon {\r\n    font-size: 24px;\r\n    background: rgba(255, 255, 255, 0.1);\r\n    height: 40px;\r\n    width: 40px;\r\n    text-align: center;\r\n    line-height: 40px;\r\n    border-radius: 3px;\r\n    display: inline-block;\r\n  }\r\n</style>"], "mappings": "AAyHE,OAAOA,KAAI,MAAO,OAAO;AACzB,SAASC,SAAQ,QAAS,cAAc;AACxC,OAAOC,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AAEnD,eAAe;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACR;MACAC,YAAY,EAAE,CAAC;MACfC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACd;MACAC,eAAe,EAAE,CAAC;MAClBC,kBAAkB,EAAE;IACtB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACR,SAAQ,GAAIS,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACT,IAAG,GAAIQ,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IAC1C,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,MAAME,GAAE,GAAIf,IAAG,IAAK,IAAI,CAACG,IAAG,KAAM,KAAI,GAAI,mBAAkB,GAAI,qBAAqB,CAAC;QAEtF,IAAIa,MAAK,GAAI,CAAC,CAAC;QACf,IAAI,IAAI,CAACb,IAAG,KAAM,IAAI,EAAE;UACtB,IAAIc,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACR,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;UACrDI,MAAK,GAAI;YACPI,GAAG,EAAEH,IAAI,CAACG;UACZ,CAAC;QACH;QAEA,MAAMC,QAAO,GAAI,MAAMtB,OAAO,CAACuB,GAAG,CAACP,GAAG,EAAE;UAAEC;QAAO,CAAC,CAAC;QAEnD,IAAIK,QAAQ,CAACE,IAAG,KAAM,GAAG,EAAE;UACzB,MAAMtB,IAAG,GAAIoB,QAAQ,CAACpB,IAAI;UAC1B,IAAI,IAAI,CAACE,IAAG,KAAM,KAAK,EAAE;YACvB,IAAI,CAACC,YAAW,GAAIH,IAAI,CAACG,YAAY;YACrC,IAAI,CAACC,YAAW,GAAIJ,IAAI,CAACI,YAAY;YACrC,IAAI,CAACC,QAAO,GAAIL,IAAI,CAACK,QAAQ;YAC7B,IAAI,CAACC,WAAU,GAAIN,IAAI,CAACM,WAAW;UACrC,OAAO;YACL,IAAI,CAACC,eAAc,GAAIP,IAAI,CAACK,QAAQ;YACpC,IAAI,CAACG,kBAAiB,GAAIR,IAAI,CAACM,WAAW;UAC5C;QACF,OAAO;UACLT,SAAS,CAAC0B,KAAK,CAACH,QAAQ,CAACpB,IAAI,CAACwB,GAAE,IAAK,UAAU,CAAC;QAClD;MACF,EAAE,OAAOD,KAAK,EAAE;QAAA,IAAAE,eAAA;QACdC,OAAO,CAACH,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC1B,SAAS,CAAC0B,KAAK,CAAC,EAAAE,eAAA,GAAAF,KAAK,CAACH,QAAQ,cAAAK,eAAA,gBAAAA,eAAA,GAAdA,eAAA,CAAgBzB,IAAI,cAAAyB,eAAA,uBAApBA,eAAA,CAAsBD,GAAE,KAAK,UAAU,CAAC;MAC1D;IACF;EACF;AACF,CAAC", "ignoreList": []}]}