{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\LeftMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1741619105687}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgZXhwb3J0IGRlZmF1bHQgewogICAgbmFtZTogJ0xlZnRNZW51JywKICAgIGRhdGEoKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgdXNlckxuYW1lOiAnJywKICAgICAgICByb2xlOiAnJywKICAgICAgfTsKICAgIH0sCiAgICBtb3VudGVkKCkgewogICAgICB0aGlzLnVzZXJMbmFtZSA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3VzZXJMbmFtZScpOwogICAgICB0aGlzLnJvbGUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdyb2xlJyk7CiAgICB9LAogICAgY3JlYXRlZCgpIHsgfSwKICB9Owo="}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\LeftMenu.vue"], "names": [], "mappings": ";EAuLE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACV,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACf,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/components/LeftMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"leftside-menu menuitem-active\">\n    <a href=\"\" class=\"logo logo-light\">\n      <span class=\"logo-lg\" style=\"font-size: 18px\"> 求职系统 </span>\n      <span class=\"logo-sm\" style=\"font-size: 18px\"> 求职系统 </span>\n    </a>\n    <a href=\"\" class=\"logo logo-dark\">\n      <span class=\"logo-lg\" style=\"font-size: 18px\"> 求职系统 </span>\n      <span class=\"logo-sm\" style=\"font-size: 18px\"> 求职系统 </span>\n    </a>\n    <div class=\"h-100 show\" id=\"leftside-menu-container\" data-simplebar=\"init\">\n      <div class=\"simplebar-wrapper\" style=\"margin: 0px\">\n        <div class=\"simplebar-height-auto-observer-wrapper\">\n          <div class=\"simplebar-height-auto-observer\"></div>\n        </div>\n        <div class=\"simplebar-mask\">\n          <div class=\"simplebar-offset\" style=\"right: 0px; bottom: 0px\">\n            <div class=\"simplebar-content-wrapper\" tabindex=\"0\" role=\"region\" aria-label=\"scrollable content\"\n              style=\"height: 100%; overflow: hidden\">\n              <div class=\"simplebar-content\" style=\"padding: 0px\">\n                <ul class=\"side-nav\">\n                  <li class=\"side-nav-title\">功能菜单</li>\n\n                  <el-menu style=\"width: 240px\" router unique-opened=\"true\" active-text-color=\"#36A9B0\"\n                    background-color=\"#1A2942\" text-color=\"#70809A\" class=\"el-menu-vertical-demo\" v-if=\"role == '管理员'\">\n                    <el-submenu index=\"8\">\n                      <template #title>\n                        <i class=\"el-icon-reading\"></i>\n                        <span>专业管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/professionalsAdd\"><i class=\"el-icon-position\"></i>添加专业</el-menu-item>\n                        <el-menu-item index=\"/professionalsManage\"><i class=\"el-icon-position\"></i>管理专业</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"6\">\n                      <template #title>\n                        <i class=\"el-icon-user\"></i>\n                        <span>求职者管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/studentsAdd\"><i class=\"el-icon-position\"></i>添加求职者</el-menu-item>\n                        <el-menu-item index=\"/studentsManage\"><i class=\"el-icon-position\"></i>管理求职者</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"7\">\n                      <template #title>\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>企业管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/companyManage2\"><i class=\"el-icon-position\"></i>审核企业</el-menu-item>\n                        <el-menu-item index=\"/companyManage\"><i class=\"el-icon-position\"></i>管理企业</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"5\">\n                      <template #title>\n                        <i class=\"el-icon-collection\"></i>\n                        <span>职位分类管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/jobcategoriesAdd\"><i class=\"el-icon-position\"></i>添加职位分类</el-menu-item>\n                        <el-menu-item index=\"/jobcategoriesManage\"><i class=\"el-icon-position\"></i>管理职位分类</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"2\">\n                      <template #title>\n                        <i class=\"el-icon-suitcase\"></i>\n                        <span>职位管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/positionsManage3\"><i class=\"el-icon-position\"></i>审核职位</el-menu-item>\n                        <el-menu-item index=\"/positionsManage2\"><i class=\"el-icon-position\"></i>管理职位</el-menu-item>\n\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"4\">\n                      <template #title>\n                        <i class=\"el-icon-document\"></i>\n                        <span>简历管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/resumeManage\"><i class=\"el-icon-position\"></i>管理简历</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"10\">\n                      <template #title>\n                        <i class=\"el-icon-message\"></i>\n                        <span>简历投递管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/resumedeliveryManage\"><i\n                            class=\"el-icon-position\"></i>管理简历投递</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"11\"> \n                      <template #title> \n                        <i class=\"el-icon-dish\"></i>\n                        <span>论坛管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/bbsManage\"><i class=\"el-icon-position\"></i>帖子管理</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n\n\n                    <el-submenu index=\"1\">\n                      <template #title>\n                        <i class=\"el-icon-setting\"></i>\n                        <span>系统管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"password\">修改密码</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n                  </el-menu>\n\n                  <el-menu style=\"width: 240px\" router unique-opened=\"true\" active-text-color=\"#36A9B0\"\n                    background-color=\"#1A2942\" text-color=\"#70809A\" class=\"el-menu-vertical-demo\" v-if=\"role == '企业'\">\n                    <el-submenu index=\"2\">\n                      <template #title>\n                        <i class=\"el-icon-suitcase\"></i>\n                        <span>职位管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/positionsAdd\"><i class=\"el-icon-position\"></i>添加职位</el-menu-item>\n                        <el-menu-item index=\"/positionsManage\"><i class=\"el-icon-position\"></i>管理职位</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n                    <el-submenu index=\"10\">\n                      <template #title>\n                        <i class=\"el-icon-notebook-2\"></i>\n                        <span>简历投递管理</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/resumedeliveryManage2\"><i\n                            class=\"el-icon-position\"></i>简历投递列表</el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n\n\n         \n\n                    <el-submenu index=\"1\">\n                      <template #title>\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>企业中心</span>\n                      </template>\n                      <el-menu-item-group>\n                        <el-menu-item index=\"/companyInfo\"><i class=\"el-icon-position\"></i>信息维护</el-menu-item>\n                        <el-menu-item index=\"password\">\n                          <i class=\"el-icon-position\"></i>修改密码\n                        </el-menu-item>\n                      </el-menu-item-group>\n                    </el-submenu>\n                  </el-menu>\n                </ul>\n                <div class=\"clearfix\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"simplebar-placeholder\" style=\"width: auto; height: 680px\"></div>\n      </div>\n      <div class=\"simplebar-track simplebar-horizontal\" style=\"visibility: hidden\">\n        <div class=\"simplebar-scrollbar\" style=\"width: 0px; display: none\"></div>\n      </div>\n      <div class=\"simplebar-track simplebar-vertical\" style=\"visibility: hidden\">\n        <div class=\"simplebar-scrollbar\" style=\"height: 0px; display: none\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\n  export default {\n    name: 'LeftMenu',\n    data() {\n      return {\n        userLname: '',\n        role: '',\n      };\n    },\n    mounted() {\n      this.userLname = sessionStorage.getItem('userLname');\n      this.role = sessionStorage.getItem('role');\n    },\n    created() { },\n  };\n</script>\n\n<style scoped></style>"]}]}