{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Menunav.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Menunav.vue", "mtime": 1741536483000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vLi4vdXRpbHMvaHR0cCI7DQppbXBvcnQgTWVudSBmcm9tICIuLi8uLi9jb21wb25lbnRzL01lbnUiOw0KaW1wb3J0IFRvcE1lbnUgZnJvbSAiLi4vLi4vY29tcG9uZW50cy9Ub3BNZW51IjsNCmltcG9ydCBGb290IGZyb20gIi4uLy4uL2NvbXBvbmVudHMvRm9vdCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCgluYW1lOiAiTWVudW5hdiIsDQoJY29tcG9uZW50czogew0KCQlUb3BNZW51LA0KCQlGb290LA0KCQlNZW51DQoJfSwNCglkYXRhKCkgew0KCQlyZXR1cm4gew0KDQoJCX07DQoJfSwNCgltb3VudGVkKCkgew0KDQoJfSwNCgljcmVhdGVkKCkgew0KDQoJfSwNCgltZXRob2RzOiB7DQoNCgl9LA0KfTsNCg=="}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Menunav.vue"], "names": [], "mappings": ";AA+DA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAExC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;CACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;CACJ,CAAC;CACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEP,CAAC;CACF,CAAC;CACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;CAEV,CAAC;CACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;CAEV,CAAC;CACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;CAET,CAAC;AACF,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Menunav.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\t<TopMenu />\r\n\r\n\t<div class=\"custom-breadcrumb custom-breadcrumb--bg\"\r\n\t\t:style=\"{ backgroundImage: 'url(' + require('@/assets/images/breadcrumb-bg.jpg') + ')' }\">\r\n\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"row\">\r\n\t\t\t\t<!-- page title -->\r\n\t\t\t\t<div class=\"col-md-6\">\r\n\t\t\t\t\t<h1 class=\"page-title\">\r\n\t\t\t\t\t\t{{ $route.meta.title }}\r\n\t\t\t\t\t</h1>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- breadcrumb -->\r\n\t\t\t\t<div class=\"col-md-6 breadcrumb-menu\">\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\r\n\t<div class=\"main-content py-120\" style=\"padding-top:30px;\">\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"row\">\r\n\r\n\t\t\t\t<Menu />\r\n\r\n\t\t\t\t<div class=\"col-lg-9 sidebar\">\r\n\t\t\t\t\t<div class=\"widget widget_archive\">\r\n\t\t\t\t\t\t<h3 class=\"widget-title\">\r\n\t\t\t\t\t\t\t{{ $route.meta.title }}\r\n\t\t\t\t\t\t</h3>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<!-- blog posts -->\r\n\t\t\t\t\t<div class=\"row\">\r\n\t\t\t\t\t\t<!-- blog post item -->\r\n\t\t\t\t\t\t<div class=\"col-12\">\r\n\r\n\t\t\t\t\t\t\t<div class=\"blog-post blog-post--list\">\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"blog-post__body\" style=\"padding-top:0px;\">\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t<div class=\"blog-post__body--content\">\r\n\t\t\t\t\t\t\t\t\t\t<router-view />\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<!-- end blog contents -->\r\n\t\t</div>\r\n\t</div>\r\n\r\n\r\n\r\n\t<Foot />\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport Menu from \"../../components/Menu\";\r\nimport TopMenu from \"../../components/TopMenu\";\r\nimport Foot from \"../../components/Foot\";\r\n\r\nexport default {\r\n\tname: \"Menunav\",\r\n\tcomponents: {\r\n\t\tTopMenu,\r\n\t\tFoot,\r\n\t\tMenu\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\r\n\t\t};\r\n\t},\r\n\tmounted() {\r\n\r\n\t},\r\n\tcreated() {\r\n\r\n\t},\r\n\tmethods: {\r\n\r\n\t},\r\n};\r\n</script>\r\n\r\n\r\n<style></style>\r\n"]}]}