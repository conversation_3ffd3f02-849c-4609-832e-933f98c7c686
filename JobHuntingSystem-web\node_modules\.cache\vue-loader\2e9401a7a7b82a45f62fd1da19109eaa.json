{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue?vue&type=template&id=9bcc0be2&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue", "mtime": 1741617283358}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;MACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAChE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAClE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACrE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC7E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAClE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACjE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/components/Menu.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div class=\"col-lg-3 sidebar sidebar--left\">\n  <div class=\"sidebar-container\">\n    <div class=\"sidebar-header\">\n      <h3 class=\"sidebar-title\">个人中心</h3>\n      <div class=\"sidebar-divider\"></div>\n    </div>\n\n    <div class=\"sidebar-menu\">\n      <div class=\"menu-group\">\n     \n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Sweclome') }\">\n            <a href=\"/Sweclome\">\n              <i class=\"fas fa-home\"></i>\n              <span>欢迎页面</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-file-alt\"></i>\n          <span>简历管理</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resume_Add') }\">\n            <a href=\"/resume_Add\">\n              <i class=\"fas fa-plus-circle\"></i>\n              <span>创建简历</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resume_manage') }\">\n            <a href=\"/resume_manage\">\n              <i class=\"fas fa-tasks\"></i>\n              <span>管理简历</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-history\"></i>\n          <span>记录查询</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resumedelivery_manage') }\">\n            <a href=\"/resumedelivery_manage\">\n              <i class=\"fas fa-paper-plane\"></i>\n              <span>我的投递记录</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/browsingrecords_manage') }\">\n            <a href=\"/browsingrecords_manage\">\n              <i class=\"fas fa-eye\"></i>\n              <span>我的浏览记录</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">  \n      \n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/bbs_manage') }\">\n            <a href=\"/bbs_manage\">\n              <i class=\"fas fa-comments\"></i>\n              <span>我的帖子</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-user-cog\"></i>\n          <span>账户设置</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Sinfo') }\">\n            <a href=\"/Sinfo\">\n              <i class=\"fas fa-user-edit\"></i>\n              <span>修改个人信息</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Spassword') }\">\n            <a href=\"/Spassword\">\n              <i class=\"fas fa-lock\"></i>\n              <span>修改密码</span>\n            </a>\n          </li>\n          <li class=\"menu-item\">\n            <a @click=\"quit()\" style=\"cursor: pointer\">\n              <i class=\"fas fa-sign-out-alt\"></i>\n              <span>退出登录</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n    </div>\n  </div>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: 'Menu',\n    data() {\n      return {\n        currentPath: ''\n      };\n    },\n    mounted() {\n      //判断是否登录\n      var lname = sessionStorage.getItem('lname');\n      if (lname == null) {\n        //弹出提示\n        this.$message({\n          message: '请先登录',\n          type: 'warning',\n          offset: 320,\n        });\n        //跳转到登录页面\n        this.$router.push('/slogin');\n      }\n\n      // 获取当前路径\n      this.currentPath = window.location.pathname;\n    },\n    methods: {\n      quit() {\n        var _this = this;\n        this.$confirm('确认退出吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            sessionStorage.removeItem('lname');\n            _this.$router.push('/slogin');\n          })\n          .catch(() => { });\n      },\n      isActive(path) {\n        return this.currentPath === path;\n      }\n    },\n  };\n</script>\n\n<style scoped>\n  .sidebar-container {\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n    overflow: hidden;\n  }\n\n  .sidebar-header {\n    padding: 20px;\n    background-color: #3498db;\n    color: #fff;\n  }\n\n  .sidebar-title {\n    margin: 0;\n    font-size: 20px;\n    font-weight: 600;\n  }\n\n  .sidebar-divider {\n    height: 3px;\n    width: 50px;\n    background-color: rgba(255, 255, 255, 0.5);\n    margin-top: 10px;\n    border-radius: 1.5px;\n  }\n\n  .sidebar-menu {\n    padding: 15px 0;\n  }\n\n  .menu-group {\n    margin-bottom: 15px;\n  }\n\n  .menu-group-title {\n    padding: 10px 20px;\n    color: #7f8c8d;\n    font-size: 14px;\n    font-weight: 600;\n    text-transform: uppercase;\n    display: flex;\n    align-items: center;\n  }\n\n  .menu-group-title i {\n    margin-right: 8px;\n    font-size: 16px;\n  }\n\n  .menu-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .menu-item {\n    position: relative;\n  }\n\n  .menu-item a {\n    display: flex;\n    align-items: center;\n    padding: 12px 20px;\n    color: #34495e;\n    transition: all 0.3s ease;\n    text-decoration: none;\n  }\n\n  .menu-item a i {\n    margin-right: 10px;\n    width: 20px;\n    text-align: center;\n    font-size: 16px;\n    color: #7f8c8d;\n    transition: all 0.3s ease;\n  }\n\n  .menu-item a:hover {\n    background-color: #f8f9fa;\n    color: #3498db;\n  }\n\n  .menu-item a:hover i {\n    color: #3498db;\n  }\n\n  .menu-item.active a {\n    background-color: #ebf5fb;\n    color: #3498db;\n    font-weight: 600;\n  }\n\n  .menu-item.active a i {\n    color: #3498db;\n  }\n\n  .menu-item.active::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 0;\n    height: 100%;\n    width: 4px;\n    background-color: #3498db;\n    border-radius: 0 2px 2px 0;\n  }\n\n  /* 响应式设计 */\n  @media (max-width: 992px) {\n    .sidebar-container {\n      margin-bottom: 30px;\n    }\n  }\n</style>"]}]}