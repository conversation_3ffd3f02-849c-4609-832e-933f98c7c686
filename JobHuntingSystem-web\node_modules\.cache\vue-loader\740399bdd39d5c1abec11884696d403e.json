{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyInfo.vue?vue&type=template&id=13b6a42f", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyInfo.vue", "mtime": 1741602812000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0icm93Ij4KICAgIDxkaXYgY2xhc3M9ImNvbC0xMiI+CiAgICAgIDxkaXYgY2xhc3M9InBhZ2UtdGl0bGUtYm94Ij4KICAgICAgICA8ZGl2IGNsYXNzPSJwYWdlLXRpdGxlLXJpZ2h0Ij4KICAgICAgICAgIDxvbCBjbGFzcz0iYnJlYWRjcnVtYiBtLTAiPgogICAgICAgICAgICA8bGkgY2xhc3M9ImJyZWFkY3J1bWItaXRlbSI+PGEgaWQ9InRpdGxlMiI+5LyB5Lia5Lit5b+DPC9hPjwvbGk+CiAgICAgICAgICAgIDxsaSBjbGFzcz0iYnJlYWRjcnVtYi1pdGVtIGFjdGl2ZSIgaWQ9InRpdGxlMyI+5L+h5oGv57u05oqkPC9saT4KICAgICAgICAgIDwvb2w+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGg0IGNsYXNzPSJwYWdlLXRpdGxlIiBpZD0idGl0bGUxIj7kv6Hmga/nu7TmiqQ8L2g0PgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogICAgPGVsLWZvcm0gOm1vZGVsPSJmb3JtRGF0YSIgbGFiZWwtd2lkdGg9IjIwJSIgcmVmPSJmb3JtRGF0YVJlZiIgOnJ1bGVzPSJhZGRydWxlcyIgYWxpZ249ImxlZnQiPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLkvIHkuJrotKblj7ciIHByb3A9ImNsbmFtZSI+CiAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICB2LW1vZGVsPSJmb3JtRGF0YS5jbG5hbWUiCiAgICAgICAgICBwbGFjZWhvbGRlcj0i5LyB5Lia6LSm5Y+3IgogICAgICAgICAgc3R5bGU9IndpZHRoOiA1MCUiCiAgICAgICAgICBkaXNhYmxlZAogICAgICAgID48L2VsLWlucHV0PgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5LyB5Lia5ZCN56ewIiBwcm9wPSJjb21uYW1lIj4KICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybURhdGEuY29tbmFtZSIgcGxhY2Vob2xkZXI9IuS8geS4muWQjeensCIgc3R5bGU9IndpZHRoOiA1MCUiPjwvZWwtaW5wdXQ+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLkvIHkuJrop4TmqKEiIHByb3A9InNjYWxlIj4KICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybURhdGEuc2NhbGUiIHBsYWNlaG9sZGVyPSLkvIHkuJrop4TmqKEiIHN0eWxlPSJ3aWR0aDogNTAlIj48L2VsLWlucHV0PgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5LyB5Lia5oCn6LSoIiBwcm9wPSJuYXR1cmUiPgogICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtRGF0YS5uYXR1cmUiIHBsYWNlaG9sZGVyPSLkvIHkuJrmgKfotKgiIHN0eWxlPSJ3aWR0aDogNTAlIj48L2VsLWlucHV0PgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6IGU57O75pa55byPIiBwcm9wPSJjb250YWN0Ij4KICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybURhdGEuY29udGFjdCIgcGxhY2Vob2xkZXI9IuiBlOezu+aWueW8jyIgc3R5bGU9IndpZHRoOiA1MCUiPjwvZWwtaW5wdXQ+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLogZTns7vlnLDlnYAiIHByb3A9ImFkZHJlc3MiPgogICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtRGF0YS5hZGRyZXNzIiBwbGFjZWhvbGRlcj0i6IGU57O75Zyw5Z2AIiBzdHlsZT0id2lkdGg6IDUwJSI+PC9lbC1pbnB1dD4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0ibG9nbyIgbGFiZWw9IuS8geS4mmxvZ28iIG1pbi13aWR0aD0iMjAlIj4KICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgIHYtbW9kZWw9ImZvcm1EYXRhLmxvZ28iCiAgICAgICAgICBwbGFjZWhvbGRlcj0i5LyB5LiabG9nbyIKICAgICAgICAgIHJlYWRvbmx5PSJ0cnVlIgogICAgICAgICAgc3R5bGU9IndpZHRoOiA1MCUiCiAgICAgICAgPjwvZWwtaW5wdXQ+CiAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBzaXplPSJzbWFsbCIgQGNsaWNrPSJzaG93VXBsb2FkIj7kuIrkvKA8L2VsLWJ1dHRvbj4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuS8geS4muS7i+e7jSIgcHJvcD0iaW50cm9kdWN0aW9uIj4KICAgICAgICA8V2FuZ0VkaXRvcgogICAgICAgICAgcmVmPSJ3YW5nRWRpdG9yUmVmIgogICAgICAgICAgdi1tb2RlbD0iZm9ybURhdGEuaW50cm9kdWN0aW9uIgogICAgICAgICAgOmNvbmZpZz0iZWRpdG9yQ29uZmlnIgogICAgICAgICAgOmlzQ2xlYXI9ImlzQ2xlYXIiCiAgICAgICAgICBAY2hhbmdlPSJlZGl0b3JDaGFuZ2UiCiAgICAgICAgPjwvV2FuZ0VkaXRvcj4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0+CiAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgQGNsaWNrPSJzYXZlIgogICAgICAgICAgOmxvYWRpbmc9ImJ0bkxvYWRpbmciCiAgICAgICAgICBpY29uPSJlbC1pY29uLXVwbG9hZCIKICAgICAgICAgID7mj5Ag5LqkPC9lbC1idXR0b24KICAgICAgICA+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPC9lbC1mb3JtPgogICAgPGVsLWRpYWxvZwogICAgICB2LW1vZGVsPSJ1cGxvYWRWaXNpYmxlIgogICAgICB0aXRsZT0i6ZmE5Lu25LiK5LygIgogICAgICBjdXN0b20tY2xhc3M9ImVsLWRpYWxvZy13aWR0aFNtYWxsIgogICAgICBAY2xvc2U9ImNsb3NlRGlhbG9nIgogICAgPgogICAgICA8ZGl2PgogICAgICAgIDxiPuivt+mAieaLqeaWh+S7tu+8iHBuZyxqcGfmoLzlvI/vvInov5vooYzkuIrkvKDvvIEmZW1zcDs8L2I+CiAgICAgIDwvZGl2PgogICAgICA8ZWwtdXBsb2FkCiAgICAgICAgYWN0aW9uPSJodHRwOi8vbG9jYWxob3N0OjgwODgvZ291d3UvYXBpL2NvbW1vbi91cGxvYWRGaWxlIgogICAgICAgIHN0eWxlPSJtYXJnaW46IGF1dG87IG1hcmdpbi10b3A6IDEwcHg7IGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7IGJvcmRlci1yYWRpdXM6IDRweCIKICAgICAgICBkcmFnCiAgICAgICAgOmxpbWl0PSIxIgogICAgICAgIDpvbi1wcmV2aWV3PSJoYW5kbGVQcmV2aWV3IgogICAgICAgIDpvbi1yZW1vdmU9ImhhbmRsZVJlbW92ZSIKICAgICAgICA6ZmlsZS1saXN0PSJmaWxlTGlzdCIKICAgICAgICA6b24tZXhjZWVkPSJoYW5kbGVFeGNlZWQiCiAgICAgICAgOmF1dG8tdXBsb2FkPSJmYWxzZSIKICAgICAgICBuYW1lPSJmaWxlIgogICAgICAgIDpvbi1jaGFuZ2U9ImZpbGVMaXN0Q2hhbmdlIgogICAgICA+CiAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tdXBsb2FkIj48L2k+CiAgICAgICAgPGRpdiBjbGFzcz0iZWwtdXBsb2FkX190ZXh0Ij7lsIbmlbDmja7mlofku7bmi5bliLDmraTlpITvvIzmiJY8ZW0+54K55Ye75LiK5LygPC9lbT48L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJlbC11cGxvYWRfX3RpcCI+CiAgICAgICAgICA8ZGl2CiAgICAgICAgICAgIHN0eWxlPSJkaXNwbGF5OiBpbmxpbmU7IGNvbG9yOiAjZDcwMDAwOyBmb250LXNpemU6IDE0cHgiCiAgICAgICAgICAgIGNsYXNzPSJ1cGxvYWRGaWxlV2FybmluZyIKICAgICAgICAgICAgaWQ9InVwbG9hZEZpbGVXYXJuaW5nIgogICAgICAgICAgPjwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2VsLXVwbG9hZD4KICAgICAgPHNwYW4gY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJoaWRlVXBsb2FkIj7lj5Yg5raIPC9lbC1idXR0b24+CiAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImhhbmRsZUNvbmZpcm0iPuaPkCDkuqQ8L2VsLWJ1dHRvbj4KICAgICAgPC9zcGFuPgogICAgPC9lbC1kaWFsb2c+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyInfo.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;MACE,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/company/CompanyInfo.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业中心</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">信息维护</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">信息维护</h4>\n      </div>\n    </div>\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n      <el-form-item label=\"企业账号\" prop=\"clname\">\n        <el-input\n          v-model=\"formData.clname\"\n          placeholder=\"企业账号\"\n          style=\"width: 50%\"\n          disabled\n        ></el-input>\n      </el-form-item>\n      <el-form-item label=\"企业名称\" prop=\"comname\">\n        <el-input v-model=\"formData.comname\" placeholder=\"企业名称\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"企业规模\" prop=\"scale\">\n        <el-input v-model=\"formData.scale\" placeholder=\"企业规模\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"企业性质\" prop=\"nature\">\n        <el-input v-model=\"formData.nature\" placeholder=\"企业性质\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"formData.contact\" placeholder=\"联系方式\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系地址\" prop=\"address\">\n        <el-input v-model=\"formData.address\" placeholder=\"联系地址\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item prop=\"logo\" label=\"企业logo\" min-width=\"20%\">\n        <el-input\n          v-model=\"formData.logo\"\n          placeholder=\"企业logo\"\n          readonly=\"true\"\n          style=\"width: 50%\"\n        ></el-input>\n        <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n      </el-form-item>\n      <el-form-item label=\"企业介绍\" prop=\"introduction\">\n        <WangEditor\n          ref=\"wangEditorRef\"\n          v-model=\"formData.introduction\"\n          :config=\"editorConfig\"\n          :isClear=\"isClear\"\n          @change=\"editorChange\"\n        ></WangEditor>\n      </el-form-item>\n      <el-form-item>\n        <el-button\n          type=\"primary\"\n          size=\"small\"\n          @click=\"save\"\n          :loading=\"btnLoading\"\n          icon=\"el-icon-upload\"\n          >提 交</el-button\n        >\n      </el-form-item>\n    </el-form>\n    <el-dialog\n      v-model=\"uploadVisible\"\n      title=\"附件上传\"\n      custom-class=\"el-dialog-widthSmall\"\n      @close=\"closeDialog\"\n    >\n      <div>\n        <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n      </div>\n      <el-upload\n        action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n        style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\"\n        drag\n        :limit=\"1\"\n        :on-preview=\"handlePreview\"\n        :on-remove=\"handleRemove\"\n        :file-list=\"fileList\"\n        :on-exceed=\"handleExceed\"\n        :auto-upload=\"false\"\n        name=\"file\"\n        :on-change=\"fileListChange\"\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\">\n          <div\n            style=\"display: inline; color: #d70000; font-size: 14px\"\n            class=\"uploadFileWarning\"\n            id=\"uploadFileWarning\"\n          ></div>\n        </div>\n      </el-upload>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"hideUpload\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport request, { base } from '../../../../utils/http';\nimport WangEditor from '../../../components/WangEditor';\nexport default {\n  name: 'CompanyInfo',\n  components: {\n    WangEditor,\n  },\n  data() {\n    return {\n      id: '',\n      isClear: false,\n      uploadVisible: false,\n      btnLoading: false, //保存按钮加载状态\n      formData: {}, //表单数据\n      addrules: {\n        comname: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],\n        scale: [{ required: true, message: '请输入企业规模', trigger: 'blur' }],\n        nature: [{ required: true, message: '请输入企业性质', trigger: 'blur' }],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],\n        address: [{ required: true, message: '请输入联系地址', trigger: 'blur' }],\n        logo: [{ required: true, message: '请输入企业logo', trigger: 'blur' }],\n      },\n    };\n  },\n  created() {\n    var user = JSON.parse(sessionStorage.getItem('user'));\n    this.id = user.cid;\n    this.getDatas();\n  },\n  methods: {\n    //获取列表数据\n    getDatas() {\n      let para = {};\n      this.listLoading = true;\n      let url = base + '/company/get?id=' + this.id;\n      request.post(url, para).then((res) => {\n        this.formData = JSON.parse(JSON.stringify(res.resdata));\n        this.listLoading = false;\n        this.$refs['wangEditorRef'].editor.txt.html(this.formData.introduction);\n      });\n    },\n\n    // 添加\n    save() {\n      this.$refs['formDataRef'].validate((valid) => {\n        //验证表单\n        if (valid) {\n          let url = base + '/company/update';\n          this.btnLoading = true;\n\n          request.post(url, this.formData).then((res) => {\n            //发送请求\n            if (res.code == 200) {\n              this.$message({\n                message: '操作成功',\n                type: 'success',\n                offset: 320,\n              });\n            } else {\n              this.$message({\n                message: res.msg,\n                type: 'error',\n                offset: 320,\n              });\n            }\n            this.btnLoading = false;\n          });\n        }\n      });\n    },\n\n    //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: '只能上传一个文件',\n        type: 'error',\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = 'png,jpg';\n      let extendFileNames = extendFileName.split(',');\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key['name']);\n        }\n        if (!ret) {\n          console.log(key['name'] + ':' + ret);\n          that.$message({\n            duration: 1000,\n            message: '上传的文件后缀必须为' + extendFileName + '格式！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key['name']) != -1) {\n          that.$message({\n            duration: 1000,\n            message: '上传的文件重复！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key['name']);\n        if (fileNames !== '') {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: '请选择文件！',\n          type: 'error',\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append('file', file.raw, file.raw.name);\n      });\n      let url = base + '/common/uploadFile';\n      console.log('url=' + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.logo = furl; // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n\n    // 富文本编辑器\n    editorChange(val) {\n      this.formData.introduction = val;\n    },\n  },\n};\n</script>\n<style scoped></style>\n"]}]}