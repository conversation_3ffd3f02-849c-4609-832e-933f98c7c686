{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue", "mtime": 1741617073822}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gJy4uLy4uLy4uL3V0aWxzL2h0dHAnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1N3ZWNsb21lJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG5hbWU6ICcnLAogICAgICBmb3JtRGF0YToge30sCiAgICAgIC8v6KGo5Y2V5pWw5o2uCiAgICAgIHRpbWU6ICcnLAogICAgICAvL+W9k+WJjeaXtumXtAogICAgICByZXN1bWVDb3VudDogMCwKICAgICAgZGVsaXZlcnlDb3VudDogMCwKICAgICAgYnJvd3NlQ291bnQ6IDAsCiAgICAgIGZhdm9yaXRlQ291bnQ6IDAKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5sbmFtZSA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ2xuYW1lJyk7CiAgICB0aGlzLmdldERhdGFzKCk7CiAgICB0aGlzLnRpbWUgPSBuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+iOt+WPluS4quS6uuS/oeaBr+aVsOaNrgogICAgZ2V0RGF0YXMoKSB7CiAgICAgIGxldCBwYXJhID0ge307CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOwogICAgICBsZXQgdXJsID0gYmFzZSArICcvc3R1ZGVudHMvZ2V0P2lkPScgKyB0aGlzLmxuYW1lOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5mb3JtRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocmVzLnJlc2RhdGEpKTsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["request", "base", "name", "data", "lname", "formData", "time", "resumeCount", "deliveryCount", "browseCount", "favoriteCount", "created", "sessionStorage", "getItem", "getDatas", "Date", "toLocaleString", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue"], "sourcesContent": ["<template>\r\n  <div class=\"welcome-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <div class=\"banner-content\">\r\n        <div class=\"user-avatar\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" alt=\"用户头像\" />\r\n        </div>\r\n        <div class=\"user-info\">\r\n          <h2 class=\"welcome-text\">欢迎回来，<span class=\"user-name\">{{ lname }}</span></h2>\r\n          <p class=\"login-time\" style=\"color: white;\">登录时间：{{ time }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n\r\n\r\n    <!-- 快捷操作 -->\r\n    <div class=\"quick-actions\">\r\n      <div class=\"section-title\">\r\n        <h3>快捷操作</h3>\r\n      </div>\r\n\r\n      <div class=\"row\">\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-plus-circle\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">创建简历</h4>\r\n            <p class=\"action-desc\">创建一份专业的简历，展示你的技能和经验</p>\r\n            <a href=\"/resume_Add\" class=\"action-link\">立即创建</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-search\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">搜索职位</h4>\r\n            <p class=\"action-desc\">浏览最新的职位信息，寻找适合你的工作机会</p>\r\n            <a href=\"/positionsList\" class=\"action-link\">开始搜索</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-robot\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">AI顾问</h4>\r\n            <p class=\"action-desc\">获取个性化的职业建议和简历优化指导</p>\r\n            <a href=\"/ai\" class=\"action-link\">咨询顾问</a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n  export default {\r\n    name: 'Sweclome',\r\n    data() {\r\n      return {\r\n        lname: '',\r\n        formData: {}, //表单数据\r\n        time: '', //当前时间\r\n        resumeCount: 0,\r\n        deliveryCount: 0,\r\n        browseCount: 0,\r\n        favoriteCount: 0\r\n      };\r\n    },\r\n    created() {\r\n      this.lname = sessionStorage.getItem('lname');\r\n      this.getDatas();\r\n      this.time = new Date().toLocaleString();\r\n    },\r\n    methods: {\r\n      //获取个人信息数据\r\n      getDatas() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + '/students/get?id=' + this.lname;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n  \r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 欢迎页面容器 */\r\n  .welcome-container {\r\n    padding: 20px;\r\n  }\r\n\r\n  /* 欢迎横幅 */\r\n  .welcome-banner {\r\n    background: linear-gradient(135deg, #3498db, #2c3e50);\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    margin-bottom: 30px;\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .banner-content {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .user-avatar {\r\n    margin-right: 30px;\r\n  }\r\n\r\n  .user-avatar img {\r\n    width: 100px;\r\n    height: 100px;\r\n    border-radius: 50%;\r\n    border: 4px solid rgba(255, 255, 255, 0.3);\r\n    object-fit: cover;\r\n  }\r\n\r\n  .user-info {\r\n    color: #fff;\r\n  }\r\n\r\n  .welcome-text {\r\n    font-size: 24px;\r\n    margin-bottom: 10px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .user-name {\r\n    color: #f1c40f;\r\n    font-weight: 700;\r\n  }\r\n\r\n  .login-time {\r\n    font-size: 14px;\r\n    opacity: 0.8;\r\n    margin: 0;\r\n  }\r\n\r\n  /* 统计卡片 */\r\n  .stats-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .stats-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .stats-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .stats-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 10px;\r\n    background-color: rgba(231, 76, 60, 0.1);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 15px;\r\n  }\r\n\r\n  .stats-icon i {\r\n    font-size: 24px;\r\n    color: #e74c3c;\r\n  }\r\n\r\n  .stats-icon.blue {\r\n    background-color: rgba(52, 152, 219, 0.1);\r\n  }\r\n\r\n  .stats-icon.blue i {\r\n    color: #3498db;\r\n  }\r\n\r\n  .stats-icon.green {\r\n    background-color: rgba(46, 204, 113, 0.1);\r\n  }\r\n\r\n  .stats-icon.green i {\r\n    color: #2ecc71;\r\n  }\r\n\r\n  .stats-icon.purple {\r\n    background-color: rgba(155, 89, 182, 0.1);\r\n  }\r\n\r\n  .stats-icon.purple i {\r\n    color: #9b59b6;\r\n  }\r\n\r\n  .stats-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .stats-title {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin: 0 0 5px;\r\n  }\r\n\r\n  .stats-value {\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    color: #2c3e50;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .stats-action a {\r\n    color: #3498db;\r\n    font-size: 13px;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .stats-action a:hover {\r\n    color: #2980b9;\r\n  }\r\n\r\n  .stats-action i {\r\n    font-size: 10px;\r\n    margin-left: 3px;\r\n  }\r\n\r\n  /* 个人信息卡片 */\r\n  .profile-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .section-title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .section-title h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0;\r\n    position: relative;\r\n    padding-left: 15px;\r\n  }\r\n\r\n  .section-title h3::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: 0;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 5px;\r\n    height: 20px;\r\n    background-color: #3498db;\r\n    border-radius: 2.5px;\r\n  }\r\n\r\n  .edit-link {\r\n    color: #3498db;\r\n    font-size: 14px;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .edit-link:hover {\r\n    color: #2980b9;\r\n  }\r\n\r\n  .profile-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 25px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  }\r\n\r\n  .info-group {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-label {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .info-value {\r\n    font-size: 16px;\r\n    color: #2c3e50;\r\n    font-weight: 500;\r\n  }\r\n\r\n  /* 快捷操作 */\r\n  .quick-actions {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .action-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 25px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n    height: 100%;\r\n  }\r\n\r\n  .action-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .action-icon {\r\n    width: 70px;\r\n    height: 70px;\r\n    border-radius: 50%;\r\n    background-color: rgba(52, 152, 219, 0.1);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin: 0 auto 20px;\r\n  }\r\n\r\n  .action-icon i {\r\n    font-size: 30px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .action-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .action-desc {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin-bottom: 20px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  .action-link {\r\n    display: inline-block;\r\n    padding: 8px 20px;\r\n    background-color: #3498db;\r\n    color: #fff;\r\n    border-radius: 5px;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .action-link:hover {\r\n    background-color: #2980b9;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n  @media (max-width: 768px) {\r\n    .banner-content {\r\n      flex-direction: column;\r\n      text-align: center;\r\n    }\r\n\r\n    .user-avatar {\r\n      margin-right: 0;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .welcome-text {\r\n      font-size: 20px;\r\n    }\r\n  }\r\n</style>"], "mappings": "AAgEE,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,IAAI,EAAE,EAAE;MAAE;MACVC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE;IACjB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACP,KAAI,GAAIQ,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;IAC5C,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACR,IAAG,GAAI,IAAIS,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;EACzC,CAAC;EACDC,OAAO,EAAE;IACP;IACAH,QAAQA,CAAA,EAAG;MACT,IAAII,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAInB,IAAG,GAAI,mBAAkB,GAAI,IAAI,CAACG,KAAK;MACjDJ,OAAO,CAACqB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAAClB,QAAO,GAAImB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ;EAEF;AACF,CAAC", "ignoreList": []}]}