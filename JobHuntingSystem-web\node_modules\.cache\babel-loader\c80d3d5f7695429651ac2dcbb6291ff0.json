{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\jobcategories\\JobcategoriesEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\jobcategories\\JobcategoriesEdit.vue", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "catname", "required", "message", "trigger", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "save", "$refs", "validate", "valid", "code", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\jobcategories\\JobcategoriesEdit.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">职位分类管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">编辑职位分类</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">编辑职位分类</h4>\r\n      </div>\r\n    </div>\r\n     <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"分类名称\" prop=\"catname\">\r\n<el-input v-model=\"formData.catname\" placeholder=\"分类名称\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'JobcategoriesEdit',\n  components: {\n    \n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          catname: [{ required: true, message: '请输入分类名称', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n    },\r\n\r\n \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/jobcategories/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            \n            \n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/jobcategories/update\";\n              this.btnLoading = true;\n              \n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });\n                  this.$router.push({\n                    path: \"/JobcategoriesManage\",\n                  });\n                } else {\n                  this.$message({\n                    message:res.msg,\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n                this.btnLoading = false;\n              });\n            }\n    \n          });\n        },\n        \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/JobcategoriesManage\",\n          });\n        },       \n              \n          \n           \n           \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";AA6BA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,OAAO,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MACjE;IAEJ,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACV,IAAI,CAACV,EAAC,GAAI,IAAI,CAACW,MAAM,CAACC,KAAK,CAACZ,EAAE;IAC5B,IAAI,CAACa,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAEb;IACQD,QAAQA,CAAA,EAAG;MACT,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIrB,IAAG,GAAI,wBAAuB,GAAI,IAAI,CAACI,EAAE;MACnDL,OAAO,CAACuB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAAChB,QAAO,GAAIiB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAG1B,CAAC,CAAC;IACJ,CAAC;IAED;IACAS,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIX,GAAE,GAAIrB,IAAG,GAAI,uBAAuB;UACxC,IAAI,CAACO,UAAS,GAAI,IAAI;UAEtBR,OAAO,CAACuB,IAAI,CAACD,GAAG,EAAE,IAAI,CAACb,QAAQ,CAAC,CAACe,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACS,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZtB,OAAO,EAAE,MAAM;gBACfuB,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZtB,OAAO,EAACY,GAAG,CAACgB,GAAG;gBACfL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAAC7B,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACJ,CAAC;IAEF;IACCkC,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EAKF;AACN", "ignoreList": []}]}