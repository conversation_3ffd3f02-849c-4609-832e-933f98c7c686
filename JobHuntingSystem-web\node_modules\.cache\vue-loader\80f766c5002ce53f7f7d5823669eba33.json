{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyDetail.vue", "mtime": 1741615257361}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCiAgaW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOw0KICBleHBvcnQgZGVmYXVsdCB7DQogICAgbmFtZTogJ0NvbXBhbnlEZXRhaWwnLA0KICAgIGNvbXBvbmVudHM6IHsNCiAgICB9LA0KICAgIGRhdGEoKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICBpZDogJycsDQogICAgICAgIGZvcm1EYXRhOiB7fSwgLy/ooajljZXmlbDmja4gICAgICAgICANCg0KICAgICAgfTsNCiAgICB9LA0KICAgIGNyZWF0ZWQoKSB7DQogICAgICB0aGlzLmlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7IC8v6I635Y+W5Y+C5pWwDQogICAgICB0aGlzLmdldERhdGFzKCk7DQogICAgfSwNCg0KDQogICAgbWV0aG9kczogew0KDQogICAgICAvL+iOt+WPluWIl+ihqOaVsOaNrg0KICAgICAgZ2V0RGF0YXMoKSB7DQogICAgICAgIGxldCBwYXJhID0gew0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL2NvbXBhbnkvZ2V0P2lkPSIgKyB0aGlzLmlkOw0KICAgICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLmZvcm1EYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyZXMucmVzZGF0YSkpOw0KICAgICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSk7DQogICAgICB9LA0KDQogICAgICAvLyDov5Tlm54NCiAgICAgIGJhY2soKSB7DQogICAgICAgIC8v6L+U5Zue5LiK5LiA6aG1DQogICAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7DQogICAgICB9LA0KDQogICAgfSwNCiAgfQ0KDQo="}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyDetail.vue"], "names": [], "mappings": ";;EAkDE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,EAAE,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;;IAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;MAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;;IAEH,CAAC;EACH", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/company/CompanyDetail.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">企业详情</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">企业详情</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n      <el-form-item label=\"企业id\">\r\n        {{ formData.cid }}</el-form-item>\r\n      <el-form-item label=\"企业账号\">\r\n        {{ formData.clname }}</el-form-item>\r\n      <el-form-item label=\"登录密码\">\r\n        {{ formData.password }}</el-form-item>\r\n      <el-form-item label=\"企业名称\">\r\n        {{ formData.comname }}</el-form-item>\r\n      <el-form-item label=\"企业规模\">\r\n        {{ formData.scale }}</el-form-item>\r\n      <el-form-item label=\"企业性质\">\r\n        {{ formData.nature }}</el-form-item>\r\n      <el-form-item label=\"联系方式\">\r\n        {{ formData.contact }}</el-form-item>\r\n      <el-form-item label=\"联系地址\">\r\n        {{ formData.address }}</el-form-item>\r\n      <el-form-item label=\"企业logo\" prop=\"logo\">\r\n        <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.logo\" style=\"width: 150px;height: 150px\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"企业介绍\" prop=\"introduction\">\r\n        <div v-html=\"formData.introduction\"></div>\r\n      </el-form-item>\r\n      <el-form-item label=\"注册时间\">\r\n        {{ formData.addtime }}</el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\n\r\n  import request, { base } from \"../../../../utils/http\";\r\n  export default {\r\n    name: 'CompanyDetail',\r\n    components: {\r\n    },\r\n    data() {\r\n      return {\r\n        id: '',\r\n        formData: {}, //表单数据         \r\n\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id; //获取参数\r\n      this.getDatas();\r\n    },\r\n\r\n\r\n    methods: {\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/company/get?id=\" + this.id;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 返回\r\n      back() {\r\n        //返回上一页\r\n        this.$router.go(-1);\r\n      },\r\n\r\n    },\r\n  }\r\n\r\n</script>\r\n<style scoped></style>"]}]}