{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue", "mtime": 1741614414000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "bbsmore", "formData", "mdetail", "rules", "required", "message", "trigger", "created", "getDatas", "methods", "id", "$route", "query", "para", "listLoading", "url", "post", "then", "res", "resdata", "save", "lname", "sessionStorage", "getItem", "$message", "type", "$refs", "formDataRef", "validate", "valid", "btnLoading", "bid", "sno", "code", "$nextTick", "editor", "txt", "html", "msg", "<PERSON><PERSON><PERSON><PERSON>", "val"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue"], "sourcesContent": ["<template>\r\n  <table style=\"width:100%;line-height:27px; font-size:12px;border: 1px solid #C2D5E3;  margin-bottom:10px;\">\r\n    <tr>\r\n\r\n      <td> <span style=\"font-size:14px; font-weight:bold\">\r\n          {{ bbsmore.btitle }}\r\n        </span>\r\n\r\n      </td>\r\n      <td width=\"200\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\">\r\n        查看：<b style=\"color:red;\">\r\n          {{ bbsmore.btotal }}\r\n        </b> |\r\n        回复：<b style=\"color:red;\">\r\n          {{ bbsmore.by1 }}\r\n        </b> |\r\n        <a href=\"/bbs\">返回列表</a>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/JobHuntingSystem/' + bbsmore.by2\" />\r\n        </div>\r\n\r\n        <span class=\"cu-tag bg-gradual-green\" style=\" height:28px;\">楼主</span>\r\n        <span class=\"text-red\">\r\n          {{ bbsmore.sno }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"bbsmore.bdetail\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ bbsmore.addtime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \" v-for=\"item in bbsmore.bbsmore\">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/JobHuntingSystem/' + item.by1\" />\r\n        </div>\r\n\r\n        <span class=\"text-red\">\r\n          {{ item.sno }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"item.mdetail\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ item.antime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\r\n\r\n    <el-form-item label=\"回复内容\" prop=\"mdetail\">\r\n      <WangEditor ref=\"wangEditorRef\" v-model=\"formData.mdetail\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n        @change=\"editorChange\" height=\"200\"></WangEditor>\r\n    </el-form-item>\r\n    <el-form-item label=\"\">\r\n      <el-button type=\"primary\" @click=\"save\" :loading=\"btnLoading\">提 交</el-button>\r\n    </el-form-item>\r\n\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport WangEditor from \"../../components/WangEditor\";\r\n\r\nexport default {\r\n  name: \"bbsView\",\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      bbsmore: \"\",\r\n\r\n      formData: {\r\n        mdetail: \"\",\r\n      },\r\n\r\n      rules: {\r\n        mdetail: [\r\n          { required: true, message: \"请输入回复内容\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n\r\n\r\n    };\r\n  },\r\n  created() {\r\n\r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let id = this.$route.query.id;\r\n      let para = {\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/bbs/get?id=\" + id;\r\n      request.post(url, para).then((res) => {\r\n        this.bbsmore = res.resdata;\r\n      });\r\n    },\r\n\r\n    //回复\r\n    save() {\r\n      var lname = sessionStorage.getItem(\"lname\");\r\n      if (lname == null) {\r\n        this.$message({\r\n          message: \"请先登录\",\r\n          type: \"error\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs.formDataRef.validate((valid) => {\r\n        if (valid) {\r\n          this.btnLoading = true;\r\n          let para = {\r\n            bid: this.bbsmore.bid,\r\n            mdetail: this.formData.mdetail,\r\n            sno: lname,\r\n          };\r\n          let url = base + \"/bbsmore/add\";\r\n          request.post(url, para).then((res) => {\r\n            this.btnLoading = false;\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: \"回复成功\",\r\n                type: \"success\",\r\n              });\r\n              this.getDatas();\r\n              this.formData.mdetail = \"\";\r\n\r\n              //设置富文本编辑器内容\r\n              this.$nextTick(() => {\r\n                this.$refs[\"wangEditorRef\"].editor.txt.html(\"\");\r\n              });\r\n\r\n\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.mdetail = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 180px;\r\n}\r\n</style>\r\n\r\n"], "mappings": "AAkGA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,OAAOC,UAAS,MAAO,6BAA6B;AAEpD,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,EAAE;MAEXC,QAAQ,EAAE;QACRC,OAAO,EAAE;MACX,CAAC;MAEDC,KAAK,EAAE;QACLD,OAAO,EAAE,CACP;UAAEE,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAE3D;IAGF,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IAER,IAAI,CAACC,QAAQ,CAAC,CAAC;EAEjB,CAAC;EACDC,OAAO,EAAE;IAEP;IACAD,QAAQA,CAAA,EAAG;MACT,IAAIE,EAAC,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,EAAE;MAC7B,IAAIG,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIpB,IAAG,GAAI,cAAa,GAAIe,EAAE;MACpChB,OAAO,CAACsB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAAClB,OAAM,GAAIkB,GAAG,CAACC,OAAO;MAC5B,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL,IAAIC,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAI,IAAK,IAAI,EAAE;QACjB,IAAI,CAACG,QAAQ,CAAC;UACZnB,OAAO,EAAE,MAAM;UACfoB,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF;MAEA,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,QAAQ,CAAEC,KAAK,IAAK;QACzC,IAAIA,KAAK,EAAE;UACT,IAAI,CAACC,UAAS,GAAI,IAAI;UACtB,IAAIjB,IAAG,GAAI;YACTkB,GAAG,EAAE,IAAI,CAAC/B,OAAO,CAAC+B,GAAG;YACrB7B,OAAO,EAAE,IAAI,CAACD,QAAQ,CAACC,OAAO;YAC9B8B,GAAG,EAAEX;UACP,CAAC;UACD,IAAIN,GAAE,GAAIpB,IAAG,GAAI,cAAc;UAC/BD,OAAO,CAACsB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;YACpC,IAAI,CAACY,UAAS,GAAI,KAAK;YACvB,IAAIZ,GAAG,CAACe,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACT,QAAQ,CAAC;gBACZnB,OAAO,EAAE,MAAM;gBACfoB,IAAI,EAAE;cACR,CAAC,CAAC;cACF,IAAI,CAACjB,QAAQ,CAAC,CAAC;cACf,IAAI,CAACP,QAAQ,CAACC,OAAM,GAAI,EAAE;;cAE1B;cACA,IAAI,CAACgC,SAAS,CAAC,MAAM;gBACnB,IAAI,CAACR,KAAK,CAAC,eAAe,CAAC,CAACS,MAAM,CAACC,GAAG,CAACC,IAAI,CAAC,EAAE,CAAC;cACjD,CAAC,CAAC;YAGJ,OAAO;cACL,IAAI,CAACb,QAAQ,CAAC;gBACZnB,OAAO,EAAEa,GAAG,CAACoB,GAAG;gBAChBb,IAAI,EAAE;cACR,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,OAAO;UACL,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC;IACD;IACAc,YAAYA,CAACC,GAAG,EAAE;MAChB,IAAI,CAACvC,QAAQ,CAACC,OAAM,GAAIsC,GAAG;IAC7B;EACF;AACF,CAAC", "ignoreList": []}]}