{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue?vue&type=template&id=3c83f0b7", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue", "mtime": 1741615257361}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_Fragment", "_renderList", "$data", "list1", "item", "index", "key", "href", "catid", "catname", "_hoisted_4", "_hoisted_5", "_hoisted_6", "list2", "pid", "pname", "_hoisted_8", "_hoisted_9", "list3", "cid", "src", "logo", "style", "_hoisted_12", "_toDisplayString", "comname"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue"], "sourcesContent": ["<template>\r\n  <div class=\"col-xxl-4 col-xl-4 col-lg-4\">\r\n    <div class=\"animated-list\">\r\n      <h2 class=\"section-title\">职位分类</h2>\r\n      <ul class=\"fen-list\">\r\n        <li v-for=\"(item, index) in list1\" :key=\"index\">\r\n          <a :href=\"'positionsList?catid=' + item.catid\">{{ item.catname }}</a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"animated-list\">\r\n      <h2 class=\"section-title\">最新职位</h2>\r\n      <ul class=\"news-list\">\r\n        <li class=\"fade-in\" v-for=\"(item, index) in list2\" :key=\"index\">\r\n          <a :href=\"'positionsView?id=' + item.pid\"><i class=\"fas fa-star\"></i>{{ item.pname }}</a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"animated-gallery\">\r\n      <h2 class=\"section-title\">最新企业</h2>\r\n      <div class=\"image-grid\">\r\n        <div class=\"image-item zoom-in\" v-for=\"(item, index) in list3\" :key=\"index\">\r\n          <a :href=\"'companyView?id=' + item.cid\">\r\n            <img :src=\"'http://localhost:8088/JobHuntingSystem/' + item.logo\" style=\"width: 165px; height: 123px\" />\r\n            <p class=\"image-caption\">{{ item.comname }}</p>\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../utils/http';\r\n  export default {\r\n    name: 'Left',\r\n    data() {\r\n      return {\r\n        list1: '',\r\n        list2: '',\r\n        list3: '',\r\n        isLoggedIn: false,\r\n      };\r\n    },\r\n    created() {\r\n      this.checkLoginStatus();\r\n      this.getlist1();\r\n      this.getlist2();\r\n      this.getlist3();\r\n    },\r\n    methods: {\r\n      // 检查登录状态\r\n      checkLoginStatus() {\r\n        const lname = sessionStorage.getItem('lname');\r\n        this.isLoggedIn = !!lname;\r\n      },\r\n\r\n      // 获取职位分类\r\n      getlist1() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + '/jobcategories/list?currentPage=1&pageSize=100';\r\n        request.post(url, para).then((res) => {\r\n          this.list1 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 获取最新职位\r\n      getlist2() {\r\n        let para = {\r\n          pflag: '开放', // 只获取开放状态的职位\r\n          pflag2: '审核通过',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/positions/list?currentPage=1&pageSize=10';\r\n        request.post(url, para).then((res) => {\r\n          this.list2 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 获取最新企业\r\n      getlist3() {\r\n        let para = {\r\n          cflag: '审核通过',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/company/list?currentPage=1&pageSize=6';\r\n        request.post(url, para).then((res) => {\r\n          this.list3 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style>\r\n  .fen-list {\r\n    list-style-type: none;\r\n    padding: 0;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .fen-list li {\r\n    width: calc(50% - 10px);\r\n    margin-bottom: 15px;\r\n    opacity: 0;\r\n    animation: fadeIn 0.5s ease-in forwards;\r\n    font-size: 16px;\r\n    color: #555;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .fen-list li:hover {\r\n    transform: translateY(-5px);\r\n  }\r\n\r\n  .fen-list li a {\r\n    color: #333;\r\n    text-decoration: none;\r\n    display: block;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 100%;\r\n    padding: 10px;\r\n    background-color: #f0f0f0;\r\n    border-radius: 5px;\r\n    transition: background-color 0.3s ease;\r\n  }\r\n\r\n  .fen-list li a:hover {\r\n    color: #3498db;\r\n    background-color: #e0e0e0;\r\n  }\r\n\r\n  .fen-list li i {\r\n    margin-right: 10px;\r\n    color: #3498db;\r\n  }\r\n\r\n  @keyframes fadeIn {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(20px);\r\n    }\r\n\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .animated-list,\r\n  .animated-gallery {\r\n    margin-bottom: 40px;\r\n    background: #f9f9f9;\r\n    border-radius: 10px;\r\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 18px;\r\n    margin-bottom: 20px;\r\n    color: #333;\r\n    border-bottom: 2px solid #3498db;\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .news-list {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  .news-list li {\r\n    margin-bottom: 15px;\r\n    opacity: 0;\r\n    animation: fadeIn 0.5s ease-in forwards;\r\n    display: flex;\r\n    flex-direction: column;\r\n    font-size: 16px;\r\n    color: #555;\r\n  }\r\n\r\n  .news-list li a {\r\n    color: #333;\r\n    text-decoration: none;\r\n    display: flex;\r\n    align-items: center;\r\n    display: block;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .news-list li a:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .news-list li i {\r\n    margin-right: 10px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .news-date {\r\n    font-size: 14px;\r\n    color: #888;\r\n    margin-top: 5px;\r\n    margin-left: 24px;\r\n  }\r\n\r\n  .section-divider {\r\n    border: 0;\r\n    height: 1px;\r\n    background: #e0e0e0;\r\n    margin: 30px 0;\r\n  }\r\n\r\n  .image-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 20px;\r\n  }\r\n\r\n  .image-item {\r\n    position: relative;\r\n    overflow: hidden;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .image-item img {\r\n    width: 100%;\r\n    height: auto;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .image-item:hover img {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  .image-caption {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: #fff;\r\n    padding: 10px;\r\n    margin: 0;\r\n    font-size: 13px;\r\n    text-align: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n  }\r\n\r\n  .image-item:hover .image-caption {\r\n    opacity: 1;\r\n  }\r\n\r\n  @keyframes fadeIn {\r\n    to {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .fade-in {\r\n    animation-delay: calc(var(--item-index) * 0.1s);\r\n  }\r\n\r\n  .zoom-in {\r\n    animation: zoomIn 0.5s ease-out;\r\n  }\r\n\r\n  @keyframes zoomIn {\r\n    from {\r\n      transform: scale(0.8);\r\n      opacity: 0;\r\n    }\r\n\r\n    to {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .user-menu {\r\n    list-style-type: none;\r\n    padding: 0;\r\n    margin: 0;\r\n  }\r\n\r\n  .user-menu li {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .user-menu a {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 10px 15px;\r\n    color: #333;\r\n    text-decoration: none;\r\n    border-radius: 5px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .user-menu a:hover {\r\n    background-color: #3bc0c3;\r\n    color: #fff;\r\n  }\r\n\r\n  .user-menu a.router-link-active {\r\n    background-color: #3bc0c3;\r\n    color: #fff;\r\n  }\r\n\r\n  .user-menu i {\r\n    margin-right: 10px;\r\n    font-size: 18px;\r\n  }\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAe;;EAEpBA,KAAK,EAAC;AAAU;;;EAMjBA,KAAK,EAAC;AAAe;;EAEpBA,KAAK,EAAC;AAAW;;;EAMlBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAY;;;;EAIdA,KAAK,EAAC;AAAe;;uBAvBlCC,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJC,mBAAA,CAOM,OAPNC,UAOM,G,0BANJD,mBAAA,CAAmC;IAA/BH,KAAK,EAAC;EAAe,GAAC,MAAI,sBAC9BG,mBAAA,CAIK,MAJLE,UAIK,I,kBAHHJ,mBAAA,CAEKK,SAAA,QAAAC,WAAA,CAFuBC,KAAA,CAAAC,KAAK,GAArBC,IAAI,EAAEC,KAAK;yBAAvBV,mBAAA,CAEK;MAF+BW,GAAG,EAAED;IAAK,IAC5CR,mBAAA,CAAqE;MAAjEU,IAAI,2BAA2BH,IAAI,CAACI;wBAAUJ,IAAI,CAACK,OAAO,wBAAAC,UAAA,E;sCAIpEb,mBAAA,CAOM,OAPNc,UAOM,G,0BANJd,mBAAA,CAAmC;IAA/BH,KAAK,EAAC;EAAe,GAAC,MAAI,sBAC9BG,mBAAA,CAIK,MAJLe,UAIK,I,kBAHHjB,mBAAA,CAEKK,SAAA,QAAAC,WAAA,CAFuCC,KAAA,CAAAW,KAAK,GAArBT,IAAI,EAAEC,KAAK;yBAAvCV,mBAAA,CAEK;MAFDD,KAAK,EAAC,SAAS;MAAiCY,GAAG,EAAED;QACvDR,mBAAA,CAAyF;MAArFU,IAAI,wBAAwBH,IAAI,CAACU;kCAAKjB,mBAAA,CAA2B;MAAxBH,KAAK,EAAC;IAAa,6B,kCAAQU,IAAI,CAACW,KAAK,iB;sCAIxFlB,mBAAA,CAUM,OAVNmB,UAUM,G,0BATJnB,mBAAA,CAAmC;IAA/BH,KAAK,EAAC;EAAe,GAAC,MAAI,sBAC9BG,mBAAA,CAOM,OAPNoB,UAOM,I,kBANJtB,mBAAA,CAKMK,SAAA,QAAAC,WAAA,CALkDC,KAAA,CAAAgB,KAAK,GAArBd,IAAI,EAAEC,KAAK;yBAAnDV,mBAAA,CAKM;MALDD,KAAK,EAAC,oBAAoB;MAAiCY,GAAG,EAAED;QACnER,mBAAA,CAGI;MAHAU,IAAI,sBAAsBH,IAAI,CAACe;QACjCtB,mBAAA,CAAwG;MAAlGuB,GAAG,8CAA8ChB,IAAI,CAACiB,IAAI;MAAEC,KAAmC,EAAnC;QAAA;QAAA;MAAA;0CAClEzB,mBAAA,CAA+C,KAA/C0B,WAA+C,EAAAC,gBAAA,CAAnBpB,IAAI,CAACqB,OAAO,iB", "ignoreList": []}]}