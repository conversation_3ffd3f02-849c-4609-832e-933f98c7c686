{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue", "mtime": 1741617283358}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTWVudScsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGN1cnJlbnRQYXRoOiAnJwogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICAvL+WIpOaWreaYr+WQpueZu+W9lQogICAgdmFyIGxuYW1lID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnbG5hbWUnKTsKICAgIGlmIChsbmFtZSA9PSBudWxsKSB7CiAgICAgIC8v5by55Ye65o+Q56S6CiAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgIG1lc3NhZ2U6ICfor7flhYjnmbvlvZUnLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICBvZmZzZXQ6IDMyMAogICAgICB9KTsKICAgICAgLy/ot7PovazliLDnmbvlvZXpobXpnaIKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9zbG9naW4nKTsKICAgIH0KCiAgICAvLyDojrflj5blvZPliY3ot6/lvoQKICAgIHRoaXMuY3VycmVudFBhdGggPSB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWU7CiAgfSwKICBtZXRob2RzOiB7CiAgICBxdWl0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTpgIDlh7rlkJc/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ2xuYW1lJyk7CiAgICAgICAgX3RoaXMuJHJvdXRlci5wdXNoKCcvc2xvZ2luJyk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICBpc0FjdGl2ZShwYXRoKSB7CiAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRQYXRoID09PSBwYXRoOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "currentPath", "mounted", "lname", "sessionStorage", "getItem", "$message", "message", "type", "offset", "$router", "push", "window", "location", "pathname", "methods", "quit", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "then", "removeItem", "catch", "isActive", "path"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue"], "sourcesContent": ["<template>\n    <div class=\"col-lg-3 sidebar sidebar--left\">\n  <div class=\"sidebar-container\">\n    <div class=\"sidebar-header\">\n      <h3 class=\"sidebar-title\">个人中心</h3>\n      <div class=\"sidebar-divider\"></div>\n    </div>\n\n    <div class=\"sidebar-menu\">\n      <div class=\"menu-group\">\n     \n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Sweclome') }\">\n            <a href=\"/Sweclome\">\n              <i class=\"fas fa-home\"></i>\n              <span>欢迎页面</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-file-alt\"></i>\n          <span>简历管理</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resume_Add') }\">\n            <a href=\"/resume_Add\">\n              <i class=\"fas fa-plus-circle\"></i>\n              <span>创建简历</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resume_manage') }\">\n            <a href=\"/resume_manage\">\n              <i class=\"fas fa-tasks\"></i>\n              <span>管理简历</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-history\"></i>\n          <span>记录查询</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resumedelivery_manage') }\">\n            <a href=\"/resumedelivery_manage\">\n              <i class=\"fas fa-paper-plane\"></i>\n              <span>我的投递记录</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/browsingrecords_manage') }\">\n            <a href=\"/browsingrecords_manage\">\n              <i class=\"fas fa-eye\"></i>\n              <span>我的浏览记录</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">  \n      \n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/bbs_manage') }\">\n            <a href=\"/bbs_manage\">\n              <i class=\"fas fa-comments\"></i>\n              <span>我的帖子</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-user-cog\"></i>\n          <span>账户设置</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Sinfo') }\">\n            <a href=\"/Sinfo\">\n              <i class=\"fas fa-user-edit\"></i>\n              <span>修改个人信息</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Spassword') }\">\n            <a href=\"/Spassword\">\n              <i class=\"fas fa-lock\"></i>\n              <span>修改密码</span>\n            </a>\n          </li>\n          <li class=\"menu-item\">\n            <a @click=\"quit()\" style=\"cursor: pointer\">\n              <i class=\"fas fa-sign-out-alt\"></i>\n              <span>退出登录</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n    </div>\n  </div>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: 'Menu',\n    data() {\n      return {\n        currentPath: ''\n      };\n    },\n    mounted() {\n      //判断是否登录\n      var lname = sessionStorage.getItem('lname');\n      if (lname == null) {\n        //弹出提示\n        this.$message({\n          message: '请先登录',\n          type: 'warning',\n          offset: 320,\n        });\n        //跳转到登录页面\n        this.$router.push('/slogin');\n      }\n\n      // 获取当前路径\n      this.currentPath = window.location.pathname;\n    },\n    methods: {\n      quit() {\n        var _this = this;\n        this.$confirm('确认退出吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            sessionStorage.removeItem('lname');\n            _this.$router.push('/slogin');\n          })\n          .catch(() => { });\n      },\n      isActive(path) {\n        return this.currentPath === path;\n      }\n    },\n  };\n</script>\n\n<style scoped>\n  .sidebar-container {\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n    overflow: hidden;\n  }\n\n  .sidebar-header {\n    padding: 20px;\n    background-color: #3498db;\n    color: #fff;\n  }\n\n  .sidebar-title {\n    margin: 0;\n    font-size: 20px;\n    font-weight: 600;\n  }\n\n  .sidebar-divider {\n    height: 3px;\n    width: 50px;\n    background-color: rgba(255, 255, 255, 0.5);\n    margin-top: 10px;\n    border-radius: 1.5px;\n  }\n\n  .sidebar-menu {\n    padding: 15px 0;\n  }\n\n  .menu-group {\n    margin-bottom: 15px;\n  }\n\n  .menu-group-title {\n    padding: 10px 20px;\n    color: #7f8c8d;\n    font-size: 14px;\n    font-weight: 600;\n    text-transform: uppercase;\n    display: flex;\n    align-items: center;\n  }\n\n  .menu-group-title i {\n    margin-right: 8px;\n    font-size: 16px;\n  }\n\n  .menu-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .menu-item {\n    position: relative;\n  }\n\n  .menu-item a {\n    display: flex;\n    align-items: center;\n    padding: 12px 20px;\n    color: #34495e;\n    transition: all 0.3s ease;\n    text-decoration: none;\n  }\n\n  .menu-item a i {\n    margin-right: 10px;\n    width: 20px;\n    text-align: center;\n    font-size: 16px;\n    color: #7f8c8d;\n    transition: all 0.3s ease;\n  }\n\n  .menu-item a:hover {\n    background-color: #f8f9fa;\n    color: #3498db;\n  }\n\n  .menu-item a:hover i {\n    color: #3498db;\n  }\n\n  .menu-item.active a {\n    background-color: #ebf5fb;\n    color: #3498db;\n    font-weight: 600;\n  }\n\n  .menu-item.active a i {\n    color: #3498db;\n  }\n\n  .menu-item.active::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 0;\n    height: 100%;\n    width: 4px;\n    background-color: #3498db;\n    border-radius: 0 2px 2px 0;\n  }\n\n  /* 响应式设计 */\n  @media (max-width: 992px) {\n    .sidebar-container {\n      margin-bottom: 30px;\n    }\n  }\n</style>"], "mappings": ";AA0GE,eAAe;EACbA,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR;IACA,IAAIC,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAI,IAAK,IAAI,EAAE;MACjB;MACA,IAAI,CAACG,QAAQ,CAAC;QACZC,OAAO,EAAE,MAAM;QACfC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;MACF;MACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;IAC9B;;IAEA;IACA,IAAI,CAACV,WAAU,GAAIW,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAC7C,CAAC;EACDC,OAAO,EAAE;IACPC,IAAIA,CAAA,EAAG;MACL,IAAIC,KAAI,GAAI,IAAI;MAChB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBZ,IAAI,EAAE;MACR,CAAC,EACEa,IAAI,CAAC,MAAM;QACVjB,cAAc,CAACkB,UAAU,CAAC,OAAO,CAAC;QAClCL,KAAK,CAACP,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;MAC/B,CAAC,EACAY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IACDC,QAAQA,CAACC,IAAI,EAAE;MACb,OAAO,IAAI,CAACxB,WAAU,KAAMwB,IAAI;IAClC;EACF;AACF,CAAC", "ignoreList": []}]}