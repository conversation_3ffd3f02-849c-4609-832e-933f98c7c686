{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsManage2.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsManage2.vue", "mtime": 1741536226000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "pname", "catid", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "jobcategoriesList", "created", "getDatas", "getJobCategories", "methods", "url", "post", "params", "then", "res", "code", "resdata", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "pid", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "para", "pflag2", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "id", "handleEdit"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsManage2.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">职位管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">职位列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">职位列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.pname\" placeholder=\"职位名称\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"职位分类\" prop=\"catid\">\n          <el-select v-model=\"filters.catid\" placeholder=\"请选择\" size=\"small\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option v-for=\"item in jobcategoriesList\" :key=\"item.catid\" :label=\"item.catname\" :value=\"item.catid\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"pname\" label=\"职位名称\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"by1\" label=\"职位分类\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"wlocation\" label=\"工作地点\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"rnumber\" label=\"招聘人数\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"streatment\" label=\"薪资待遇\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"pflag\" label=\"招聘状态\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"by2\" label=\"企业\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"ptime\" label=\"发布时间\" align=\"center\"></el-table-column>\n      <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n  </div>\n</template>\n<script>\nimport request, { base } from '../../../../utils/http';\nexport default {\n  name: 'positions',\n  components: {},\n  data() {\n    return {\n      filters: {\n        //列表查询参数\n        pname: '',\n        catid: '',\n      },\n\n      page: {\n        currentPage: 1, // 当前页\n        pageSize: 10, // 每页显示条目个数\n        totalCount: 0, // 总条目数\n      },\n      isClear: false,\n\n      listLoading: false, //列表加载状态\n      btnLoading: false, //保存按钮加载状态\n      datalist: [], //表格数据\n      jobcategoriesList: [],\n    };\n  },\n  created() {\n    this.getDatas();\n    this.getJobCategories();\n  },\n\n  methods: {\n    // 获取职位分类列表\n    getJobCategories() {\n      let url = base + '/jobcategories/list';\n      request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\n        if (res.code == 200) {\n          this.jobcategoriesList = res.resdata;\n        }\n      });\n    },\n\n    // 删除职位\n    handleDelete(index, row) {\n      this.$confirm('确认删除该记录吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      })\n        .then(() => {\n          this.listLoading = true;\n          let url = base + '/positions/del?id=' + row.pid;\n          request.post(url).then((res) => {\n            this.listLoading = false;\n\n            this.$message({\n              message: '删除成功',\n              type: 'success',\n              offset: 320,\n            });\n            this.getDatas();\n          });\n        })\n        .catch(() => { });\n    },\n\n    // 分页\n    handleCurrentChange(val) {\n      this.page.currentPage = val;\n      this.getDatas();\n    },\n\n    //获取列表数据\n    getDatas() {\n      let para = {\n        pname: this.filters.pname,\n        catid: this.filters.catid,\n        pflag2: \"审核通过\",\n      };\n      this.listLoading = true;\n      let url =\n        base +\n        '/positions/list?currentPage=' +\n        this.page.currentPage +\n        '&pageSize=' +\n        this.page.pageSize;\n      request.post(url, para).then((res) => {\n        if (res.resdata.length > 0) {\n          this.isPage = true;\n        } else {\n          this.isPage = false;\n        }\n        this.page.totalCount = res.count;\n        this.datalist = res.resdata;\n        this.listLoading = false;\n      });\n    },\n    //查询\n    query() {\n      this.getDatas();\n    },\n\n    // 查看\n    handleShow(index, row) {\n      this.$router.push({\n        path: '/PositionsDetail',\n        query: {\n          id: row.pid,\n        },\n      });\n    },\n\n    // 编辑\n    handleEdit(index, row) {\n      this.$router.push({\n        path: '/PositionsEdit',\n        query: {\n          id: row.pid,\n        },\n      });\n    },\n  },\n};\n</script>\n<style scoped></style>\n"], "mappings": ";AAwDA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE,CAAC,CAAC;EACdC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;QACP;QACAC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACT,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE;MAAE;MACdC,iBAAiB,EAAE;IACrB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EAEDC,OAAO,EAAE;IACP;IACAD,gBAAgBA,CAAA,EAAG;MACjB,IAAIE,GAAE,GAAIpB,IAAG,GAAI,qBAAqB;MACtCD,OAAO,CAACsB,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,EAAE;QAAEE,MAAM,EAAE;UAAEd,WAAW,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAI;MAAE,CAAC,CAAC,CAACc,IAAI,CAAEC,GAAG,IAAK;QACjF,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAACV,iBAAgB,GAAIS,GAAG,CAACE,OAAO;QACtC;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEV,IAAI,CAAC,MAAM;QACV,IAAI,CAACX,WAAU,GAAI,IAAI;QACvB,IAAIQ,GAAE,GAAIpB,IAAG,GAAI,oBAAmB,GAAI6B,GAAG,CAACK,GAAG;QAC/CnC,OAAO,CAACsB,IAAI,CAACD,GAAG,CAAC,CAACG,IAAI,CAAEC,GAAG,IAAK;UAC9B,IAAI,CAACZ,WAAU,GAAI,KAAK;UAExB,IAAI,CAACuB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfH,IAAI,EAAE,SAAS;YACfI,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAACpB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAqB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAACjC,IAAI,CAACC,WAAU,GAAIgC,GAAG;MAC3B,IAAI,CAACvB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAIwB,IAAG,GAAI;QACTpC,KAAK,EAAE,IAAI,CAACD,OAAO,CAACC,KAAK;QACzBC,KAAK,EAAE,IAAI,CAACF,OAAO,CAACE,KAAK;QACzBoC,MAAM,EAAE;MACV,CAAC;MACD,IAAI,CAAC9B,WAAU,GAAI,IAAI;MACvB,IAAIQ,GAAE,GACJpB,IAAG,GACH,8BAA6B,GAC7B,IAAI,CAACO,IAAI,CAACC,WAAU,GACpB,YAAW,GACX,IAAI,CAACD,IAAI,CAACE,QAAQ;MACpBV,OAAO,CAACsB,IAAI,CAACD,GAAG,EAAEqB,IAAI,CAAC,CAAClB,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACE,OAAO,CAACiB,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAACrC,IAAI,CAACG,UAAS,GAAIc,GAAG,CAACqB,KAAK;QAChC,IAAI,CAAC/B,QAAO,GAAIU,GAAG,CAACE,OAAO;QAC3B,IAAI,CAACd,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACD;IACAkC,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC7B,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACA8B,UAAUA,CAACnB,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACmB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,kBAAkB;QACxBJ,KAAK,EAAE;UACLK,EAAE,EAAEtB,GAAG,CAACK;QACV;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAkB,UAAUA,CAACxB,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACmB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,gBAAgB;QACtBJ,KAAK,EAAE;UACLK,EAAE,EAAEtB,GAAG,CAACK;QACV;MACF,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}