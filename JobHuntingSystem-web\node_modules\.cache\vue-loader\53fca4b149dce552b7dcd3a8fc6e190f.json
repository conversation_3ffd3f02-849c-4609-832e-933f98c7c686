{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsView.vue?vue&type=style&index=0&id=7f1421ea&scoped=true&lang=css", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsView.vue", "mtime": 1741617175000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucG9zaXRpb24tZGV0YWlsIHsNCiAgbWF4LXdpZHRoOiAxMDAwcHg7DQogIG1hcmdpbjogMjBweCBhdXRvOw0KICBwYWRkaW5nOiAwIDIwcHg7DQp9DQoNCi5kZXRhaWwtY2FyZCB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgcGFkZGluZzogMzBweDsNCn0NCg0KLnBvc2l0aW9uLWhlYWRlciB7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWVlOw0KICBwYWRkaW5nLWJvdHRvbTogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLnBvc2l0aW9uLXRpdGxlIHsNCiAgZm9udC1zaXplOiAyNHB4Ow0KICBjb2xvcjogIzMzMzsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLnBvc2l0aW9uLW1ldGEgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDIwcHg7DQogIGNvbG9yOiAjNjY2Ow0KfQ0KDQouc2FsYXJ5IHsNCiAgY29sb3I6ICNmZjRkNGY7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmb250LXNpemU6IDE4cHg7DQp9DQoNCi5sb2NhdGlvbiwNCi5udW1iZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDVweDsNCn0NCg0KLmluZm8tc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDMwcHg7DQp9DQoNCi5zZWN0aW9uLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBjb2xvcjogIzMzMzsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5zZWN0aW9uLWNvbnRlbnQgew0KICBjb2xvcjogIzY2NjsNCiAgbGluZS1oZWlnaHQ6IDEuODsNCn0NCg0KLnBvc2l0aW9uLWZvb3RlciB7DQogIG1hcmdpbi10b3A6IDMwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZy10b3A6IDIwcHg7DQogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZWVlOw0KfQ0KDQoucmVzdW1lLWl0ZW0gew0KICBwYWRkaW5nOiAxNXB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTsNCn0NCg0KLnJlc3VtZS1kYXRlIHsNCiAgbWFyZ2luLWxlZnQ6IDEwcHg7DQogIGNvbG9yOiAjOTk5Ow0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi5uby1yZXN1bWUgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDMwcHggMDsNCn0NCg=="}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsView.vue"], "names": [], "mappings": ";AAkPA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACjB", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/PositionsView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"position-detail\">\r\n    <div class=\"detail-card\">\r\n      <div class=\"position-header\">\r\n        <h1 class=\"position-title\">{{ formData.pname }}</h1>\r\n        <div class=\"position-meta\">\r\n          <span class=\"salary\">{{ formData.streatment }}</span>\r\n          <span class=\"location\"\r\n            ><i class=\"el-icon-location\"></i>工作地点：{{ formData.wlocation }}</span\r\n          >\r\n          <span class=\"number\"><i class=\"el-icon-user\"></i>招聘人数：{{ formData.rnumber }}人</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"position-body\">\r\n        <div class=\"info-section\">\r\n          <h2 class=\"section-title\"><i class=\"el-icon-office-building\"></i>职位要求</h2>\r\n          <div class=\"section-content\" v-html=\"formData.prequirements\"></div>\r\n        </div>\r\n\r\n        <div class=\"info-section\">\r\n          <h2 class=\"section-title\"><i class=\"el-icon-info\"></i>其他信息</h2>\r\n          <div class=\"section-content\">\r\n            <p>发布时间：{{ formData.ptime }}</p>\r\n            <p>职位状态：{{ formData.pflag }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"position-footer\">\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"large\"\r\n          @click=\"handleDelivery\"\r\n          :disabled=\"formData.pflag !== '开放'\"\r\n        >\r\n          投递简历\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 简历选择对话框 -->\r\n    <el-dialog title=\"选择简历\" v-model=\"dialogVisible\" width=\"500px\">\r\n      <div v-if=\"resumeList.length > 0\">\r\n        <el-radio-group v-model=\"selectedResumeId\">\r\n          <div v-for=\"resume in resumeList\" :key=\"resume.rid\" class=\"resume-item\">\r\n            <el-radio :label=\"resume.rid\">\r\n              {{ resume.resumename }}\r\n              <span class=\"resume-date\">创建时间：{{ resume.createdat }}</span>\r\n            </el-radio>\r\n          </div>\r\n        </el-radio-group>\r\n      </div>\r\n      <div v-else class=\"no-resume\">\r\n        <el-empty description=\"暂无简历\">\r\n          <el-button type=\"primary\" @click=\"goToAddResume\">创建简历</el-button>\r\n        </el-empty>\r\n      </div>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitDelivery\" :disabled=\"!selectedResumeId\">\r\n            确 定\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from '../../../utils/http';\r\n\r\nexport default {\r\n  name: 'PositionsView',\r\n  data() {\r\n    return {\r\n      id: '',\r\n      formData: {},\r\n      dialogVisible: false,\r\n      resumeList: [],\r\n      selectedResumeId: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getPositionData();\r\n  },\r\n  methods: {\r\n    // 获取职位详情\r\n    async getPositionData() {\r\n      try {\r\n        let url = base + '/positions/get?id=' + this.id;\r\n        const res = await request.post(url);\r\n        if (res.code === 200) {\r\n          this.formData = res.resdata;\r\n          // 获取职位信息后记录浏览记录\r\n          await this.recordBrowsingHistory();\r\n        }\r\n      } catch (error) {\r\n        console.error('获取职位详情失败:', error);\r\n        this.$message({\r\n          message: '获取职位信息失败，请重试',\r\n          type: 'error',\r\n          offset: 320,\r\n        });\r\n      }\r\n    },\r\n\r\n    // 检查是否已经投递过\r\n    async checkIfDelivered() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      let url = base + '/resumedelivery/list';\r\n      try {\r\n        const res = await request.post(\r\n          url,\r\n          { sno: lname, pid: this.id },\r\n          { params: { currentPage: 1, pageSize: 1 } }\r\n        );\r\n        if (res.code === 200 && res.resdata.length > 0) {\r\n          return true;\r\n        }\r\n        return false;\r\n      } catch (error) {\r\n        console.error('检查投递记录失败:', error);\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 修改处理投递简历按钮点击方法\r\n    async handleDelivery() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      if (!lname) {\r\n        this.$message({\r\n          message: '请先登录',\r\n          type: 'warning',\r\n          offset: 320,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 检查是否已投递\r\n      const hasDelivered = await this.checkIfDelivered();\r\n      if (hasDelivered) {\r\n        this.$message({\r\n          message: '您已经投递过该职位',\r\n          type: 'warning',\r\n          offset: 320,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 获取用户简历列表\r\n      this.getResumeList();\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n    // 获取用户简历列表\r\n    getResumeList() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      let url = base + '/resume/list';\r\n      request\r\n        .post(url, { sno: lname }, { params: { currentPage: 1, pageSize: 100 } })\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            this.resumeList = res.resdata;\r\n          }\r\n        });\r\n    },\r\n\r\n    // 跳转到创建简历页面\r\n    goToAddResume() {\r\n      this.dialogVisible = false;\r\n      this.$router.push('/resume_add');\r\n    },\r\n\r\n    // 提交简历投递\r\n    submitDelivery() {\r\n      if (!this.selectedResumeId) {\r\n        this.$message({\r\n          message: '请选择要投递的简历',\r\n          type: 'warning',\r\n          offset: 320,\r\n        });\r\n        return;\r\n      }\r\n\r\n      const lname = sessionStorage.getItem('lname');\r\n      const data = {\r\n        pid: this.id,\r\n        sno: lname,\r\n        rid: this.selectedResumeId,\r\n      };\r\n\r\n      let url = base + '/resumedelivery/add';\r\n      data.auditstatus = '待审核';\r\n      request.post(url, data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message({\r\n            message: '简历投递成功',\r\n            type: 'success',\r\n            offset: 320,\r\n          });\r\n          this.dialogVisible = false;\r\n        } else {\r\n          this.$message({\r\n            message: res.msg || '投递失败，请重试',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 记录浏览记录\r\n    async recordBrowsingHistory() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      if (!lname) {\r\n        return; // 未登录不记录浏览记录\r\n      }\r\n\r\n      const data = {\r\n        pid: this.id,\r\n        sno: lname,\r\n        btime: new Date().toLocaleString(),\r\n      };\r\n\r\n      try {\r\n        let url = base + '/browsingrecords/add';\r\n        const res = await request.post(url, data);\r\n        if (res.code !== 200) {\r\n          console.error('记录浏览记录失败:', res.msg);\r\n        }\r\n      } catch (error) {\r\n        console.error('记录浏览记录失败:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.position-detail {\r\n  max-width: 1000px;\r\n  margin: 20px auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n.detail-card {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 30px;\r\n}\r\n\r\n.position-header {\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.position-title {\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.position-meta {\r\n  display: flex;\r\n  gap: 20px;\r\n  color: #666;\r\n}\r\n\r\n.salary {\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n}\r\n\r\n.location,\r\n.number {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.info-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.section-content {\r\n  color: #666;\r\n  line-height: 1.8;\r\n}\r\n\r\n.position-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.resume-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.resume-date {\r\n  margin-left: 10px;\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.no-resume {\r\n  text-align: center;\r\n  padding: 30px 0;\r\n}\r\n</style>\r\n"]}]}