{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Index.vue?vue&type=template&id=23543608", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Index.vue", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXY+DQogICAgPFRvcE1lbnUgLz4NCiAgPC9kaXY+DQogIDxyb3V0ZXItdmlldyAvPg0KICA8Rm9vdCAvPg0K"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/Index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <TopMenu />\r\n  </div>\r\n  <router-view />\r\n  <Foot />\r\n</template>\r\n\r\n<script>\r\nimport TopMenu from \"../components/TopMenu\";\r\nimport Foot from \"../components/Foot\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\n\r\n\r\n\r\nexport default {\r\n  name: \"IndexLayout\",\r\n  components: {\r\n    TopMenu,\r\n    Foot,\r\n  },\r\n  data() {\r\n    return {\r\n\r\n    };\r\n  },\r\n\r\n  setup() {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n"]}]}