{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue?vue&type=template&id=3c83f0b7", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue", "mtime": 1741615257361}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImNvbC14eGwtNCBjb2wteGwtNCBjb2wtbGctNCI+DQogICAgPGRpdiBjbGFzcz0iYW5pbWF0ZWQtbGlzdCI+DQogICAgICA8aDIgY2xhc3M9InNlY3Rpb24tdGl0bGUiPuiBjOS9jeWIhuexuzwvaDI+DQogICAgICA8dWwgY2xhc3M9ImZlbi1saXN0Ij4NCiAgICAgICAgPGxpIHYtZm9yPSIoaXRlbSwgaW5kZXgpIGluIGxpc3QxIiA6a2V5PSJpbmRleCI+DQogICAgICAgICAgPGEgOmhyZWY9Iidwb3NpdGlvbnNMaXN0P2NhdGlkPScgKyBpdGVtLmNhdGlkIj57eyBpdGVtLmNhdG5hbWUgfX08L2E+DQogICAgICAgIDwvbGk+DQogICAgICA8L3VsPg0KICAgIDwvZGl2Pg0KICAgIDxkaXYgY2xhc3M9ImFuaW1hdGVkLWxpc3QiPg0KICAgICAgPGgyIGNsYXNzPSJzZWN0aW9uLXRpdGxlIj7mnIDmlrDogYzkvY08L2gyPg0KICAgICAgPHVsIGNsYXNzPSJuZXdzLWxpc3QiPg0KICAgICAgICA8bGkgY2xhc3M9ImZhZGUtaW4iIHYtZm9yPSIoaXRlbSwgaW5kZXgpIGluIGxpc3QyIiA6a2V5PSJpbmRleCI+DQogICAgICAgICAgPGEgOmhyZWY9Iidwb3NpdGlvbnNWaWV3P2lkPScgKyBpdGVtLnBpZCI+PGkgY2xhc3M9ImZhcyBmYS1zdGFyIj48L2k+e3sgaXRlbS5wbmFtZSB9fTwvYT4NCiAgICAgICAgPC9saT4NCiAgICAgIDwvdWw+DQogICAgPC9kaXY+DQogICAgPGRpdiBjbGFzcz0iYW5pbWF0ZWQtZ2FsbGVyeSI+DQogICAgICA8aDIgY2xhc3M9InNlY3Rpb24tdGl0bGUiPuacgOaWsOS8geS4mjwvaDI+DQogICAgICA8ZGl2IGNsYXNzPSJpbWFnZS1ncmlkIj4NCiAgICAgICAgPGRpdiBjbGFzcz0iaW1hZ2UtaXRlbSB6b29tLWluIiB2LWZvcj0iKGl0ZW0sIGluZGV4KSBpbiBsaXN0MyIgOmtleT0iaW5kZXgiPg0KICAgICAgICAgIDxhIDpocmVmPSInY29tcGFueVZpZXc/aWQ9JyArIGl0ZW0uY2lkIj4NCiAgICAgICAgICAgIDxpbWcgOnNyYz0iJ2h0dHA6Ly9sb2NhbGhvc3Q6ODA4OC9Kb2JIdW50aW5nU3lzdGVtLycgKyBpdGVtLmxvZ28iIHN0eWxlPSJ3aWR0aDogMTY1cHg7IGhlaWdodDogMTIzcHgiIC8+DQogICAgICAgICAgICA8cCBjbGFzcz0iaW1hZ2UtY2FwdGlvbiI+e3sgaXRlbS5jb21uYW1lIH19PC9wPg0KICAgICAgICAgIDwvYT4NCiAgICAgICAgPC9kaXY+DQogICAgICA8L2Rpdj4NCiAgICA8L2Rpdj4NCiAgPC9kaXY+DQo="}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACvG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/components/Left.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"col-xxl-4 col-xl-4 col-lg-4\">\r\n    <div class=\"animated-list\">\r\n      <h2 class=\"section-title\">职位分类</h2>\r\n      <ul class=\"fen-list\">\r\n        <li v-for=\"(item, index) in list1\" :key=\"index\">\r\n          <a :href=\"'positionsList?catid=' + item.catid\">{{ item.catname }}</a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"animated-list\">\r\n      <h2 class=\"section-title\">最新职位</h2>\r\n      <ul class=\"news-list\">\r\n        <li class=\"fade-in\" v-for=\"(item, index) in list2\" :key=\"index\">\r\n          <a :href=\"'positionsView?id=' + item.pid\"><i class=\"fas fa-star\"></i>{{ item.pname }}</a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"animated-gallery\">\r\n      <h2 class=\"section-title\">最新企业</h2>\r\n      <div class=\"image-grid\">\r\n        <div class=\"image-item zoom-in\" v-for=\"(item, index) in list3\" :key=\"index\">\r\n          <a :href=\"'companyView?id=' + item.cid\">\r\n            <img :src=\"'http://localhost:8088/JobHuntingSystem/' + item.logo\" style=\"width: 165px; height: 123px\" />\r\n            <p class=\"image-caption\">{{ item.comname }}</p>\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../utils/http';\r\n  export default {\r\n    name: 'Left',\r\n    data() {\r\n      return {\r\n        list1: '',\r\n        list2: '',\r\n        list3: '',\r\n        isLoggedIn: false,\r\n      };\r\n    },\r\n    created() {\r\n      this.checkLoginStatus();\r\n      this.getlist1();\r\n      this.getlist2();\r\n      this.getlist3();\r\n    },\r\n    methods: {\r\n      // 检查登录状态\r\n      checkLoginStatus() {\r\n        const lname = sessionStorage.getItem('lname');\r\n        this.isLoggedIn = !!lname;\r\n      },\r\n\r\n      // 获取职位分类\r\n      getlist1() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + '/jobcategories/list?currentPage=1&pageSize=100';\r\n        request.post(url, para).then((res) => {\r\n          this.list1 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 获取最新职位\r\n      getlist2() {\r\n        let para = {\r\n          pflag: '开放', // 只获取开放状态的职位\r\n          pflag2: '审核通过',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/positions/list?currentPage=1&pageSize=10';\r\n        request.post(url, para).then((res) => {\r\n          this.list2 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 获取最新企业\r\n      getlist3() {\r\n        let para = {\r\n          cflag: '审核通过',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/company/list?currentPage=1&pageSize=6';\r\n        request.post(url, para).then((res) => {\r\n          this.list3 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style>\r\n  .fen-list {\r\n    list-style-type: none;\r\n    padding: 0;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .fen-list li {\r\n    width: calc(50% - 10px);\r\n    margin-bottom: 15px;\r\n    opacity: 0;\r\n    animation: fadeIn 0.5s ease-in forwards;\r\n    font-size: 16px;\r\n    color: #555;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .fen-list li:hover {\r\n    transform: translateY(-5px);\r\n  }\r\n\r\n  .fen-list li a {\r\n    color: #333;\r\n    text-decoration: none;\r\n    display: block;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 100%;\r\n    padding: 10px;\r\n    background-color: #f0f0f0;\r\n    border-radius: 5px;\r\n    transition: background-color 0.3s ease;\r\n  }\r\n\r\n  .fen-list li a:hover {\r\n    color: #3498db;\r\n    background-color: #e0e0e0;\r\n  }\r\n\r\n  .fen-list li i {\r\n    margin-right: 10px;\r\n    color: #3498db;\r\n  }\r\n\r\n  @keyframes fadeIn {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(20px);\r\n    }\r\n\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .animated-list,\r\n  .animated-gallery {\r\n    margin-bottom: 40px;\r\n    background: #f9f9f9;\r\n    border-radius: 10px;\r\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 18px;\r\n    margin-bottom: 20px;\r\n    color: #333;\r\n    border-bottom: 2px solid #3498db;\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .news-list {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  .news-list li {\r\n    margin-bottom: 15px;\r\n    opacity: 0;\r\n    animation: fadeIn 0.5s ease-in forwards;\r\n    display: flex;\r\n    flex-direction: column;\r\n    font-size: 16px;\r\n    color: #555;\r\n  }\r\n\r\n  .news-list li a {\r\n    color: #333;\r\n    text-decoration: none;\r\n    display: flex;\r\n    align-items: center;\r\n    display: block;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .news-list li a:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .news-list li i {\r\n    margin-right: 10px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .news-date {\r\n    font-size: 14px;\r\n    color: #888;\r\n    margin-top: 5px;\r\n    margin-left: 24px;\r\n  }\r\n\r\n  .section-divider {\r\n    border: 0;\r\n    height: 1px;\r\n    background: #e0e0e0;\r\n    margin: 30px 0;\r\n  }\r\n\r\n  .image-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 20px;\r\n  }\r\n\r\n  .image-item {\r\n    position: relative;\r\n    overflow: hidden;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .image-item img {\r\n    width: 100%;\r\n    height: auto;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .image-item:hover img {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  .image-caption {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: #fff;\r\n    padding: 10px;\r\n    margin: 0;\r\n    font-size: 13px;\r\n    text-align: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n  }\r\n\r\n  .image-item:hover .image-caption {\r\n    opacity: 1;\r\n  }\r\n\r\n  @keyframes fadeIn {\r\n    to {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .fade-in {\r\n    animation-delay: calc(var(--item-index) * 0.1s);\r\n  }\r\n\r\n  .zoom-in {\r\n    animation: zoomIn 0.5s ease-out;\r\n  }\r\n\r\n  @keyframes zoomIn {\r\n    from {\r\n      transform: scale(0.8);\r\n      opacity: 0;\r\n    }\r\n\r\n    to {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .user-menu {\r\n    list-style-type: none;\r\n    padding: 0;\r\n    margin: 0;\r\n  }\r\n\r\n  .user-menu li {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .user-menu a {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 10px 15px;\r\n    color: #333;\r\n    text-decoration: none;\r\n    border-radius: 5px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .user-menu a:hover {\r\n    background-color: #3bc0c3;\r\n    color: #fff;\r\n  }\r\n\r\n  .user-menu a.router-link-active {\r\n    background-color: #3bc0c3;\r\n    color: #fff;\r\n  }\r\n\r\n  .user-menu i {\r\n    margin-right: 10px;\r\n    font-size: 18px;\r\n  }\r\n</style>"]}]}