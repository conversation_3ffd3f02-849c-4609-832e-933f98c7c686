{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Leftnav.vue?vue&type=template&id=04abcb37", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Leftnav.vue", "mtime": 1741536471000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoJPFRvcE1lbnUgLz4NCg0KDQoJPGRpdiBjbGFzcz0iY3VzdG9tLWJyZWFkY3J1bWIgY3VzdG9tLWJyZWFkY3J1bWItLWJnIg0KCQk6c3R5bGU9InsgYmFja2dyb3VuZEltYWdlOiAndXJsKCcgKyByZXF1aXJlKCdAL2Fzc2V0cy9pbWFnZXMvYnJlYWRjcnVtYi1iZy5qcGcnKSArICcpJyB9Ij4NCgkJPGRpdiBjbGFzcz0iY29udGFpbmVyIj4NCgkJCTxkaXYgY2xhc3M9InJvdyI+DQoJCQkJPCEtLSBwYWdlIHRpdGxlIC0tPg0KDQoJCQkJPGRpdiBjbGFzcz0iY29sLW1kLTYiPg0KCQkJCQk8aDEgY2xhc3M9InBhZ2UtdGl0bGUiPg0KCQkJCQkJe3sgJHJvdXRlLm1ldGEudGl0bGUgfX0NCgkJCQkJPC9oMT4NCgkJCQk8L2Rpdj4NCgkJCQk8IS0tIGJyZWFkY3J1bWIgLS0+DQoJCQkJPGRpdiBjbGFzcz0iY29sLW1kLTYgYnJlYWRjcnVtYi1tZW51Ij4NCgkJCQkJPG5hdiBhcmlhLWxhYmVsPSJicmVhZGNydW1iIj4NCgkJCQkJCTxvbCBjbGFzcz0iYnJlYWRjcnVtYiI+DQoJCQkJCQkJPGxpIGNsYXNzPSJicmVhZGNydW1iLWl0ZW0iPg0KCQkJCQkJCQk8YSBocmVmPSIvaW5kZXgiPg0KCQkJCQkJCQkJPHNwYW4+DQoJCQkJCQkJCQkJPGkgY2xhc3M9ImZhcyBmYS1ob21lIj4NCgkJCQkJCQkJCQk8L2k+DQoJCQkJCQkJCQk8L3NwYW4+DQoJCQkJCQkJCQnnvZHnq5npppbpobUNCgkJCQkJCQkJPC9hPg0KCQkJCQkJCTwvbGk+DQoJCQkJCQkJPGxpIGNsYXNzPSJicmVhZGNydW1iLWl0ZW0gYWN0aXZlIiBhcmlhLWN1cnJlbnQ9InBhZ2UiPg0KCQkJCQkJCQnigIx7eyAkcm91dGUubWV0YS50aXRsZSB9fQ0KCQkJCQkJCTwvbGk+DQoJCQkJCQk8L29sPg0KCQkJCQk8L25hdj4NCgkJCQk8L2Rpdj4NCgkJCTwvZGl2Pg0KCQk8L2Rpdj4NCgk8L2Rpdj4NCg0KDQoJPGRpdiBjbGFzcz0ibWFpbi1jb250ZW50IHB0LTEyMCI+DQoJCTxkaXYgY2xhc3M9ImNvbnRhaW5lciI+DQoJCQk8ZGl2IGNsYXNzPSJwYi0xMjAiPg0KCQkJCTwhLS0gb3VyIHRlYW0gbGlzdCAtLT4NCgkJCQk8ZGl2IGNsYXNzPSJyb3ciPg0KDQoNCgkJCQkJPGRpdiBjbGFzcz0iY29sLXh4bC04IGNvbC14bC04IGNvbC1sZy04Ij4NCgkJCQkJCTxkaXYgc3R5bGU9ImxpbmUtaGVpZ2h0OiAzMHB4OyI+DQoJCQkJCQkJPHJvdXRlci12aWV3IC8+DQoJCQkJCQk8L2Rpdj4NCgkJCQkJPC9kaXY+DQoJCQkJCTxMZWZ0IC8+DQoNCg0KDQoJCQkJPC9kaXY+DQoJCQk8L2Rpdj4NCgkJPC9kaXY+DQoJCTwhLS0gY2FsbCB0byBhY3Rpb24gYmFubmVyIC0tPg0KCQk8IS0tIGVuZCBjYWxsIHRvIGFjdGlvbiBiYW5uZXIgLS0+DQoJPC9kaXY+DQoNCg0KDQoNCgk8Rm9vdCAvPg0K"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Leftnav.vue"], "names": [], "mappings": ";CACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;;CAGV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KACvB,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACf,CAAC,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC;SACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACN,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;OACJ,CAAC,CAAC,CAAC,CAAC;OACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;OACxB,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC;KACL,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC;GACN,CAAC,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC;CACN,CAAC,CAAC,CAAC,CAAC,CAAC;;;CAGL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;KAGf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC;KACN,CAAC,CAAC,CAAC,CAAC,CAAC;KACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;;;IAIR,CAAC,CAAC,CAAC,CAAC,CAAC;GACN,CAAC,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;CAClC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;CAKL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Leftnav.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\t<TopMenu />\r\n\r\n\r\n\t<div class=\"custom-breadcrumb custom-breadcrumb--bg\"\r\n\t\t:style=\"{ backgroundImage: 'url(' + require('@/assets/images/breadcrumb-bg.jpg') + ')' }\">\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"row\">\r\n\t\t\t\t<!-- page title -->\r\n\r\n\t\t\t\t<div class=\"col-md-6\">\r\n\t\t\t\t\t<h1 class=\"page-title\">\r\n\t\t\t\t\t\t{{ $route.meta.title }}\r\n\t\t\t\t\t</h1>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- breadcrumb -->\r\n\t\t\t\t<div class=\"col-md-6 breadcrumb-menu\">\r\n\t\t\t\t\t<nav aria-label=\"breadcrumb\">\r\n\t\t\t\t\t\t<ol class=\"breadcrumb\">\r\n\t\t\t\t\t\t\t<li class=\"breadcrumb-item\">\r\n\t\t\t\t\t\t\t\t<a href=\"/index\">\r\n\t\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"fas fa-home\">\r\n\t\t\t\t\t\t\t\t\t\t</i>\r\n\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t网站首页\r\n\t\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t<li class=\"breadcrumb-item active\" aria-current=\"page\">\r\n\t\t\t\t\t\t\t\t‌{{ $route.meta.title }}\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t</ol>\r\n\t\t\t\t\t</nav>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\r\n\r\n\t<div class=\"main-content pt-120\">\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"pb-120\">\r\n\t\t\t\t<!-- our team list -->\r\n\t\t\t\t<div class=\"row\">\r\n\r\n\r\n\t\t\t\t\t<div class=\"col-xxl-8 col-xl-8 col-lg-8\">\r\n\t\t\t\t\t\t<div style=\"line-height: 30px;\">\r\n\t\t\t\t\t\t\t<router-view />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<Left />\r\n\r\n\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<!-- call to action banner -->\r\n\t\t<!-- end call to action banner -->\r\n\t</div>\r\n\r\n\r\n\r\n\r\n\t<Foot />\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport Left from \"../../components/Left\";\r\nimport TopMenu from \"../../components/TopMenu\";\r\nimport Foot from \"../../components/Foot\";\r\n\r\nexport default {\r\n\tname: \"Leftnav\",\r\n\tcomponents: {\r\n\t\tLeft,\r\n\t\tTopMenu,\r\n\t\tFoot,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\r\n\t\t};\r\n\t},\r\n\tmounted() {\r\n\r\n\t},\r\n\tcreated() {\r\n\r\n\t},\r\n\tmethods: {\r\n\r\n\t},\r\n};\r\n</script>\r\n\r\n\r\n<style></style>\r\n"]}]}