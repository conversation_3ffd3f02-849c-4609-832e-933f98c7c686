{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue?vue&type=template&id=04724886&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue", "mtime": 1741615349283}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtF,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtF,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC;;MAEJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/CompanyView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"company-detail\">\r\n    <!-- 企业基本信息 -->\r\n    <div class=\"company-card\">\r\n      <div class=\"company-header\">\r\n        <div class=\"company-logo\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.logo\" alt=\"企业logo\" />\r\n        </div>\r\n        <div class=\"company-info\">\r\n          <div class=\"name-action\">\r\n            <h1 class=\"company-name\">{{ formData.comname }}</h1>\r\n            <el-button type=\"primary\" size=\"small\" @click=\"handleSendMessage\" icon=\"el-icon-message\">\r\n              发送私信\r\n            </el-button>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-location\"></i>\r\n            <span>地址：{{ formData.address }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-phone\"></i>\r\n            <span>联系方式：{{ formData.contact }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n            <span>企业规模：{{ formData.scale }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-collection\"></i>\r\n            <span>企业性质：{{ formData.nature }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-time\"></i>\r\n            <span>添加时间：{{ formData.addtime }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"company-description\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          企业简介\r\n        </h2>\r\n        <div class=\"description-content\" v-html=\"formData.introduction\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 企业招聘职位列表 -->\r\n    <div class=\"positions-card\">\r\n      <h2 class=\"section-title\">\r\n        <i class=\"el-icon-suitcase\"></i>\r\n        招聘职位\r\n      </h2>\r\n\r\n      <div v-if=\"positionsList.length > 0\" class=\"positions-list\">\r\n        <div v-for=\"position in positionsList\" :key=\"position.pid\" class=\"position-item\">\r\n          <div class=\"position-header\">\r\n            <h3 class=\"position-title\">{{ position.pname }}</h3>\r\n            <span class=\"salary\">{{ position.streatment }}</span>\r\n          </div>\r\n          <div class=\"position-info\">\r\n            <span><i class=\"el-icon-location\"></i>{{ position.wlocation }}</span>\r\n            <span><i class=\"el-icon-user\"></i>招聘人数：{{ position.rnumber }}人</span>\r\n            <span><i class=\"el-icon-time\"></i>发布时间：{{ position.ptime }}</span>\r\n          </div>\r\n          <div class=\"position-footer\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"viewPosition(position.pid)\"\r\n              :disabled=\"position.pflag !== '开放'\">\r\n              查看详情\r\n            </el-button>\r\n            <el-tag :type=\"position.pflag === '开放' ? 'success' : 'info'\" size=\"small\">\r\n              {{ position.pflag }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <el-empty v-else description=\"暂无招聘职位\"></el-empty>\r\n    </div>\r\n\r\n    <!-- 发送私信对话框 -->\r\n    <el-dialog title=\"发送私信\" v-model=\"dialogVisible\" width=\"500px\">\r\n      <el-form :model=\"messageForm\" ref=\"messageFormRef\" :rules=\"rules\">\r\n        <el-form-item prop=\"content\">\r\n          <el-input type=\"textarea\" v-model=\"messageForm.content\" :rows=\"4\" placeholder=\"请输入私信内容\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"sendMessage\" :loading=\"sending\">发 送</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    name: 'CompanyView',\r\n    data() {\r\n      return {\r\n        id: '',\r\n        formData: {},\r\n        positionsList: [],\r\n        dialogVisible: false,\r\n        sending: false,\r\n        messageForm: {\r\n          content: '',\r\n          clname: '', // 企业账号\r\n          sno: '', // 求职者账号\r\n        },\r\n        rules: {\r\n          content: [\r\n            { required: true, message: '请输入私信内容', trigger: 'blur' },\r\n            { min: 1, max: 500, message: '长度在1到500个字符', trigger: 'blur' }\r\n          ]\r\n        }\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id;\r\n      this.getCompanyData();\r\n      this.getPositionsList();\r\n    },\r\n    methods: {\r\n      // 获取企业信息\r\n      async getCompanyData() {\r\n        try {\r\n          const res = await request.post(base + '/company/get?id=' + this.id);\r\n          if (res.code === 200) {\r\n            this.formData = res.resdata;\r\n          }\r\n        } catch (error) {\r\n          console.error('获取企业信息失败:', error);\r\n          this.$message({\r\n            message: '获取企业信息失败',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      },\r\n\r\n      // 获取企业发布的职位列表\r\n      async getPositionsList() {\r\n        try {\r\n          const res = await request.post(\r\n            base + '/positions/list',\r\n            { cid: this.id, pflag: '开放', pflag2: '审核通过' },\r\n            { params: { currentPage: 1, pageSize: 100 } }\r\n          );\r\n          if (res.code === 200) {\r\n            this.positionsList = res.resdata;\r\n          }\r\n        } catch (error) {\r\n          console.error('获取职位列表失败:', error);\r\n          this.$message({\r\n            message: '获取职位列表失败',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      },\r\n\r\n      // 查看职位详情\r\n      viewPosition(pid) {\r\n        this.$router.push({\r\n          path: '/positionsView',\r\n          query: { id: pid },\r\n        });\r\n      },\r\n\r\n      // 处理发送私信按钮点击\r\n      handleSendMessage() {\r\n        // 检查是否登录\r\n        const sno = sessionStorage.getItem('lname');\r\n        if (!sno) {\r\n          this.$message({\r\n            message: '请先登录后再发送私信',\r\n            type: 'warning',\r\n            offset: 320\r\n          });\r\n          this.$router.push('/Slogin');\r\n          return;\r\n        }\r\n\r\n        this.messageForm.sno = sno;\r\n        this.messageForm.clname = this.formData.clname;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n      // 发送私信\r\n      sendMessage() {\r\n        this.$refs.messageFormRef.validate(async (valid) => {\r\n          if (valid) {\r\n            this.sending = true;\r\n            try {\r\n              const res = await request.post(base + '/mails/add', {\r\n                sno: this.messageForm.sno,\r\n                clname: this.messageForm.clname,\r\n                content: this.messageForm.content,\r\n                readstatus: '未读',\r\n              });\r\n\r\n              if (res.code === 200) {\r\n                this.$message({\r\n                  message: '私信发送成功',\r\n                  type: 'success',\r\n                  offset: 320\r\n                });\r\n                this.dialogVisible = false;\r\n                this.messageForm.content = '';\r\n              } else {\r\n                this.$message({\r\n                  message: res.msg || '发送失败',\r\n                  type: 'error',\r\n                  offset: 320\r\n                });\r\n              }\r\n            } catch (error) {\r\n              this.$message({\r\n                message: '发送失败',\r\n                type: 'error',\r\n                offset: 320\r\n              });\r\n            } finally {\r\n              this.sending = false;\r\n            }\r\n          }\r\n        });\r\n      }\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .company-detail {\r\n    max-width: 1200px;\r\n    margin: 20px auto;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .company-card,\r\n  .positions-card {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    padding: 30px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .company-header {\r\n    display: flex;\r\n    gap: 30px;\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .company-logo {\r\n    width: 120px;\r\n    height: 120px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .company-logo img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .company-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .name-action {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .company-name {\r\n    margin: 0;\r\n  }\r\n\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    margin-bottom: 10px;\r\n    color: #666;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 18px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    border-bottom: 2px solid #409eff;\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .description-content {\r\n    color: #666;\r\n    line-height: 1.8;\r\n  }\r\n\r\n  .positions-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n    gap: 20px;\r\n  }\r\n\r\n  .position-item {\r\n    border: 1px solid #eee;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .position-item:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .position-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .position-title {\r\n    font-size: 16px;\r\n    color: #333;\r\n    margin: 0;\r\n  }\r\n\r\n  .salary {\r\n    color: #ff4d4f;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .position-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin-bottom: 15px;\r\n    color: #666;\r\n  }\r\n\r\n  .position-info span {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 5px;\r\n  }\r\n\r\n  .position-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: right;\r\n    display: block;\r\n  }\r\n</style>"]}]}