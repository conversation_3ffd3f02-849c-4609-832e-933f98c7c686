{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyInfo.vue?vue&type=template&id=13b6a42f", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyInfo.vue", "mtime": 1741602812000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIHJlc29sdmVDb21wb25lbnQgYXMgX3Jlc29sdmVDb21wb25lbnQsIGNyZWF0ZVZOb2RlIGFzIF9jcmVhdGVWTm9kZSwgd2l0aEN0eCBhcyBfd2l0aEN0eCwgY3JlYXRlVGV4dFZOb2RlIGFzIF9jcmVhdGVUZXh0Vk5vZGUsIGNyZWF0ZVN0YXRpY1ZOb2RlIGFzIF9jcmVhdGVTdGF0aWNWTm9kZSwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IHsKICBjbGFzczogInJvdyIKfTsKY29uc3QgX2hvaXN0ZWRfMiA9IHsKICBjbGFzczogImRpYWxvZy1mb290ZXIiCn07CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9pbnB1dCA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1pbnB1dCIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfZm9ybV9pdGVtID0gX3Jlc29sdmVDb21wb25lbnQoImVsLWZvcm0taXRlbSIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfYnV0dG9uID0gX3Jlc29sdmVDb21wb25lbnQoImVsLWJ1dHRvbiIpOwogIGNvbnN0IF9jb21wb25lbnRfV2FuZ0VkaXRvciA9IF9yZXNvbHZlQ29tcG9uZW50KCJXYW5nRWRpdG9yIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9mb3JtID0gX3Jlc29sdmVDb21wb25lbnQoImVsLWZvcm0iKTsKICBjb25zdCBfY29tcG9uZW50X2VsX3VwbG9hZCA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC11cGxvYWQiKTsKICBjb25zdCBfY29tcG9uZW50X2VsX2RpYWxvZyA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1kaWFsb2ciKTsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xLCBbX2NhY2hlWzE1XSB8fCAoX2NhY2hlWzE1XSA9IF9jcmVhdGVTdGF0aWNWTm9kZSgiPGRpdiBjbGFzcz1cImNvbC0xMlwiPjxkaXYgY2xhc3M9XCJwYWdlLXRpdGxlLWJveFwiPjxkaXYgY2xhc3M9XCJwYWdlLXRpdGxlLXJpZ2h0XCI+PG9sIGNsYXNzPVwiYnJlYWRjcnVtYiBtLTBcIj48bGkgY2xhc3M9XCJicmVhZGNydW1iLWl0ZW1cIj48YSBpZD1cInRpdGxlMlwiPuS8geS4muS4reW/gzwvYT48L2xpPjxsaSBjbGFzcz1cImJyZWFkY3J1bWItaXRlbSBhY3RpdmVcIiBpZD1cInRpdGxlM1wiPuS/oeaBr+e7tOaKpDwvbGk+PC9vbD48L2Rpdj48aDQgY2xhc3M9XCJwYWdlLXRpdGxlXCIgaWQ9XCJ0aXRsZTFcIj7kv6Hmga/nu7TmiqQ8L2g0PjwvZGl2PjwvZGl2PiIsIDEpKSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybSwgewogICAgbW9kZWw6ICRkYXRhLmZvcm1EYXRhLAogICAgImxhYmVsLXdpZHRoIjogIjIwJSIsCiAgICByZWY6ICJmb3JtRGF0YVJlZiIsCiAgICBydWxlczogJGRhdGEuYWRkcnVsZXMsCiAgICBhbGlnbjogImxlZnQiCiAgfSwgewogICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIuS8geS4mui0puWPtyIsCiAgICAgIHByb3A6ICJjbG5hbWUiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9pbnB1dCwgewogICAgICAgIG1vZGVsVmFsdWU6ICRkYXRhLmZvcm1EYXRhLmNsbmFtZSwKICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVswXSB8fCAoX2NhY2hlWzBdID0gJGV2ZW50ID0+ICRkYXRhLmZvcm1EYXRhLmNsbmFtZSA9ICRldmVudCksCiAgICAgICAgcGxhY2Vob2xkZXI6ICLkvIHkuJrotKblj7ciLAogICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAid2lkdGgiOiAiNTAlIgogICAgICAgIH0sCiAgICAgICAgZGlzYWJsZWQ6ICIiCiAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIuS8geS4muWQjeensCIsCiAgICAgIHByb3A6ICJjb21uYW1lIgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICBtb2RlbFZhbHVlOiAkZGF0YS5mb3JtRGF0YS5jb21uYW1lLAogICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzFdIHx8IChfY2FjaGVbMV0gPSAkZXZlbnQgPT4gJGRhdGEuZm9ybURhdGEuY29tbmFtZSA9ICRldmVudCksCiAgICAgICAgcGxhY2Vob2xkZXI6ICLkvIHkuJrlkI3np7AiLAogICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAid2lkdGgiOiAiNTAlIgogICAgICAgIH0KICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgIGxhYmVsOiAi5LyB5Lia6KeE5qihIiwKICAgICAgcHJvcDogInNjYWxlIgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICBtb2RlbFZhbHVlOiAkZGF0YS5mb3JtRGF0YS5zY2FsZSwKICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsyXSB8fCAoX2NhY2hlWzJdID0gJGV2ZW50ID0+ICRkYXRhLmZvcm1EYXRhLnNjYWxlID0gJGV2ZW50KSwKICAgICAgICBwbGFjZWhvbGRlcjogIuS8geS4muinhOaooSIsCiAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICJ3aWR0aCI6ICI1MCUiCiAgICAgICAgfQogICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgbGFiZWw6ICLkvIHkuJrmgKfotKgiLAogICAgICBwcm9wOiAibmF0dXJlIgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICBtb2RlbFZhbHVlOiAkZGF0YS5mb3JtRGF0YS5uYXR1cmUsCiAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbM10gfHwgKF9jYWNoZVszXSA9ICRldmVudCA9PiAkZGF0YS5mb3JtRGF0YS5uYXR1cmUgPSAkZXZlbnQpLAogICAgICAgIHBsYWNlaG9sZGVyOiAi5LyB5Lia5oCn6LSoIiwKICAgICAgICBzdHlsZTogewogICAgICAgICAgIndpZHRoIjogIjUwJSIKICAgICAgICB9CiAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIuiBlOezu+aWueW8jyIsCiAgICAgIHByb3A6ICJjb250YWN0IgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICBtb2RlbFZhbHVlOiAkZGF0YS5mb3JtRGF0YS5jb250YWN0LAogICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzRdIHx8IChfY2FjaGVbNF0gPSAkZXZlbnQgPT4gJGRhdGEuZm9ybURhdGEuY29udGFjdCA9ICRldmVudCksCiAgICAgICAgcGxhY2Vob2xkZXI6ICLogZTns7vmlrnlvI8iLAogICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAid2lkdGgiOiAiNTAlIgogICAgICAgIH0KICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgIGxhYmVsOiAi6IGU57O75Zyw5Z2AIiwKICAgICAgcHJvcDogImFkZHJlc3MiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9pbnB1dCwgewogICAgICAgIG1vZGVsVmFsdWU6ICRkYXRhLmZvcm1EYXRhLmFkZHJlc3MsCiAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbNV0gfHwgKF9jYWNoZVs1XSA9ICRldmVudCA9PiAkZGF0YS5mb3JtRGF0YS5hZGRyZXNzID0gJGV2ZW50KSwKICAgICAgICBwbGFjZWhvbGRlcjogIuiBlOezu+WcsOWdgCIsCiAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICJ3aWR0aCI6ICI1MCUiCiAgICAgICAgfQogICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgcHJvcDogImxvZ28iLAogICAgICBsYWJlbDogIuS8geS4mmxvZ28iLAogICAgICAibWluLXdpZHRoIjogIjIwJSIKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2lucHV0LCB7CiAgICAgICAgbW9kZWxWYWx1ZTogJGRhdGEuZm9ybURhdGEubG9nbywKICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVs2XSB8fCAoX2NhY2hlWzZdID0gJGV2ZW50ID0+ICRkYXRhLmZvcm1EYXRhLmxvZ28gPSAkZXZlbnQpLAogICAgICAgIHBsYWNlaG9sZGVyOiAi5LyB5LiabG9nbyIsCiAgICAgICAgcmVhZG9ubHk6ICJ0cnVlIiwKICAgICAgICBzdHlsZTogewogICAgICAgICAgIndpZHRoIjogIjUwJSIKICAgICAgICB9CiAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgICAgdHlwZTogInByaW1hcnkiLAogICAgICAgIHNpemU6ICJzbWFsbCIsCiAgICAgICAgb25DbGljazogJG9wdGlvbnMuc2hvd1VwbG9hZAogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gX2NhY2hlWzldIHx8IChfY2FjaGVbOV0gPSBbX2NyZWF0ZVRleHRWTm9kZSgi5LiK5LygIildKSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJvbkNsaWNrIl0pXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgIGxhYmVsOiAi5LyB5Lia5LuL57uNIiwKICAgICAgcHJvcDogImludHJvZHVjdGlvbiIKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X1dhbmdFZGl0b3IsIHsKICAgICAgICByZWY6ICJ3YW5nRWRpdG9yUmVmIiwKICAgICAgICBtb2RlbFZhbHVlOiAkZGF0YS5mb3JtRGF0YS5pbnRyb2R1Y3Rpb24sCiAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbN10gfHwgKF9jYWNoZVs3XSA9ICRldmVudCA9PiAkZGF0YS5mb3JtRGF0YS5pbnRyb2R1Y3Rpb24gPSAkZXZlbnQpLAogICAgICAgIGNvbmZpZzogX2N0eC5lZGl0b3JDb25maWcsCiAgICAgICAgaXNDbGVhcjogJGRhdGEuaXNDbGVhciwKICAgICAgICBvbkNoYW5nZTogJG9wdGlvbnMuZWRpdG9yQ2hhbmdlCiAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSIsICJjb25maWciLCAiaXNDbGVhciIsICJvbkNoYW5nZSJdKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgbnVsbCwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgICAgdHlwZTogInByaW1hcnkiLAogICAgICAgIHNpemU6ICJzbWFsbCIsCiAgICAgICAgb25DbGljazogJG9wdGlvbnMuc2F2ZSwKICAgICAgICBsb2FkaW5nOiAkZGF0YS5idG5Mb2FkaW5nLAogICAgICAgIGljb246ICJlbC1pY29uLXVwbG9hZCIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IF9jYWNoZVsxMF0gfHwgKF9jYWNoZVsxMF0gPSBbX2NyZWF0ZVRleHRWTm9kZSgi5o+QIOS6pCIpXSkpLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsib25DbGljayIsICJsb2FkaW5nIl0pXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KV0pLAogICAgXzogMSAvKiBTVEFCTEUgKi8KICB9LCA4IC8qIFBST1BTICovLCBbIm1vZGVsIiwgInJ1bGVzIl0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9kaWFsb2csIHsKICAgIG1vZGVsVmFsdWU6ICRkYXRhLnVwbG9hZFZpc2libGUsCiAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVs4XSB8fCAoX2NhY2hlWzhdID0gJGV2ZW50ID0+ICRkYXRhLnVwbG9hZFZpc2libGUgPSAkZXZlbnQpLAogICAgdGl0bGU6ICLpmYTku7bkuIrkvKAiLAogICAgImN1c3RvbS1jbGFzcyI6ICJlbC1kaWFsb2ctd2lkdGhTbWFsbCIsCiAgICBvbkNsb3NlOiBfY3R4LmNsb3NlRGlhbG9nCiAgfSwgewogICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jYWNoZVsxNF0gfHwgKF9jYWNoZVsxNF0gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBudWxsLCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYiIsIG51bGwsICLor7fpgInmi6nmlofku7bvvIhwbmcsanBn5qC85byP77yJ6L+b6KGM5LiK5Lyg77yB4oCDIildLCAtMSAvKiBIT0lTVEVEICovKSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3VwbG9hZCwgewogICAgICBhY3Rpb246ICJodHRwOi8vbG9jYWxob3N0OjgwODgvZ291d3UvYXBpL2NvbW1vbi91cGxvYWRGaWxlIiwKICAgICAgc3R5bGU6IHsKICAgICAgICAibWFyZ2luIjogImF1dG8iLAogICAgICAgICJtYXJnaW4tdG9wIjogIjEwcHgiLAogICAgICAgICJib3JkZXIiOiAiMXB4IHNvbGlkICNkY2RmZTYiLAogICAgICAgICJib3JkZXItcmFkaXVzIjogIjRweCIKICAgICAgfSwKICAgICAgZHJhZzogIiIsCiAgICAgIGxpbWl0OiAxLAogICAgICAib24tcHJldmlldyI6ICRvcHRpb25zLmhhbmRsZVByZXZpZXcsCiAgICAgICJvbi1yZW1vdmUiOiAkb3B0aW9ucy5oYW5kbGVSZW1vdmUsCiAgICAgICJmaWxlLWxpc3QiOiBfY3R4LmZpbGVMaXN0LAogICAgICAib24tZXhjZWVkIjogJG9wdGlvbnMuaGFuZGxlRXhjZWVkLAogICAgICAiYXV0by11cGxvYWQiOiBmYWxzZSwKICAgICAgbmFtZTogImZpbGUiLAogICAgICAib24tY2hhbmdlIjogJG9wdGlvbnMuZmlsZUxpc3RDaGFuZ2UKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gX2NhY2hlWzExXSB8fCAoX2NhY2hlWzExXSA9IFtfY3JlYXRlRWxlbWVudFZOb2RlKCJpIiwgewogICAgICAgIGNsYXNzOiAiZWwtaWNvbi11cGxvYWQiCiAgICAgIH0sIG51bGwsIC0xIC8qIEhPSVNURUQgKi8pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgICAgY2xhc3M6ICJlbC11cGxvYWRfX3RleHQiCiAgICAgIH0sIFtfY3JlYXRlVGV4dFZOb2RlKCLlsIbmlbDmja7mlofku7bmi5bliLDmraTlpITvvIzmiJYiKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZW0iLCBudWxsLCAi54K55Ye75LiK5LygIildLCAtMSAvKiBIT0lTVEVEICovKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICAgIGNsYXNzOiAiZWwtdXBsb2FkX190aXAiCiAgICAgIH0sIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICJkaXNwbGF5IjogImlubGluZSIsCiAgICAgICAgICAiY29sb3IiOiAiI2Q3MDAwMCIsCiAgICAgICAgICAiZm9udC1zaXplIjogIjE0cHgiCiAgICAgICAgfSwKICAgICAgICBjbGFzczogInVwbG9hZEZpbGVXYXJuaW5nIiwKICAgICAgICBpZDogInVwbG9hZEZpbGVXYXJuaW5nIgogICAgICB9KV0sIC0xIC8qIEhPSVNURUQgKi8pXSkpLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSwgOCAvKiBQUk9QUyAqLywgWyJvbi1wcmV2aWV3IiwgIm9uLXJlbW92ZSIsICJmaWxlLWxpc3QiLCAib24tZXhjZWVkIiwgIm9uLWNoYW5nZSJdKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIF9ob2lzdGVkXzIsIFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9idXR0b24sIHsKICAgICAgb25DbGljazogJG9wdGlvbnMuaGlkZVVwbG9hZAogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBfY2FjaGVbMTJdIHx8IChfY2FjaGVbMTJdID0gW19jcmVhdGVUZXh0Vk5vZGUoIuWPliDmtogiKV0pKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0sIDggLyogUFJPUFMgKi8sIFsib25DbGljayJdKSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgIHR5cGU6ICJwcmltYXJ5IiwKICAgICAgb25DbGljazogJG9wdGlvbnMuaGFuZGxlQ29uZmlybQogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBfY2FjaGVbMTNdIHx8IChfY2FjaGVbMTNdID0gW19jcmVhdGVUZXh0Vk5vZGUoIuaPkCDkuqQiKV0pKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0sIDggLyogUFJPUFMgKi8sIFsib25DbGljayJdKV0pXSksCiAgICBfOiAxIC8qIFNUQUJMRSAqLwogIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSIsICJvbkNsb3NlIl0pXSk7Cn0="}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_input", "clname", "$event", "placeholder", "style", "disabled", "comname", "scale", "nature", "contact", "address", "logo", "readonly", "_component_el_button", "type", "size", "onClick", "$options", "showUpload", "_cache", "_component_WangEditor", "introduction", "config", "_ctx", "editorConfig", "isClear", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "save", "loading", "btnLoading", "icon", "_component_el_dialog", "uploadVisible", "title", "onClose", "closeDialog", "_createElementVNode", "_component_el_upload", "action", "drag", "limit", "handlePreview", "handleRemove", "fileList", "handleExceed", "name", "fileListChange", "id", "_hoisted_2", "hideUpload", "handleConfirm"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyInfo.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业中心</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">信息维护</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">信息维护</h4>\n      </div>\n    </div>\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n      <el-form-item label=\"企业账号\" prop=\"clname\">\n        <el-input\n          v-model=\"formData.clname\"\n          placeholder=\"企业账号\"\n          style=\"width: 50%\"\n          disabled\n        ></el-input>\n      </el-form-item>\n      <el-form-item label=\"企业名称\" prop=\"comname\">\n        <el-input v-model=\"formData.comname\" placeholder=\"企业名称\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"企业规模\" prop=\"scale\">\n        <el-input v-model=\"formData.scale\" placeholder=\"企业规模\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"企业性质\" prop=\"nature\">\n        <el-input v-model=\"formData.nature\" placeholder=\"企业性质\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"formData.contact\" placeholder=\"联系方式\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系地址\" prop=\"address\">\n        <el-input v-model=\"formData.address\" placeholder=\"联系地址\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item prop=\"logo\" label=\"企业logo\" min-width=\"20%\">\n        <el-input\n          v-model=\"formData.logo\"\n          placeholder=\"企业logo\"\n          readonly=\"true\"\n          style=\"width: 50%\"\n        ></el-input>\n        <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n      </el-form-item>\n      <el-form-item label=\"企业介绍\" prop=\"introduction\">\n        <WangEditor\n          ref=\"wangEditorRef\"\n          v-model=\"formData.introduction\"\n          :config=\"editorConfig\"\n          :isClear=\"isClear\"\n          @change=\"editorChange\"\n        ></WangEditor>\n      </el-form-item>\n      <el-form-item>\n        <el-button\n          type=\"primary\"\n          size=\"small\"\n          @click=\"save\"\n          :loading=\"btnLoading\"\n          icon=\"el-icon-upload\"\n          >提 交</el-button\n        >\n      </el-form-item>\n    </el-form>\n    <el-dialog\n      v-model=\"uploadVisible\"\n      title=\"附件上传\"\n      custom-class=\"el-dialog-widthSmall\"\n      @close=\"closeDialog\"\n    >\n      <div>\n        <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n      </div>\n      <el-upload\n        action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n        style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\"\n        drag\n        :limit=\"1\"\n        :on-preview=\"handlePreview\"\n        :on-remove=\"handleRemove\"\n        :file-list=\"fileList\"\n        :on-exceed=\"handleExceed\"\n        :auto-upload=\"false\"\n        name=\"file\"\n        :on-change=\"fileListChange\"\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\">\n          <div\n            style=\"display: inline; color: #d70000; font-size: 14px\"\n            class=\"uploadFileWarning\"\n            id=\"uploadFileWarning\"\n          ></div>\n        </div>\n      </el-upload>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"hideUpload\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport request, { base } from '../../../../utils/http';\nimport WangEditor from '../../../components/WangEditor';\nexport default {\n  name: 'CompanyInfo',\n  components: {\n    WangEditor,\n  },\n  data() {\n    return {\n      id: '',\n      isClear: false,\n      uploadVisible: false,\n      btnLoading: false, //保存按钮加载状态\n      formData: {}, //表单数据\n      addrules: {\n        comname: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],\n        scale: [{ required: true, message: '请输入企业规模', trigger: 'blur' }],\n        nature: [{ required: true, message: '请输入企业性质', trigger: 'blur' }],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],\n        address: [{ required: true, message: '请输入联系地址', trigger: 'blur' }],\n        logo: [{ required: true, message: '请输入企业logo', trigger: 'blur' }],\n      },\n    };\n  },\n  created() {\n    var user = JSON.parse(sessionStorage.getItem('user'));\n    this.id = user.cid;\n    this.getDatas();\n  },\n  methods: {\n    //获取列表数据\n    getDatas() {\n      let para = {};\n      this.listLoading = true;\n      let url = base + '/company/get?id=' + this.id;\n      request.post(url, para).then((res) => {\n        this.formData = JSON.parse(JSON.stringify(res.resdata));\n        this.listLoading = false;\n        this.$refs['wangEditorRef'].editor.txt.html(this.formData.introduction);\n      });\n    },\n\n    // 添加\n    save() {\n      this.$refs['formDataRef'].validate((valid) => {\n        //验证表单\n        if (valid) {\n          let url = base + '/company/update';\n          this.btnLoading = true;\n\n          request.post(url, this.formData).then((res) => {\n            //发送请求\n            if (res.code == 200) {\n              this.$message({\n                message: '操作成功',\n                type: 'success',\n                offset: 320,\n              });\n            } else {\n              this.$message({\n                message: res.msg,\n                type: 'error',\n                offset: 320,\n              });\n            }\n            this.btnLoading = false;\n          });\n        }\n      });\n    },\n\n    //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: '只能上传一个文件',\n        type: 'error',\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = 'png,jpg';\n      let extendFileNames = extendFileName.split(',');\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key['name']);\n        }\n        if (!ret) {\n          console.log(key['name'] + ':' + ret);\n          that.$message({\n            duration: 1000,\n            message: '上传的文件后缀必须为' + extendFileName + '格式！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key['name']) != -1) {\n          that.$message({\n            duration: 1000,\n            message: '上传的文件重复！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key['name']);\n        if (fileNames !== '') {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: '请选择文件！',\n          type: 'error',\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append('file', file.raw, file.raw.name);\n      });\n      let url = base + '/common/uploadFile';\n      console.log('url=' + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.logo = furl; // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n\n    // 富文本编辑器\n    editorChange(val) {\n      this.formData.introduction = val;\n    },\n  },\n};\n</script>\n<style scoped></style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAK;;EAiGNA,KAAK,EAAC;AAAe;;;;;;;;;uBAjG/BC,mBAAA,CAsGM,OAtGNC,UAsGM,G,wWA1FJC,YAAA,CAoDUC,kBAAA;IApDAC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAEC,KAAK,EAAC;;sBACrF,MAOe,CAPfR,YAAA,CAOeS,uBAAA;MAPDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAKY,CALZX,YAAA,CAKYY,mBAAA;oBAJDT,KAAA,CAAAC,QAAQ,CAACS,MAAM;mEAAfV,KAAA,CAAAC,QAAQ,CAACS,MAAM,GAAAC,MAAA;QACxBC,WAAW,EAAC,MAAM;QAClBC,KAAkB,EAAlB;UAAA;QAAA,CAAkB;QAClBC,QAAQ,EAAR;;;QAGJjB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACc,OAAO;mEAAhBf,KAAA,CAAAC,QAAQ,CAACc,OAAO,GAAAJ,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAE1DhB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAoF,CAApFX,YAAA,CAAoFY,mBAAA;oBAAjET,KAAA,CAAAC,QAAQ,CAACe,KAAK;mEAAdhB,KAAA,CAAAC,QAAQ,CAACe,KAAK,GAAAL,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAExDhB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAqF,CAArFX,YAAA,CAAqFY,mBAAA;oBAAlET,KAAA,CAAAC,QAAQ,CAACgB,MAAM;mEAAfjB,KAAA,CAAAC,QAAQ,CAACgB,MAAM,GAAAN,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAEzDhB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACiB,OAAO;mEAAhBlB,KAAA,CAAAC,QAAQ,CAACiB,OAAO,GAAAP,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAE1DhB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACkB,OAAO;mEAAhBnB,KAAA,CAAAC,QAAQ,CAACkB,OAAO,GAAAR,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACC,KAAkB,EAAlB;UAAA;QAAA;;;QAE1DhB,YAAA,CAQeS,uBAAA;MARDE,IAAI,EAAC,MAAM;MAACD,KAAK,EAAC,QAAQ;MAAC,WAAS,EAAC;;wBACjD,MAKY,CALZV,YAAA,CAKYY,mBAAA;oBAJDT,KAAA,CAAAC,QAAQ,CAACmB,IAAI;mEAAbpB,KAAA,CAAAC,QAAQ,CAACmB,IAAI,GAAAT,MAAA;QACtBC,WAAW,EAAC,QAAQ;QACpBS,QAAQ,EAAC,MAAM;QACfR,KAAkB,EAAlB;UAAA;QAAA;+CAEFhB,YAAA,CAAyEyB,oBAAA;QAA9DC,IAAI,EAAC,SAAS;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC;;0BAAY,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;QAE/D/B,YAAA,CAQeS,uBAAA;MARDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAMc,CANdX,YAAA,CAMcgC,qBAAA;QALZ3B,GAAG,EAAC,eAAe;oBACVF,KAAA,CAAAC,QAAQ,CAAC6B,YAAY;mEAArB9B,KAAA,CAAAC,QAAQ,CAAC6B,YAAY,GAAAnB,MAAA;QAC7BoB,MAAM,EAAEC,IAAA,CAAAC,YAAY;QACpBC,OAAO,EAAElC,KAAA,CAAAkC,OAAO;QAChBC,QAAM,EAAET,QAAA,CAAAU;;;QAGbvC,YAAA,CASeS,uBAAA;wBARb,MAOC,CAPDT,YAAA,CAOCyB,oBAAA;QANCC,IAAI,EAAC,SAAS;QACdC,IAAI,EAAC,OAAO;QACXC,OAAK,EAAEC,QAAA,CAAAW,IAAI;QACXC,OAAO,EAAEtC,KAAA,CAAAuC,UAAU;QACpBC,IAAI,EAAC;;0BACJ,MAAGZ,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;;;;yCAIV/B,YAAA,CAoCY4C,oBAAA;gBAnCDzC,KAAA,CAAA0C,aAAa;+DAAb1C,KAAA,CAAA0C,aAAa,GAAA/B,MAAA;IACtBgC,KAAK,EAAC,MAAM;IACZ,cAAY,EAAC,sBAAsB;IAClCC,OAAK,EAAEZ,IAAA,CAAAa;;sBAER,MAEM,C,4BAFNC,mBAAA,CAEM,cADJA,mBAAA,CAAkC,WAA/B,wBAA2B,E,sBAEhCjD,YAAA,CAsBYkD,oBAAA;MArBVC,MAAM,EAAC,mDAAmD;MAC1DnC,KAAqF,EAArF;QAAA;QAAA;QAAA;QAAA;MAAA,CAAqF;MACrFoC,IAAI,EAAJ,EAAI;MACHC,KAAK,EAAE,CAAC;MACR,YAAU,EAAExB,QAAA,CAAAyB,aAAa;MACzB,WAAS,EAAEzB,QAAA,CAAA0B,YAAY;MACvB,WAAS,EAAEpB,IAAA,CAAAqB,QAAQ;MACnB,WAAS,EAAE3B,QAAA,CAAA4B,YAAY;MACvB,aAAW,EAAE,KAAK;MACnBC,IAAI,EAAC,MAAM;MACV,WAAS,EAAE7B,QAAA,CAAA8B;;wBAEZ,MAA8B5B,MAAA,SAAAA,MAAA,QAA9BkB,mBAAA,CAA8B;QAA3BpD,KAAK,EAAC;MAAgB,4BACzBoD,mBAAA,CAA2D;QAAtDpD,KAAK,EAAC;MAAiB,I,iBAAC,aAAW,GAAAoD,mBAAA,CAAa,YAAT,MAAI,E,qBAChDA,mBAAA,CAMM;QANDpD,KAAK,EAAC;MAAgB,IACzBoD,mBAAA,CAIO;QAHLjC,KAAwD,EAAxD;UAAA;UAAA;UAAA;QAAA,CAAwD;QACxDnB,KAAK,EAAC,mBAAmB;QACzB+D,EAAE,EAAC;;;2FAITX,mBAAA,CAGO,QAHPY,UAGO,GAFL7D,YAAA,CAA8CyB,oBAAA;MAAlCG,OAAK,EAAEC,QAAA,CAAAiC;IAAU;wBAAE,MAAG/B,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;oCAClC/B,YAAA,CAAgEyB,oBAAA;MAArDC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAEC,QAAA,CAAAkC;;wBAAe,MAAGhC,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E", "ignoreList": []}]}