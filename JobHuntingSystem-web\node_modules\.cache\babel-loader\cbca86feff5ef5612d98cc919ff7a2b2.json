{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue?vue&type=template&id=04724886&scoped=true", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue", "mtime": 1749118278931}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "src", "$data", "formData", "logo", "alt", "qualification", "_hoisted_6", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_toDisplayString", "comname", "_hoisted_11", "address", "_hoisted_12", "contact", "_hoisted_13", "scale", "_hoisted_14", "nature", "_hoisted_15", "addtime", "_hoisted_16", "innerHTML", "introduction", "_hoisted_18", "positionsList", "length", "_hoisted_19", "_Fragment", "_renderList", "position", "key", "pid", "_hoisted_20", "_hoisted_21", "pname", "_hoisted_22", "streatment", "_hoisted_23", "wlocation", "rnumber", "ptime", "_hoisted_24", "_createVNode", "_component_el_button", "type", "size", "onClick", "$event", "$options", "viewPosition", "disabled", "pflag", "_cache", "_component_el_tag", "_createBlock", "_component_el_empty", "description", "_component_el_dialog", "title", "dialogVisible", "width", "footer", "_withCtx", "_hoisted_25", "_ctx", "sendMessage", "loading", "sending", "_component_el_form", "model", "messageForm", "ref", "rules", "_component_el_form_item", "prop", "_component_el_input", "content", "rows", "placeholder"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"company-detail\">\r\n    <!-- 企业基本信息 -->\r\n    <div class=\"company-card\">\r\n      <div class=\"company-header\">\r\n        <div class=\"company-logo\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.logo\" alt=\"企业logo\" />\r\n        </div>\r\n        <div class=\"company-qualification\" v-if=\"formData.qualification\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.qualification\" alt=\"资质图片\" />\r\n        </div>\r\n        <div class=\"company-info\">\r\n          <div class=\"name-action\">\r\n            <h1 class=\"company-name\">{{ formData.comname }}</h1>\r\n          \r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-location\"></i>\r\n            <span>地址：{{ formData.address }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-phone\"></i>\r\n            <span>联系方式：{{ formData.contact }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n            <span>企业规模：{{ formData.scale }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-collection\"></i>\r\n            <span>企业性质：{{ formData.nature }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-time\"></i>\r\n            <span>添加时间：{{ formData.addtime }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"company-description\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          企业简介\r\n        </h2>\r\n        <div class=\"description-content\" v-html=\"formData.introduction\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 企业招聘职位列表 -->\r\n    <div class=\"positions-card\">\r\n      <h2 class=\"section-title\">\r\n        <i class=\"el-icon-suitcase\"></i>\r\n        招聘职位\r\n      </h2>\r\n\r\n      <div v-if=\"positionsList.length > 0\" class=\"positions-list\">\r\n        <div v-for=\"position in positionsList\" :key=\"position.pid\" class=\"position-item\">\r\n          <div class=\"position-header\">\r\n            <h3 class=\"position-title\">{{ position.pname }}</h3>\r\n            <span class=\"salary\">{{ position.streatment }}</span>\r\n          </div>\r\n          <div class=\"position-info\">\r\n            <span><i class=\"el-icon-location\"></i>{{ position.wlocation }}</span>\r\n            <span><i class=\"el-icon-user\"></i>招聘人数：{{ position.rnumber }}人</span>\r\n            <span><i class=\"el-icon-time\"></i>发布时间：{{ position.ptime }}</span>\r\n          </div>\r\n          <div class=\"position-footer\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"viewPosition(position.pid)\"\r\n              :disabled=\"position.pflag !== '开放'\">\r\n              查看详情\r\n            </el-button>\r\n            <el-tag :type=\"position.pflag === '开放' ? 'success' : 'info'\" size=\"small\">\r\n              {{ position.pflag }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <el-empty v-else description=\"暂无招聘职位\"></el-empty>\r\n    </div>\r\n\r\n    <!-- 发送私信对话框 -->\r\n    <el-dialog title=\"发送私信\" v-model=\"dialogVisible\" width=\"500px\">\r\n      <el-form :model=\"messageForm\" ref=\"messageFormRef\" :rules=\"rules\">\r\n        <el-form-item prop=\"content\">\r\n          <el-input type=\"textarea\" v-model=\"messageForm.content\" :rows=\"4\" placeholder=\"请输入私信内容\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"sendMessage\" :loading=\"sending\">发 送</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    name: 'CompanyView',\r\n    data() {\r\n      return {\r\n        id: '',\r\n        formData: {},\r\n        positionsList: [],\r\n        dialogVisible: false,\r\n        sending: false,\r\n        messageForm: {\r\n          content: '',\r\n          clname: '', // 企业账号\r\n          sno: '', // 求职者账号\r\n        },\r\n        rules: {\r\n          content: [\r\n            { required: true, message: '请输入私信内容', trigger: 'blur' },\r\n            { min: 1, max: 500, message: '长度在1到500个字符', trigger: 'blur' }\r\n          ]\r\n        }\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id;\r\n      this.getCompanyData();\r\n      this.getPositionsList();\r\n    },\r\n    methods: {\r\n      // 获取企业信息\r\n      async getCompanyData() {\r\n        try {\r\n          const res = await request.post(base + '/company/get?id=' + this.id);\r\n          if (res.code === 200) {\r\n            this.formData = res.resdata;\r\n          }\r\n        } catch (error) {\r\n          console.error('获取企业信息失败:', error);\r\n          this.$message({\r\n            message: '获取企业信息失败',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      },\r\n\r\n      // 获取企业发布的职位列表\r\n      async getPositionsList() {\r\n        try {\r\n          const res = await request.post(\r\n            base + '/positions/list',\r\n            { cid: this.id, pflag: '开放', pflag2: '审核通过' },\r\n            { params: { currentPage: 1, pageSize: 100 } }\r\n          );\r\n          if (res.code === 200) {\r\n            this.positionsList = res.resdata;\r\n          }\r\n        } catch (error) {\r\n          console.error('获取职位列表失败:', error);\r\n          this.$message({\r\n            message: '获取职位列表失败',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      },\r\n\r\n      // 查看职位详情\r\n      viewPosition(pid) {\r\n        this.$router.push({\r\n          path: '/positionsView',\r\n          query: { id: pid },\r\n        });\r\n      },\r\n\r\n      // 处理发送私信按钮点击\r\n      handleSendMessage() {\r\n        // 检查是否登录\r\n        const sno = sessionStorage.getItem('lname');\r\n        if (!sno) {\r\n          this.$message({\r\n            message: '请先登录后再发送私信',\r\n            type: 'warning',\r\n            offset: 320\r\n          });\r\n          this.$router.push('/Slogin');\r\n          return;\r\n        }\r\n\r\n        this.messageForm.sno = sno;\r\n        this.messageForm.clname = this.formData.clname;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n   \r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .company-detail {\r\n    max-width: 1200px;\r\n    margin: 20px auto;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .company-card,\r\n  .positions-card {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    padding: 30px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .company-header {\r\n    display: flex;\r\n    gap: 30px;\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .company-logo {\r\n    width: 120px;\r\n    height: 120px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .company-logo img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .company-qualification {\r\n    width: 120px;\r\n    height: 120px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .company-qualification img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .company-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .name-action {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .company-name {\r\n    margin: 0;\r\n  }\r\n\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    margin-bottom: 10px;\r\n    color: #666;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 18px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    border-bottom: 2px solid #409eff;\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .description-content {\r\n    color: #666;\r\n    line-height: 1.8;\r\n  }\r\n\r\n  .positions-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n    gap: 20px;\r\n  }\r\n\r\n  .position-item {\r\n    border: 1px solid #eee;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .position-item:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .position-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .position-title {\r\n    font-size: 16px;\r\n    color: #333;\r\n    margin: 0;\r\n  }\r\n\r\n  .salary {\r\n    color: #ff4d4f;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .position-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin-bottom: 15px;\r\n    color: #666;\r\n  }\r\n\r\n  .position-info span {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 5px;\r\n  }\r\n\r\n  .position-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: right;\r\n    display: block;\r\n  }\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;;;EAGpBA,KAAK,EAAC;;;;EAGNA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAClBA,KAAK,EAAC;AAAc;;EAGrBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAOrBA,KAAK,EAAC;AAAqB;;;EAU7BA,KAAK,EAAC;AAAgB;;;EAMYA,KAAK,EAAC;;;EAElCA,KAAK,EAAC;AAAiB;;EACtBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAQ;;EAEjBA,KAAK,EAAC;AAAe;;EAKrBA,KAAK,EAAC;AAAiB;;EAuBxBA,KAAK,EAAC;AAAe;;;;;;;;;uBAxFjCC,mBAAA,CA8FM,OA9FNC,UA8FM,GA7FJC,mBAAA,YAAe,EACfC,mBAAA,CA2CM,OA3CNC,UA2CM,GA1CJD,mBAAA,CAiCM,OAjCNE,UAiCM,GAhCJF,mBAAA,CAEM,OAFNG,UAEM,GADJH,mBAAA,CAAqF;IAA/EI,GAAG,8CAA8CC,KAAA,CAAAC,QAAQ,CAACC,IAAI;IAAEC,GAAG,EAAC;yCAEnCH,KAAA,CAAAC,QAAQ,CAACG,aAAa,I,cAA/DZ,mBAAA,CAEM,OAFNa,UAEM,GADJV,mBAAA,CAA4F;IAAtFI,GAAG,8CAA8CC,KAAA,CAAAC,QAAQ,CAACG,aAAa;IAAED,GAAG,EAAC;8EAErFR,mBAAA,CAyBM,OAzBNW,UAyBM,GAxBJX,mBAAA,CAGM,OAHNY,UAGM,GAFJZ,mBAAA,CAAoD,MAApDa,WAAoD,EAAAC,gBAAA,CAAxBT,KAAA,CAAAC,QAAQ,CAACS,OAAO,iB,GAG9Cf,mBAAA,CAGM,OAHNgB,WAGM,G,0BAFJhB,mBAAA,CAAgC;IAA7BJ,KAAK,EAAC;EAAkB,6BAC3BI,mBAAA,CAAsC,cAAhC,KAAG,GAAAc,gBAAA,CAAGT,KAAA,CAAAC,QAAQ,CAACW,OAAO,iB,GAE9BjB,mBAAA,CAGM,OAHNkB,WAGM,G,0BAFJlB,mBAAA,CAA6B;IAA1BJ,KAAK,EAAC;EAAe,6BACxBI,mBAAA,CAAwC,cAAlC,OAAK,GAAAc,gBAAA,CAAGT,KAAA,CAAAC,QAAQ,CAACa,OAAO,iB,GAEhCnB,mBAAA,CAGM,OAHNoB,WAGM,G,0BAFJpB,mBAAA,CAAuC;IAApCJ,KAAK,EAAC;EAAyB,6BAClCI,mBAAA,CAAsC,cAAhC,OAAK,GAAAc,gBAAA,CAAGT,KAAA,CAAAC,QAAQ,CAACe,KAAK,iB,GAE9BrB,mBAAA,CAGM,OAHNsB,WAGM,G,0BAFJtB,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,6BAC7BI,mBAAA,CAAuC,cAAjC,OAAK,GAAAc,gBAAA,CAAGT,KAAA,CAAAC,QAAQ,CAACiB,MAAM,iB,GAE/BvB,mBAAA,CAGM,OAHNwB,WAGM,G,0BAFJxB,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,6BACvBI,mBAAA,CAAwC,cAAlC,OAAK,GAAAc,gBAAA,CAAGT,KAAA,CAAAC,QAAQ,CAACmB,OAAO,iB,OAKpCzB,mBAAA,CAMM,OANN0B,WAMM,G,0BALJ1B,mBAAA,CAGK;IAHDJ,KAAK,EAAC;EAAe,IACvBI,mBAAA,CAAgC;IAA7BJ,KAAK,EAAC;EAAkB,I,iBAAK,QAElC,E,sBACAI,mBAAA,CAAsE;IAAjEJ,KAAK,EAAC,qBAAqB;IAAC+B,SAA8B,EAAtBtB,KAAA,CAAAC,QAAQ,CAACsB;4CAItD7B,mBAAA,cAAiB,EACjBC,mBAAA,CA8BM,OA9BN6B,WA8BM,G,4BA7BJ7B,mBAAA,CAGK;IAHDJ,KAAK,EAAC;EAAe,IACvBI,mBAAA,CAAgC;IAA7BJ,KAAK,EAAC;EAAkB,I,iBAAK,QAElC,E,sBAEWS,KAAA,CAAAyB,aAAa,CAACC,MAAM,Q,cAA/BlC,mBAAA,CAqBM,OArBNmC,WAqBM,I,kBApBJnC,mBAAA,CAmBMoC,SAAA,QAAAC,WAAA,CAnBkB7B,KAAA,CAAAyB,aAAa,EAAzBK,QAAQ;yBAApBtC,mBAAA,CAmBM;MAnBkCuC,GAAG,EAAED,QAAQ,CAACE,GAAG;MAAEzC,KAAK,EAAC;QAC/DI,mBAAA,CAGM,OAHNsC,WAGM,GAFJtC,mBAAA,CAAoD,MAApDuC,WAAoD,EAAAzB,gBAAA,CAAtBqB,QAAQ,CAACK,KAAK,kBAC5CxC,mBAAA,CAAqD,QAArDyC,WAAqD,EAAA3B,gBAAA,CAA7BqB,QAAQ,CAACO,UAAU,iB,GAE7C1C,mBAAA,CAIM,OAJN2C,WAIM,GAHJ3C,mBAAA,CAAqE,e,0BAA/DA,mBAAA,CAAgC;MAA7BJ,KAAK,EAAC;IAAkB,6B,kCAAQuC,QAAQ,CAACS,SAAS,iB,GAC3D5C,mBAAA,CAAqE,e,4BAA/DA,mBAAA,CAA4B;MAAzBJ,KAAK,EAAC;IAAc,6B,iBAAK,OAAK,GAAAkB,gBAAA,CAAGqB,QAAQ,CAACU,OAAO,IAAG,GAAC,gB,GAC9D7C,mBAAA,CAAkE,e,4BAA5DA,mBAAA,CAA4B;MAAzBJ,KAAK,EAAC;IAAc,6B,iBAAK,OAAK,GAAAkB,gBAAA,CAAGqB,QAAQ,CAACW,KAAK,iB,KAE1D9C,mBAAA,CAQM,OARN+C,WAQM,GAPJC,YAAA,CAGYC,oBAAA;MAHDC,IAAI,EAAC,SAAS;MAACC,IAAI,EAAC,OAAO;MAAEC,OAAK,EAAAC,MAAA,IAAEC,QAAA,CAAAC,YAAY,CAACpB,QAAQ,CAACE,GAAG;MACrEmB,QAAQ,EAAErB,QAAQ,CAACsB,KAAK;;wBAAW,MAEtC,KAAAC,MAAA,SAAAA,MAAA,Q,iBAFsC,QAEtC,E;;kEACAV,YAAA,CAESW,iBAAA;MAFAT,IAAI,EAAEf,QAAQ,CAACsB,KAAK;MAAgCN,IAAI,EAAC;;wBAChE,MAAoB,C,kCAAjBhB,QAAQ,CAACsB,KAAK,iB;;;qDAMzBG,YAAA,CAAiDC,mBAAA;;IAAhCC,WAAW,EAAC;SAG/B/D,mBAAA,aAAgB,EAChBiD,YAAA,CAYYe,oBAAA;IAZDC,KAAK,EAAC,MAAM;gBAAU3D,KAAA,CAAA4D,aAAa;+DAAb5D,KAAA,CAAA4D,aAAa,GAAAZ,MAAA;IAAEa,KAAK,EAAC;;IAMzCC,MAAM,EAAAC,QAAA,CACf,MAGO,CAHPpE,mBAAA,CAGO,QAHPqE,WAGO,GAFLrB,YAAA,CAAyDC,oBAAA;MAA7CG,OAAK,EAAAM,MAAA,QAAAA,MAAA,MAAAL,MAAA,IAAEhD,KAAA,CAAA4D,aAAa;;wBAAU,MAAGP,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;QAC7CV,YAAA,CAAiFC,oBAAA;MAAtEC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAEkB,IAAA,CAAAC,WAAW;MAAGC,OAAO,EAAEnE,KAAA,CAAAoE;;wBAAS,MAAGf,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;sBARzE,MAIU,CAJVV,YAAA,CAIU0B,kBAAA;MAJAC,KAAK,EAAEtE,KAAA,CAAAuE,WAAW;MAAEC,GAAG,EAAC,gBAAgB;MAAEC,KAAK,EAAEzE,KAAA,CAAAyE;;wBACzD,MAEe,CAFf9B,YAAA,CAEe+B,uBAAA;QAFDC,IAAI,EAAC;MAAS;0BAC1B,MAAmG,CAAnGhC,YAAA,CAAmGiC,mBAAA;UAAzF/B,IAAI,EAAC,UAAU;sBAAU7C,KAAA,CAAAuE,WAAW,CAACM,OAAO;qEAAnB7E,KAAA,CAAAuE,WAAW,CAACM,OAAO,GAAA7B,MAAA;UAAG8B,IAAI,EAAE,CAAC;UAAEC,WAAW,EAAC", "ignoreList": []}]}