{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsManage3.vue?vue&type=style&index=0&id=9ab0a5ba&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsManage3.vue", "mtime": 1741536213000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci8qIOa3u+WKoOaMiemSruagt+W8jyAqLwouZWwtYnV0dG9uKy5lbC1idXR0b24gewogIG1hcmdpbi1sZWZ0OiA1cHg7Cn0K"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsManage3.vue"], "names": [], "mappings": ";AA0PA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/positions/PositionsManage3.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">职位管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">职位列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">职位列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.pname\" placeholder=\"职位名称\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"职位分类\" prop=\"catid\">\n          <el-select v-model=\"filters.catid\" placeholder=\"请选择\" size=\"small\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option v-for=\"item in jobcategoriesList\" :key=\"item.catid\" :label=\"item.catname\" :value=\"item.catid\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"pname\" label=\"职位名称\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"by1\" label=\"职位分类\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"wlocation\" label=\"工作地点\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"rnumber\" label=\"招聘人数\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"streatment\" label=\"薪资待遇\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"pflag\" label=\"招聘状态\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"by2\" label=\"企业\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"ptime\" label=\"发布时间\" align=\"center\"></el-table-column>\n      <el-table-column label=\"操作\" min-width=\"300\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\n          <el-button type=\"success\" size=\"mini\" @click=\"handleApprove(scope.$index, scope.row)\" icon=\"el-icon-check\"\n            style=\"padding: 3px 6px 3px 6px\">审核通过</el-button>\n          <el-button type=\"warning\" size=\"mini\" @click=\"handleReject(scope.$index, scope.row)\" icon=\"el-icon-close\"\n            style=\"padding: 3px 6px 3px 6px\">审核不过</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n  </div>\n</template>\n<script>\nimport request, { base } from '../../../../utils/http';\nexport default {\n  name: 'positions',\n  components: {},\n  data() {\n    return {\n      filters: {\n        //列表查询参数\n        pname: '',\n        catid: '',\n      },\n\n      page: {\n        currentPage: 1, // 当前页\n        pageSize: 10, // 每页显示条目个数\n        totalCount: 0, // 总条目数\n      },\n      isClear: false,\n\n      listLoading: false, //列表加载状态\n      btnLoading: false, //保存按钮加载状态\n      datalist: [], //表格数据\n      jobcategoriesList: [],\n    };\n  },\n  created() {\n    this.getDatas();\n    this.getJobCategories();\n  },\n\n  methods: {\n    // 获取职位分类列表\n    getJobCategories() {\n      let url = base + '/jobcategories/list';\n      request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\n        if (res.code == 200) {\n          this.jobcategoriesList = res.resdata;\n        }\n      });\n    },\n\n    // 删除职位\n    handleDelete(index, row) {\n      this.$confirm('确认删除该记录吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      })\n        .then(() => {\n          this.listLoading = true;\n          let url = base + '/positions/del?id=' + row.pid;\n          request.post(url).then((res) => {\n            this.listLoading = false;\n\n            this.$message({\n              message: '删除成功',\n              type: 'success',\n              offset: 320,\n            });\n            this.getDatas();\n          });\n        })\n        .catch(() => { });\n    },\n\n    // 分页\n    handleCurrentChange(val) {\n      this.page.currentPage = val;\n      this.getDatas();\n    },\n\n    //获取列表数据\n    getDatas() {\n      let para = {\n        pname: this.filters.pname,\n        catid: this.filters.catid,\n        pflag2: \"未审核\",\n      };\n      this.listLoading = true;\n      let url =\n        base +\n        '/positions/list?currentPage=' +\n        this.page.currentPage +\n        '&pageSize=' +\n        this.page.pageSize;\n      request.post(url, para).then((res) => {\n        if (res.resdata.length > 0) {\n          this.isPage = true;\n        } else {\n          this.isPage = false;\n        }\n        this.page.totalCount = res.count;\n        this.datalist = res.resdata;\n        this.listLoading = false;\n      });\n    },\n    //查询\n    query() {\n      this.getDatas();\n    },\n\n    // 查看\n    handleShow(index, row) {\n      this.$router.push({\n        path: '/PositionsDetail',\n        query: {\n          id: row.pid,\n        },\n      });\n    },\n\n    // 编辑\n    handleEdit(index, row) {\n      this.$router.push({\n        path: '/PositionsEdit',\n        query: {\n          id: row.pid,\n        },\n      });\n    },\n\n    // 审核通过\n    handleApprove(index, row) {\n      this.$confirm('确认审核通过该职位吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'success'\n      }).then(() => {\n        this.listLoading = true;\n        let url = base + '/positions/update';\n        let para = {\n          pid: row.pid,\n          pflag2: \"审核通过\"\n        };\n        request.post(url, para).then((res) => {\n          this.listLoading = false;\n          if (res.code === 200) {\n            this.$message({\n              message: '审核通过成功',\n              type: 'success',\n              offset: 320\n            });\n            this.getDatas();\n          } else {\n            this.$message({\n              message: res.msg || '操作失败',\n              type: 'error',\n              offset: 320\n            });\n          }\n        });\n      }).catch(() => { });\n    },\n\n    // 审核不通过\n    handleReject(index, row) {\n      this.$confirm('确认审核不通过该职位吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.listLoading = true;\n        let url = base + '/positions/update';\n        let para = {\n          pid: row.pid,\n          pflag2: \"审核不过\"\n        };\n        request.post(url, para).then((res) => {\n          this.listLoading = false;\n          if (res.code === 200) {\n            this.$message({\n              message: '已设置为审核不通过',\n              type: 'success',\n              offset: 320\n            });\n            this.getDatas();\n          } else {\n            this.$message({\n              message: res.msg || '操作失败',\n              type: 'error',\n              offset: 320\n            });\n          }\n        });\n      }).catch(() => { });\n    },\n  },\n};\n</script>\n<style scoped>\n/* 添加按钮样式 */\n.el-button+.el-button {\n  margin-left: 5px;\n}\n</style>\n"]}]}