{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue?vue&type=template&id=04724886&scoped=true", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue", "mtime": 1749118278931}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtF,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7F,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAErD,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC;;MAEJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/CompanyView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"company-detail\">\r\n    <!-- 企业基本信息 -->\r\n    <div class=\"company-card\">\r\n      <div class=\"company-header\">\r\n        <div class=\"company-logo\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.logo\" alt=\"企业logo\" />\r\n        </div>\r\n        <div class=\"company-qualification\" v-if=\"formData.qualification\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.qualification\" alt=\"资质图片\" />\r\n        </div>\r\n        <div class=\"company-info\">\r\n          <div class=\"name-action\">\r\n            <h1 class=\"company-name\">{{ formData.comname }}</h1>\r\n          \r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-location\"></i>\r\n            <span>地址：{{ formData.address }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-phone\"></i>\r\n            <span>联系方式：{{ formData.contact }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n            <span>企业规模：{{ formData.scale }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-collection\"></i>\r\n            <span>企业性质：{{ formData.nature }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-time\"></i>\r\n            <span>添加时间：{{ formData.addtime }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"company-description\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          企业简介\r\n        </h2>\r\n        <div class=\"description-content\" v-html=\"formData.introduction\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 企业招聘职位列表 -->\r\n    <div class=\"positions-card\">\r\n      <h2 class=\"section-title\">\r\n        <i class=\"el-icon-suitcase\"></i>\r\n        招聘职位\r\n      </h2>\r\n\r\n      <div v-if=\"positionsList.length > 0\" class=\"positions-list\">\r\n        <div v-for=\"position in positionsList\" :key=\"position.pid\" class=\"position-item\">\r\n          <div class=\"position-header\">\r\n            <h3 class=\"position-title\">{{ position.pname }}</h3>\r\n            <span class=\"salary\">{{ position.streatment }}</span>\r\n          </div>\r\n          <div class=\"position-info\">\r\n            <span><i class=\"el-icon-location\"></i>{{ position.wlocation }}</span>\r\n            <span><i class=\"el-icon-user\"></i>招聘人数：{{ position.rnumber }}人</span>\r\n            <span><i class=\"el-icon-time\"></i>发布时间：{{ position.ptime }}</span>\r\n          </div>\r\n          <div class=\"position-footer\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"viewPosition(position.pid)\"\r\n              :disabled=\"position.pflag !== '开放'\">\r\n              查看详情\r\n            </el-button>\r\n            <el-tag :type=\"position.pflag === '开放' ? 'success' : 'info'\" size=\"small\">\r\n              {{ position.pflag }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <el-empty v-else description=\"暂无招聘职位\"></el-empty>\r\n    </div>\r\n\r\n    <!-- 发送私信对话框 -->\r\n    <el-dialog title=\"发送私信\" v-model=\"dialogVisible\" width=\"500px\">\r\n      <el-form :model=\"messageForm\" ref=\"messageFormRef\" :rules=\"rules\">\r\n        <el-form-item prop=\"content\">\r\n          <el-input type=\"textarea\" v-model=\"messageForm.content\" :rows=\"4\" placeholder=\"请输入私信内容\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"sendMessage\" :loading=\"sending\">发 送</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    name: 'CompanyView',\r\n    data() {\r\n      return {\r\n        id: '',\r\n        formData: {},\r\n        positionsList: [],\r\n        dialogVisible: false,\r\n        sending: false,\r\n        messageForm: {\r\n          content: '',\r\n          clname: '', // 企业账号\r\n          sno: '', // 求职者账号\r\n        },\r\n        rules: {\r\n          content: [\r\n            { required: true, message: '请输入私信内容', trigger: 'blur' },\r\n            { min: 1, max: 500, message: '长度在1到500个字符', trigger: 'blur' }\r\n          ]\r\n        }\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id;\r\n      this.getCompanyData();\r\n      this.getPositionsList();\r\n    },\r\n    methods: {\r\n      // 获取企业信息\r\n      async getCompanyData() {\r\n        try {\r\n          const res = await request.post(base + '/company/get?id=' + this.id);\r\n          if (res.code === 200) {\r\n            this.formData = res.resdata;\r\n          }\r\n        } catch (error) {\r\n          console.error('获取企业信息失败:', error);\r\n          this.$message({\r\n            message: '获取企业信息失败',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      },\r\n\r\n      // 获取企业发布的职位列表\r\n      async getPositionsList() {\r\n        try {\r\n          const res = await request.post(\r\n            base + '/positions/list',\r\n            { cid: this.id, pflag: '开放', pflag2: '审核通过' },\r\n            { params: { currentPage: 1, pageSize: 100 } }\r\n          );\r\n          if (res.code === 200) {\r\n            this.positionsList = res.resdata;\r\n          }\r\n        } catch (error) {\r\n          console.error('获取职位列表失败:', error);\r\n          this.$message({\r\n            message: '获取职位列表失败',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      },\r\n\r\n      // 查看职位详情\r\n      viewPosition(pid) {\r\n        this.$router.push({\r\n          path: '/positionsView',\r\n          query: { id: pid },\r\n        });\r\n      },\r\n\r\n      // 处理发送私信按钮点击\r\n      handleSendMessage() {\r\n        // 检查是否登录\r\n        const sno = sessionStorage.getItem('lname');\r\n        if (!sno) {\r\n          this.$message({\r\n            message: '请先登录后再发送私信',\r\n            type: 'warning',\r\n            offset: 320\r\n          });\r\n          this.$router.push('/Slogin');\r\n          return;\r\n        }\r\n\r\n        this.messageForm.sno = sno;\r\n        this.messageForm.clname = this.formData.clname;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n   \r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .company-detail {\r\n    max-width: 1200px;\r\n    margin: 20px auto;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .company-card,\r\n  .positions-card {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    padding: 30px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .company-header {\r\n    display: flex;\r\n    gap: 30px;\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .company-logo {\r\n    width: 120px;\r\n    height: 120px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .company-logo img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .company-qualification {\r\n    width: 120px;\r\n    height: 120px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .company-qualification img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .company-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .name-action {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .company-name {\r\n    margin: 0;\r\n  }\r\n\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    margin-bottom: 10px;\r\n    color: #666;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 18px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    border-bottom: 2px solid #409eff;\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .description-content {\r\n    color: #666;\r\n    line-height: 1.8;\r\n  }\r\n\r\n  .positions-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n    gap: 20px;\r\n  }\r\n\r\n  .position-item {\r\n    border: 1px solid #eee;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .position-item:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .position-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .position-title {\r\n    font-size: 16px;\r\n    color: #333;\r\n    margin: 0;\r\n  }\r\n\r\n  .salary {\r\n    color: #ff4d4f;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .position-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin-bottom: 15px;\r\n    color: #666;\r\n  }\r\n\r\n  .position-info span {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 5px;\r\n  }\r\n\r\n  .position-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: right;\r\n    display: block;\r\n  }\r\n</style>"]}]}