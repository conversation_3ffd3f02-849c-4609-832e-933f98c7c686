{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Ai.vue?vue&type=template&id=ba236c06&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Ai.vue", "mtime": 1741618492749}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Ai.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACV,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAClB,CAAC,CAAC,CAAC;gCACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC1B,CAAC,CAAC,CAAC,CAAC;4BACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtB,CAAC,CAAC,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/D,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChB,CAAC,CAAC,CAAC,CAAC,CAAC;oBACT,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Ai.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div class=\"ai-advisor\">\r\n        <!-- 聊天容器 -->\r\n        <div class=\"chat-container\">\r\n            <!-- 聊天头部 -->\r\n            <div class=\"chat-header\">\r\n                <div class=\"advisor-info\">\r\n                    <i class=\"fas fa-robot advisor-avatar\"></i>\r\n                    <div class=\"advisor-details\">\r\n                        <h2>AI求职顾问</h2>\r\n                        <span class=\"status online\">\r\n                            <i class=\"fas fa-circle\"></i> 在线\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 聊天消息区域 -->\r\n            <div class=\"chat-messages\" ref=\"messageContainer\">\r\n                <!-- 欢迎消息 -->\r\n                <div class=\"message-item ai-message\">\r\n                    <div class=\"message-avatar\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\">\r\n                            <p>👋 你好！我是你的AI求职顾问。</p>\r\n                            <p>我可以为你提供以下帮助：</p>\r\n                            <ul>\r\n                                <li>📝 简历优化和求职信写作指导</li>\r\n                                <li>🎯 职业规划和发展建议</li>\r\n                                <li>🤝 面试技巧和模拟面试</li>\r\n                                <li>💼 行业动态和求职策略</li>\r\n                                <li>❓ 解答求职过程中的各类问题</li>\r\n                            </ul>\r\n                            <p>请告诉我你需要什么帮助？</p>\r\n                        </div>\r\n                        <span class=\"message-time\">{{ formatTime(new Date()) }}</span>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 动态消息列表 -->\r\n                <div v-for=\"(msg, index) in messages\" :key=\"index\"\r\n                    :class=\"['message-item', msg.type === 'user' ? 'user-message' : 'ai-message']\">\r\n                    <div class=\"message-avatar\">\r\n                        <i :class=\"msg.type === 'user' ? 'fas fa-user' : 'fas fa-robot'\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\" v-html=\"formatMessage(msg.content)\"></div>\r\n                        <span class=\"message-time\">{{ formatTime(msg.time) }}</span>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 加载动画 -->\r\n                <div class=\"message-item ai-message\" v-if=\"isLoading\">\r\n                    <div class=\"message-avatar\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"typing-indicator\">\r\n                            <span></span>\r\n                            <span></span>\r\n                            <span></span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 输入区域 -->\r\n            <div class=\"chat-input\">\r\n                <div class=\"input-wrapper\">\r\n                    <el-input v-model=\"userInput\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入你的问题...\"\r\n                        :disabled=\"isLoading\" @keyup.enter.native.exact=\"handleSend\"\r\n                        @keyup.ctrl.enter.native=\"handleSend\">\r\n                    </el-input>\r\n                    <el-button type=\"primary\" :loading=\"isLoading\" :disabled=\"!userInput.trim() || isLoading\"\r\n                        @click=\"handleSend\">\r\n                        <i class=\"fas fa-paper-plane\"></i>\r\n                        发送\r\n                    </el-button>\r\n                </div>\r\n                <!-- 快捷问题建议 -->\r\n                <div class=\"quick-questions\" v-if=\"messages.length <= 1\">\r\n                    <el-tag v-for=\"(question, index) in suggestedQuestions\" :key=\"index\" @click=\"quickAsk(question)\"\r\n                        :disabled=\"isLoading\" effect=\"light\" class=\"question-tag\">\r\n                        <i class=\"fas fa-question-circle\"></i>\r\n                        {{ question }}\r\n                    </el-tag>\r\n                </div>\r\n                <div class=\"input-tips\">\r\n                    <span>按Enter换行，Ctrl + Enter发送</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import { chatWithAI } from \"../../../utils/http\";\r\n\r\n\r\n    export default {\r\n        name: \"Ai\",\r\n        data() {\r\n            return {\r\n                messages: [],\r\n                userInput: \"\",\r\n                isLoading: false,\r\n                chatHistory: [],\r\n                suggestedQuestions: [\r\n                    \"如何写一份优秀的简历？\",\r\n                    \"面试常见问题及回答技巧\",\r\n                    \"职业规划该怎么做？\",\r\n                    \"如何准备一份好的求职信？\",\r\n                    \"面试着装和礼仪要求\",\r\n                    \"如何谈薪资待遇？\"\r\n                ]\r\n            };\r\n        },\r\n        mounted() {\r\n            // 检查登录状态\r\n            const lname = sessionStorage.getItem(\"lname\");\r\n            if (!lname) {\r\n                this.$message({\r\n                    message: \"请先登录\",\r\n                    type: \"warning\",\r\n                    offset: 320,\r\n                });\r\n                this.$router.push(\"/slogin\");\r\n                return;\r\n            }\r\n        },\r\n        methods: {\r\n            async handleSend() {\r\n                if (!this.userInput.trim() || this.isLoading) return;\r\n\r\n                // 添加用户消息\r\n                const userMessage = this.userInput.trim();\r\n                this.addMessage(userMessage, 'user');\r\n                this.userInput = '';\r\n                this.isLoading = true;\r\n\r\n                try {\r\n                    // 准备对话历史\r\n                    this.chatHistory.push({ role: \"user\", content: userMessage });\r\n\r\n                    // 调用AI接口\r\n                    const response = await chatWithAI(this.chatHistory);\r\n\r\n                    // 保存AI回复到历史记录\r\n                    this.chatHistory.push({ role: \"assistant\", content: response });\r\n\r\n                    // 显示AI回复\r\n                    this.addMessage(response, 'ai');\r\n                } catch (error) {\r\n                    console.error('AI回复出错:', error);\r\n                    this.addMessage('抱歉，我遇到了一些问题，请稍后再试。', 'ai');\r\n                } finally {\r\n                    this.isLoading = false;\r\n                    this.scrollToBottom();\r\n                }\r\n            },\r\n\r\n            addMessage(content, type) {\r\n                this.messages.push({\r\n                    content,\r\n                    type,\r\n                    time: new Date()\r\n                });\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n            },\r\n\r\n            formatTime(date) {\r\n                return new Date(date).toLocaleTimeString('zh-CN', {\r\n                    hour: '2-digit',\r\n                    minute: '2-digit'\r\n                });\r\n            },\r\n\r\n            scrollToBottom() {\r\n                this.$nextTick(() => {\r\n                    const container = this.$refs.messageContainer;\r\n                    container.scrollTop = container.scrollHeight;\r\n                });\r\n            },\r\n\r\n            formatMessage(message) {\r\n                // 将换行符转换为<br>标签\r\n                return message.replace(/\\n/g, '<br>');\r\n            },\r\n\r\n            quickAsk(question) {\r\n                if (!this.isLoading) {\r\n                    this.userInput = question;\r\n                    this.handleSend();\r\n                }\r\n            }\r\n        }\r\n    };\r\n</script>\r\n\r\n<style scoped>\r\n    .ai-advisor {\r\n        padding: 20px;\r\n        height: calc(100vh - 180px);\r\n        display: flex;\r\n        justify-content: center;\r\n        background-color: #f5f7fa;\r\n    }\r\n\r\n    .chat-container {\r\n        width: 100%;\r\n        max-width: 900px;\r\n        background: #fff;\r\n        border-radius: 16px;\r\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: hidden;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 20px;\r\n        background: linear-gradient(135deg, #3498db, #2c3e50);\r\n        color: white;\r\n    }\r\n\r\n    .advisor-info {\r\n        display: flex;\r\n        align-items: center;\r\n    }\r\n\r\n    .advisor-avatar {\r\n        font-size: 32px;\r\n        margin-right: 15px;\r\n        color: #fff;\r\n    }\r\n\r\n    .advisor-details h2 {\r\n        margin: 0;\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n    }\r\n\r\n    .status {\r\n        font-size: 14px;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 5px;\r\n    }\r\n\r\n    .status.online i {\r\n        color: #2ecc71;\r\n        font-size: 10px;\r\n    }\r\n\r\n    .chat-messages {\r\n        flex: 1;\r\n        padding: 20px;\r\n        overflow-y: auto;\r\n        background: #f8f9fa;\r\n    }\r\n\r\n    .message-item {\r\n        display: flex;\r\n        margin-bottom: 20px;\r\n        align-items: flex-start;\r\n        animation: fadeIn 0.3s ease;\r\n    }\r\n\r\n    .message-avatar {\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        background: #e9ecef;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-right: 12px;\r\n        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    .ai-message .message-avatar {\r\n        background: linear-gradient(135deg, #3498db, #2980b9);\r\n        color: white;\r\n    }\r\n\r\n    .user-message {\r\n        flex-direction: row-reverse;\r\n    }\r\n\r\n    .user-message .message-avatar {\r\n        margin-right: 0;\r\n        margin-left: 12px;\r\n        background: linear-gradient(135deg, #2ecc71, #27ae60);\r\n        color: white;\r\n    }\r\n\r\n    .message-content {\r\n        max-width: 70%;\r\n    }\r\n\r\n    .user-message .message-content {\r\n        align-items: flex-end;\r\n    }\r\n\r\n    .message-text {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        background: white;\r\n        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\r\n        font-size: 15px;\r\n        line-height: 1.6;\r\n        white-space: pre-wrap;\r\n    }\r\n\r\n    .message-text :deep(br) {\r\n        margin-bottom: 8px;\r\n    }\r\n\r\n    .user-message .message-text {\r\n        background: #3498db;\r\n        color: white;\r\n    }\r\n\r\n    .message-time {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-top: 5px;\r\n        display: block;\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 20px;\r\n        background: white;\r\n        border-top: 1px solid #eaeaea;\r\n    }\r\n\r\n    .input-wrapper {\r\n        display: flex;\r\n        gap: 10px;\r\n    }\r\n\r\n    .input-wrapper .el-button {\r\n        height: auto;\r\n        padding: 12px 24px;\r\n    }\r\n\r\n    .input-wrapper .el-button i {\r\n        margin-right: 8px;\r\n    }\r\n\r\n    .quick-questions {\r\n        margin-top: 15px;\r\n        display: flex;\r\n        gap: 10px;\r\n        flex-wrap: wrap;\r\n    }\r\n\r\n    .question-tag {\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        padding: 8px 12px;\r\n    }\r\n\r\n    .question-tag i {\r\n        margin-right: 6px;\r\n    }\r\n\r\n    .question-tag:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    .input-tips {\r\n        margin-top: 10px;\r\n        font-size: 12px;\r\n        color: #666;\r\n        display: flex;\r\n        justify-content: space-between;\r\n    }\r\n\r\n    /* 修改输入提示文本 */\r\n    .input-tips span:last-child {\r\n        display: none;\r\n    }\r\n\r\n    /* 打字动画 */\r\n    .typing-indicator {\r\n        display: flex;\r\n        padding: 12px 16px;\r\n        background: white;\r\n        border-radius: 12px;\r\n        gap: 4px;\r\n    }\r\n\r\n    .typing-indicator span {\r\n        width: 8px;\r\n        height: 8px;\r\n        background: #3498db;\r\n        border-radius: 50%;\r\n        animation: typing 1s infinite ease-in-out;\r\n    }\r\n\r\n    .typing-indicator span:nth-child(1) {\r\n        animation-delay: 0.2s;\r\n    }\r\n\r\n    .typing-indicator span:nth-child(2) {\r\n        animation-delay: 0.3s;\r\n    }\r\n\r\n    .typing-indicator span:nth-child(3) {\r\n        animation-delay: 0.4s;\r\n    }\r\n\r\n    @keyframes typing {\r\n\r\n        0%,\r\n        100% {\r\n            transform: translateY(0);\r\n        }\r\n\r\n        50% {\r\n            transform: translateY(-10px);\r\n        }\r\n    }\r\n\r\n    @keyframes fadeIn {\r\n        from {\r\n            opacity: 0;\r\n            transform: translateY(10px);\r\n        }\r\n\r\n        to {\r\n            opacity: 1;\r\n            transform: translateY(0);\r\n        }\r\n    }\r\n\r\n    /* 消息内容样式 */\r\n    .message-text :deep(p) {\r\n        margin: 0 0 10px;\r\n    }\r\n\r\n    .message-text :deep(ul) {\r\n        margin: 10px 0;\r\n        padding-left: 20px;\r\n    }\r\n\r\n    .message-text :deep(li) {\r\n        margin: 5px 0;\r\n    }\r\n\r\n    .message-text :deep(code) {\r\n        background: rgba(0, 0, 0, 0.05);\r\n        padding: 2px 4px;\r\n        border-radius: 4px;\r\n        font-family: monospace;\r\n    }\r\n\r\n    .user-message .message-text :deep(code) {\r\n        background: rgba(255, 255, 255, 0.2);\r\n    }\r\n\r\n    /* 滚动条样式 */\r\n    .chat-messages::-webkit-scrollbar {\r\n        width: 6px;\r\n    }\r\n\r\n    .chat-messages::-webkit-scrollbar-track {\r\n        background: #f1f1f1;\r\n        border-radius: 3px;\r\n    }\r\n\r\n    .chat-messages::-webkit-scrollbar-thumb {\r\n        background: #888;\r\n        border-radius: 3px;\r\n    }\r\n\r\n    .chat-messages::-webkit-scrollbar-thumb:hover {\r\n        background: #555;\r\n    }\r\n\r\n    /* 响应式设计 */\r\n    @media (max-width: 768px) {\r\n        .ai-advisor {\r\n            padding: 10px;\r\n            height: calc(100vh - 140px);\r\n        }\r\n\r\n        .message-content {\r\n            max-width: 85%;\r\n        }\r\n\r\n        .quick-questions {\r\n            display: grid;\r\n            grid-template-columns: repeat(2, 1fr);\r\n            gap: 8px;\r\n        }\r\n    }\r\n</style>"]}]}