{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyInfo.vue", "mtime": 1741602812000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "comname", "required", "message", "trigger", "scale", "nature", "contact", "address", "logo", "created", "user", "JSON", "parse", "sessionStorage", "getItem", "cid", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "stringify", "resdata", "$refs", "editor", "txt", "html", "introduction", "save", "validate", "valid", "code", "$message", "type", "offset", "msg", "showUpload", "hideUpload", "handleRemove", "file", "fileList", "handlePreview", "console", "log", "handleExceed", "files", "duration", "fileListChange", "extendFileName", "extendFileNames", "split", "regExpRules", "i", "length", "push", "RegExp", "fileNames", "that", "for<PERSON>ach", "key", "val", "ret", "test", "indexOf", "multiFiles", "handleConfirm", "filePath", "FormData", "append", "raw", "furl", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyInfo.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业中心</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">信息维护</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">信息维护</h4>\n      </div>\n    </div>\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n      <el-form-item label=\"企业账号\" prop=\"clname\">\n        <el-input\n          v-model=\"formData.clname\"\n          placeholder=\"企业账号\"\n          style=\"width: 50%\"\n          disabled\n        ></el-input>\n      </el-form-item>\n      <el-form-item label=\"企业名称\" prop=\"comname\">\n        <el-input v-model=\"formData.comname\" placeholder=\"企业名称\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"企业规模\" prop=\"scale\">\n        <el-input v-model=\"formData.scale\" placeholder=\"企业规模\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"企业性质\" prop=\"nature\">\n        <el-input v-model=\"formData.nature\" placeholder=\"企业性质\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"formData.contact\" placeholder=\"联系方式\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系地址\" prop=\"address\">\n        <el-input v-model=\"formData.address\" placeholder=\"联系地址\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item prop=\"logo\" label=\"企业logo\" min-width=\"20%\">\n        <el-input\n          v-model=\"formData.logo\"\n          placeholder=\"企业logo\"\n          readonly=\"true\"\n          style=\"width: 50%\"\n        ></el-input>\n        <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n      </el-form-item>\n      <el-form-item label=\"企业介绍\" prop=\"introduction\">\n        <WangEditor\n          ref=\"wangEditorRef\"\n          v-model=\"formData.introduction\"\n          :config=\"editorConfig\"\n          :isClear=\"isClear\"\n          @change=\"editorChange\"\n        ></WangEditor>\n      </el-form-item>\n      <el-form-item>\n        <el-button\n          type=\"primary\"\n          size=\"small\"\n          @click=\"save\"\n          :loading=\"btnLoading\"\n          icon=\"el-icon-upload\"\n          >提 交</el-button\n        >\n      </el-form-item>\n    </el-form>\n    <el-dialog\n      v-model=\"uploadVisible\"\n      title=\"附件上传\"\n      custom-class=\"el-dialog-widthSmall\"\n      @close=\"closeDialog\"\n    >\n      <div>\n        <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n      </div>\n      <el-upload\n        action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n        style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\"\n        drag\n        :limit=\"1\"\n        :on-preview=\"handlePreview\"\n        :on-remove=\"handleRemove\"\n        :file-list=\"fileList\"\n        :on-exceed=\"handleExceed\"\n        :auto-upload=\"false\"\n        name=\"file\"\n        :on-change=\"fileListChange\"\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\">\n          <div\n            style=\"display: inline; color: #d70000; font-size: 14px\"\n            class=\"uploadFileWarning\"\n            id=\"uploadFileWarning\"\n          ></div>\n        </div>\n      </el-upload>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"hideUpload\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport request, { base } from '../../../../utils/http';\nimport WangEditor from '../../../components/WangEditor';\nexport default {\n  name: 'CompanyInfo',\n  components: {\n    WangEditor,\n  },\n  data() {\n    return {\n      id: '',\n      isClear: false,\n      uploadVisible: false,\n      btnLoading: false, //保存按钮加载状态\n      formData: {}, //表单数据\n      addrules: {\n        comname: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],\n        scale: [{ required: true, message: '请输入企业规模', trigger: 'blur' }],\n        nature: [{ required: true, message: '请输入企业性质', trigger: 'blur' }],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],\n        address: [{ required: true, message: '请输入联系地址', trigger: 'blur' }],\n        logo: [{ required: true, message: '请输入企业logo', trigger: 'blur' }],\n      },\n    };\n  },\n  created() {\n    var user = JSON.parse(sessionStorage.getItem('user'));\n    this.id = user.cid;\n    this.getDatas();\n  },\n  methods: {\n    //获取列表数据\n    getDatas() {\n      let para = {};\n      this.listLoading = true;\n      let url = base + '/company/get?id=' + this.id;\n      request.post(url, para).then((res) => {\n        this.formData = JSON.parse(JSON.stringify(res.resdata));\n        this.listLoading = false;\n        this.$refs['wangEditorRef'].editor.txt.html(this.formData.introduction);\n      });\n    },\n\n    // 添加\n    save() {\n      this.$refs['formDataRef'].validate((valid) => {\n        //验证表单\n        if (valid) {\n          let url = base + '/company/update';\n          this.btnLoading = true;\n\n          request.post(url, this.formData).then((res) => {\n            //发送请求\n            if (res.code == 200) {\n              this.$message({\n                message: '操作成功',\n                type: 'success',\n                offset: 320,\n              });\n            } else {\n              this.$message({\n                message: res.msg,\n                type: 'error',\n                offset: 320,\n              });\n            }\n            this.btnLoading = false;\n          });\n        }\n      });\n    },\n\n    //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: '只能上传一个文件',\n        type: 'error',\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = 'png,jpg';\n      let extendFileNames = extendFileName.split(',');\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key['name']);\n        }\n        if (!ret) {\n          console.log(key['name'] + ':' + ret);\n          that.$message({\n            duration: 1000,\n            message: '上传的文件后缀必须为' + extendFileName + '格式！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key['name']) != -1) {\n          that.$message({\n            duration: 1000,\n            message: '上传的文件重复！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key['name']);\n        if (fileNames !== '') {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: '请选择文件！',\n          type: 'error',\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append('file', file.raw, file.raw.name);\n      });\n      let url = base + '/common/uploadFile';\n      console.log('url=' + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.logo = furl; // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n\n    // 富文本编辑器\n    editorChange(val) {\n      this.formData.introduction = val;\n    },\n  },\n};\n</script>\n<style scoped></style>\n"], "mappings": ";;;AA0GA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAOC,UAAS,MAAO,gCAAgC;AACvD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,OAAO,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAClEC,KAAK,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAChEE,MAAM,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACjEG,OAAO,EAAE,CAAC;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAClEI,OAAO,EAAE,CAAC;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAClEK,IAAI,EAAE,CAAC;UAAEP,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC;MAClE;IACF,CAAC;EACH,CAAC;EACDM,OAAOA,CAAA,EAAG;IACR,IAAIC,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;IACrD,IAAI,CAACpB,EAAC,GAAIgB,IAAI,CAACK,GAAG;IAClB,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE;IACP;IACAD,QAAQA,CAAA,EAAG;MACT,IAAIE,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAI/B,IAAG,GAAI,kBAAiB,GAAI,IAAI,CAACK,EAAE;MAC7CN,OAAO,CAACiC,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACzB,QAAO,GAAIa,IAAI,CAACC,KAAK,CAACD,IAAI,CAACa,SAAS,CAACD,GAAG,CAACE,OAAO,CAAC,CAAC;QACvD,IAAI,CAACN,WAAU,GAAI,KAAK;QACxB,IAAI,CAACO,KAAK,CAAC,eAAe,CAAC,CAACC,MAAM,CAACC,GAAG,CAACC,IAAI,CAAC,IAAI,CAAC/B,QAAQ,CAACgC,YAAY,CAAC;MACzE,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACL,KAAK,CAAC,aAAa,CAAC,CAACM,QAAQ,CAAEC,KAAK,IAAK;QAC5C;QACA,IAAIA,KAAK,EAAE;UACT,IAAIb,GAAE,GAAI/B,IAAG,GAAI,iBAAiB;UAClC,IAAI,CAACQ,UAAS,GAAI,IAAI;UAEtBT,OAAO,CAACiC,IAAI,CAACD,GAAG,EAAE,IAAI,CAACtB,QAAQ,CAAC,CAACwB,IAAI,CAAEC,GAAG,IAAK;YAC7C;YACA,IAAIA,GAAG,CAACW,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZjC,OAAO,EAAE,MAAM;gBACfkC,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACF,QAAQ,CAAC;gBACZjC,OAAO,EAAEqB,GAAG,CAACe,GAAG;gBAChBF,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAACxC,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACA0C,UAAUA,CAAA,EAAG;MACX,IAAI,CAAC3C,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACA4C,UAAUA,CAAA,EAAG;MACX,IAAI,CAAC5C,aAAY,GAAI,KAAK;IAC5B,CAAC;IACD;IACA6C,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAO,GAAIA,QAAQ;IAC1B,CAAC;IACDC,aAAaA,CAACF,IAAI,EAAE;MAClBG,OAAO,CAACC,GAAG,CAACJ,IAAI,CAAC;IACnB,CAAC;IACDK,YAAYA,CAACC,KAAK,EAAEL,QAAQ,EAAE;MAC5B,IAAI,CAACR,QAAQ,CAAC;QACZc,QAAQ,EAAE,IAAI;QACd/C,OAAO,EAAE,UAAU;QACnBkC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD;IACAa,cAAcA,CAACR,IAAI,EAAEC,QAAQ,EAAE;MAC7B,IAAIQ,cAAa,GAAI,SAAS;MAC9B,IAAIC,eAAc,GAAID,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC;MAC/C,IAAIC,WAAU,GAAI,EAAE;MACpB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,eAAe,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/CD,WAAW,CAACG,IAAI,CAAC,IAAIC,MAAM,CAAC,QAAO,GAAIN,eAAe,CAACG,CAAC,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC;MAC3E;MACA,IAAII,SAAQ,GAAI,EAAE;MAClB,IAAIX,KAAI,GAAI,EAAE;MACd,IAAIY,IAAG,GAAI,IAAI;MACfjB,QAAQ,CAACkB,OAAO,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnC,IAAIC,GAAE,GAAI,KAAK;QACf,KAAK,IAAIT,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3CS,GAAE,GAAIA,GAAE,IAAKV,WAAW,CAACC,CAAC,CAAC,CAACU,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C;QACA,IAAI,CAACE,GAAG,EAAE;UACRnB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAC,MAAM,IAAI,GAAE,GAAIE,GAAG,CAAC;UACpCJ,IAAI,CAACzB,QAAQ,CAAC;YACZc,QAAQ,EAAE,IAAI;YACd/C,OAAO,EAAE,YAAW,GAAIiD,cAAa,GAAI,KAAK;YAC9Cf,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA,IAAIsB,SAAS,CAACO,OAAO,CAACJ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;UACxCF,IAAI,CAACzB,QAAQ,CAAC;YACZc,QAAQ,EAAE,IAAI;YACd/C,OAAO,EAAE,UAAU;YACnBkC,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA;QACA,IAAI,CAACuB,IAAI,CAACO,UAAU,EAAE;UACpBnB,KAAI,GAAI,EAAE;UACVW,SAAQ,GAAI,EAAE;QAChB;QACAX,KAAK,CAACS,IAAI,CAACK,GAAG,CAAC;QACfH,SAAS,CAACF,IAAI,CAACK,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAIH,SAAQ,KAAM,EAAE,EAAE;UACpB;QAAA;QAEF;MACF,CAAC,CAAC;MACF,IAAI,CAACX,KAAI,GAAIW,SAAS;MACtB,IAAI,CAAChB,QAAO,GAAIK,KAAK;IACvB,CAAC;IACD;;;IAGAoB,aAAaA,CAAA,EAAG;MACd,IAAIC,QAAO,GAAI,IAAI,CAAC1B,QAAQ;MAC5B,IAAI0B,QAAQ,CAACb,MAAK,KAAM,CAAC,EAAE;QACzB,IAAI,CAACrB,QAAQ,CAAC;UACZc,QAAQ,EAAE,IAAI;UACd/C,OAAO,EAAE,QAAQ;UACjBkC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MACA,IAAIvC,QAAO,GAAI,IAAIwE,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC3B,QAAQ,CAACkB,OAAO,CAAEnB,IAAI,IAAK;QAC9B5C,QAAQ,CAACyE,MAAM,CAAC,MAAM,EAAE7B,IAAI,CAAC8B,GAAG,EAAE9B,IAAI,CAAC8B,GAAG,CAACjF,IAAI,CAAC;MAClD,CAAC,CAAC;MACF,IAAI6B,GAAE,GAAI/B,IAAG,GAAI,oBAAoB;MACrCwD,OAAO,CAACC,GAAG,CAAC,MAAK,GAAI1B,GAAG,CAAC;MACzBhC,OAAO,CAACiC,IAAI,CAACD,GAAG,EAAEtB,QAAQ,CAAC,CAACwB,IAAI,CAAEC,GAAG,IAAK;QACxCsB,OAAO,CAACC,GAAG,CAACvB,GAAG,CAAC;QAChB,IAAIkD,IAAG,GAAIlD,GAAG,CAACE,OAAO,CAAC4C,QAAQ;QAC/B,IAAI,CAACvE,QAAQ,CAACU,IAAG,GAAIiE,IAAI,EAAE;QAC3B,IAAI,CAACjC,UAAU,CAAC,CAAC;QACjBK,OAAO,CAACC,GAAG,CAACvB,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC;IAED;IACAmD,YAAYA,CAACX,GAAG,EAAE;MAChB,IAAI,CAACjE,QAAQ,CAACgC,YAAW,GAAIiC,GAAG;IAClC;EACF;AACF,CAAC", "ignoreList": []}]}