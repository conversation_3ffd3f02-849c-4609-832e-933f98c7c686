{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyList.vue?vue&type=template&id=06b4ad36", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyList.vue", "mtime": 1741615257159}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "_hoisted_1", "_createElementBlock", "_Fragment", "_renderList", "$data", "colist", "item", "key", "cid", "href", "src", "logo", "style", "_hoisted_4", "comname", "_hoisted_5", "_createVNode", "_component_el_pagination", "onCurrentChange", "$options", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"divlist\">\r\n    <ul>\r\n      <li class=\"widthk4\" v-for=\"item in colist\" :key=\"item.cid\">\r\n        <a :href=\"'companyView?id=' + item.cid\"><img :src=\"'http://localhost:8088/JobHuntingSystem/' + item.logo\"\r\n            style=\"width: 154px; height: 120px\" /></a>\r\n        <span class=\"wspan\"><a :href=\"'companyView?id=' + item.cid\">{{ item.comname }}</a></span>\r\n      </li>\r\n    </ul>\r\n  </div>\r\n  <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\n    background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\n    style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n  export default {\r\n    name: 'CompanyList',\r\n    data() {\r\n      return {\r\n        page: {\r\n          currentPage: 1, // 当前页\r\n          pageSize: 12, // 每页显示条目个数\r\n          totalCount: 0, // 总条目数\r\n        },\r\n        colist: '',\r\n      };\r\n    },\r\n    created() {\r\n      this.getDatas();\r\n    },\r\n    methods: {\r\n      // 分页\r\n      handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n      },\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n          cflag: \"审核通过\",\r\n        };\r\n        this.listLoading = true;\r\n        let url =\r\n          base +\r\n          '/company/list?currentPage=' +\r\n          this.page.currentPage +\r\n          '&pageSize=' +\r\n          this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n          if (res.resdata.length > 0) {\r\n            this.isPage = true;\r\n          } else {\r\n            this.isPage = false;\r\n          }\r\n          this.page.totalCount = res.count;\r\n          this.colist = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style></style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAS;;;;EAKRA,KAAK,EAAC;AAAO;;;;6DALzBC,mBAAA,CAQM,OARNC,UAQM,GAPJD,mBAAA,CAMK,c,kBALHE,mBAAA,CAIKC,SAAA,QAAAC,WAAA,CAJ8BC,KAAA,CAAAC,MAAM,EAAdC,IAAI;yBAA/BL,mBAAA,CAIK;MAJDH,KAAK,EAAC,SAAS;MAAyBS,GAAG,EAAED,IAAI,CAACE;QACpDT,mBAAA,CAC8C;MAD1CU,IAAI,sBAAsBH,IAAI,CAACE;QAAKT,mBAAA,CACE;MADIW,GAAG,8CAA8CJ,IAAI,CAACK,IAAI;MACpGC,KAAmC,EAAnC;QAAA;QAAA;MAAA;sEACJb,mBAAA,CAAyF,QAAzFc,UAAyF,GAArEd,mBAAA,CAA8D;MAA1DU,IAAI,sBAAsBH,IAAI,CAACE;wBAAQF,IAAI,CAACQ,OAAO,wBAAAC,UAAA,E;sCAIjFC,YAAA,CAE8DC,wBAAA;IAF9CC,eAAc,EAAEC,QAAA,CAAAC,mBAAmB;IAAG,cAAY,EAAEhB,KAAA,CAAAiB,IAAI,CAACC,WAAW;IAAG,WAAS,EAAElB,KAAA,CAAAiB,IAAI,CAACE,QAAQ;IAC7GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAEtB,KAAA,CAAAiB,IAAI,CAACM,UAAU;IAC5Ef,KAA2C,EAA3C;MAAA;MAAA;IAAA", "ignoreList": []}]}