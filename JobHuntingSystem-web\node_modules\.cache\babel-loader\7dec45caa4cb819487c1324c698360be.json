{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyManage2.vue?vue&type=template&id=cd5e8a08&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyManage2.vue", "mtime": 1741615257360}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_col", "span", "style", "_component_el_form", "inline", "model", "$data", "filters", "_component_el_form_item", "_component_el_input", "clname", "$event", "placeholder", "size", "comname", "contact", "_component_el_button", "type", "onClick", "$options", "query", "icon", "_cache", "_createBlock", "_component_el_table", "data", "datalist", "border", "stripe", "_component_el_table_column", "prop", "label", "align", "width", "default", "_withCtx", "scope", "_createElementVNode", "src", "row", "logo", "handleShow", "$index", "handleDelete", "handleApprove", "handleReject", "listLoading", "_component_el_pagination", "onCurrentChange", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyManage2.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">企业列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">企业列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.clname\" placeholder=\"企业账号\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-input v-model=\"filters.comname\" placeholder=\"企业名称\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-input v-model=\"filters.contact\" placeholder=\"联系方式\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"clname\" label=\"企业账号\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"password\" label=\"登录密码\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"comname\" label=\"企业名称\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"scale\" label=\"企业规模\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"nature\" label=\"企业性质\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"contact\" label=\"联系方式\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"logo\" label=\"企业logo\" width=\"70\" align=\"center\">\n        <template #default=\"scope\">\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + scope.row.logo\" style=\"width: 50px; height: 50px\" />\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"addtime\" label=\"注册时间\" align=\"center\"></el-table-column>\n      <el-table-column label=\"操作\" min-width=\"300\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\n\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\n          <el-button type=\"success\" size=\"mini\" @click=\"handleApprove(scope.$index, scope.row)\" icon=\"el-icon-check\"\n            style=\"padding: 3px 6px 3px 6px\">审核通过</el-button>\n          <el-button type=\"warning\" size=\"mini\" @click=\"handleReject(scope.$index, scope.row)\" icon=\"el-icon-close\"\n            style=\"padding: 3px 6px 3px 6px\">审核不过</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n  </div>\n</template>\n<script>\n  import request, { base } from '../../../../utils/http';\n  export default {\n    name: 'company',\n    components: {},\n    data() {\n      return {\n        filters: {\n          //列表查询参数\n          clname: '',\n          comname: '',\n          contact: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n    methods: {\n      // 删除企业\n      handleDelete(index, row) {\n        this.$confirm('确认删除该记录吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            this.listLoading = true;\n            let url = base + '/company/del?id=' + row.cid;\n            request.post(url).then((res) => {\n              this.listLoading = false;\n\n              this.$message({\n                message: '删除成功',\n                type: 'success',\n                offset: 320,\n              });\n              this.getDatas();\n            });\n          })\n          .catch(() => { });\n      },\n\n      // 分页\n      handleCurrentChange(val) {\n        this.page.currentPage = val;\n        this.getDatas();\n      },\n\n      //获取列表数据\n      getDatas() {\n        let para = {\n          clname: this.filters.clname,\n          comname: this.filters.comname,\n          contact: this.filters.contact,\n          cflag: \"未审核\",\n        };\n        this.listLoading = true;\n        let url =\n          base +\n          '/company/list?currentPage=' +\n          this.page.currentPage +\n          '&pageSize=' +\n          this.page.pageSize;\n        request.post(url, para).then((res) => {\n          if (res.resdata.length > 0) {\n            this.isPage = true;\n          } else {\n            this.isPage = false;\n          }\n          this.page.totalCount = res.count;\n          this.datalist = res.resdata;\n          this.listLoading = false;\n        });\n      },\n      //查询\n      query() {\n        this.getDatas();\n      },\n\n      // 查看\n      handleShow(index, row) {\n        this.$router.push({\n          path: '/CompanyDetail',\n          query: {\n            id: row.cid,\n          },\n        });\n      },\n\n      // 编辑\n      handleEdit(index, row) {\n        this.$router.push({\n          path: '/CompanyEdit',\n          query: {\n            id: row.cid,\n          },\n        });\n      },\n\n      // 审核通过\n      handleApprove(index, row) {\n        this.$confirm('确认审核通过该企业吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'success'\n        }).then(() => {\n          this.listLoading = true;\n          let url = base + '/company/update';\n          let para = {\n            cid: row.cid,\n            cflag: \"审核通过\"\n          };\n          request.post(url, para).then((res) => {\n            this.listLoading = false;\n            if (res.code === 200) {\n              this.$message({\n                message: '审核通过成功',\n                type: 'success',\n                offset: 320\n              });\n              this.getDatas();\n            } else {\n              this.$message({\n                message: res.msg || '操作失败',\n                type: 'error',\n                offset: 320\n              });\n            }\n          });\n        }).catch(() => { });\n      },\n\n      // 审核不通过\n      handleReject(index, row) {\n        this.$confirm('确认审核不通过该企业吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.listLoading = true;\n          let url = base + '/company/update';\n          let para = {\n            cid: row.cid,\n            cflag: \"审核不过\"\n          };\n          request.post(url, para).then((res) => {\n            this.listLoading = false;\n            if (res.code === 200) {\n              this.$message({\n                message: '已设置为审核不通过',\n                type: 'success',\n                offset: 320\n              });\n              this.getDatas();\n            } else {\n              this.$message({\n                message: res.msg || '操作失败',\n                type: 'error',\n                offset: 320\n              });\n            }\n          });\n        }).catch(() => { });\n      },\n    },\n  };\n</script>\n<style scoped>\n  /* 可以添加一些按钮样式 */\n  .el-button+.el-button {\n    margin-left: 5px;\n  }\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAK;;;;;;;;;;;;uBAAhBC,mBAAA,CA4DM,OA5DNC,UA4DM,G,seAhDJC,YAAA,CAeSC,iBAAA;IAfAC,IAAI,EAAE,EAAE;IAAEC,KAA8C,EAA9C;MAAA;MAAA;IAAA;;sBACjB,MAaU,CAbVH,YAAA,CAaUI,kBAAA;MAbAC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,KAAA,CAAAC;;wBAC9B,MAEe,CAFfR,YAAA,CAEeS,uBAAA;0BADb,MAA8E,CAA9ET,YAAA,CAA8EU,mBAAA;sBAA3DH,KAAA,CAAAC,OAAO,CAACG,MAAM;qEAAdJ,KAAA,CAAAC,OAAO,CAACG,MAAM,GAAAC,MAAA;UAAEC,WAAW,EAAC,MAAM;UAACC,IAAI,EAAC;;;UAE7Dd,YAAA,CAEeS,uBAAA;0BADb,MAA+E,CAA/ET,YAAA,CAA+EU,mBAAA;sBAA5DH,KAAA,CAAAC,OAAO,CAACO,OAAO;qEAAfR,KAAA,CAAAC,OAAO,CAACO,OAAO,GAAAH,MAAA;UAAEC,WAAW,EAAC,MAAM;UAACC,IAAI,EAAC;;;UAE9Dd,YAAA,CAEeS,uBAAA;0BADb,MAA+E,CAA/ET,YAAA,CAA+EU,mBAAA;sBAA5DH,KAAA,CAAAC,OAAO,CAACQ,OAAO;qEAAfT,KAAA,CAAAC,OAAO,CAACQ,OAAO,GAAAJ,MAAA;UAAEC,WAAW,EAAC,MAAM;UAACC,IAAI,EAAC;;;UAE9Dd,YAAA,CAEeS,uBAAA;0BADb,MAA0F,CAA1FT,YAAA,CAA0FiB,oBAAA;UAA/EC,IAAI,EAAC,SAAS;UAACJ,IAAI,EAAC,OAAO;UAAEK,OAAK,EAAEC,QAAA,CAAAC,KAAK;UAAEC,IAAI,EAAC;;4BAAiB,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;;qCAKpFC,YAAA,CA2BWC,mBAAA;IA3BAC,IAAI,EAAEnB,KAAA,CAAAoB,QAAQ;IAAEC,MAAM,EAAN,EAAM;IAACC,MAAM,EAAN,EAAM;IAAC1B,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAAyB,uBAAqB,EAArB,EAAqB;IACxG,YAAU,EAAC,KAAK;IAACW,IAAI,EAAC;;sBACtB,MAA6E,CAA7Ed,YAAA,CAA6E8B,0BAAA;MAA5DC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QAClDjC,YAAA,CAA+E8B,0BAAA;MAA9DC,IAAI,EAAC,UAAU;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACpDjC,YAAA,CAA8E8B,0BAAA;MAA7DC,IAAI,EAAC,SAAS;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACnDjC,YAAA,CAA4E8B,0BAAA;MAA3DC,IAAI,EAAC,OAAO;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACjDjC,YAAA,CAA6E8B,0BAAA;MAA5DC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QAClDjC,YAAA,CAA8E8B,0BAAA;MAA7DC,IAAI,EAAC,SAAS;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACnDjC,YAAA,CAIkB8B,0BAAA;MAJDC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC,QAAQ;MAACE,KAAK,EAAC,IAAI;MAACD,KAAK,EAAC;;MAChDE,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBC,mBAAA,CAA2G;QAArGC,GAAG,8CAA8CF,KAAK,CAACG,GAAG,CAACC,IAAI;QAAEtC,KAAiC,EAAjC;UAAA;UAAA;QAAA;;;QAG3EH,YAAA,CAA8E8B,0BAAA;MAA7DC,IAAI,EAAC,SAAS;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACnDjC,YAAA,CAYkB8B,0BAAA;MAZDE,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC,KAAK;MAACC,KAAK,EAAC;;MACrCE,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBrC,YAAA,CACiDiB,oBAAA;QADtCC,IAAI,EAAC,SAAS;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAEQ,QAAA,CAAAsB,UAAU,CAACL,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACG,GAAG;QAAGlB,IAAI,EAAC,iBAAiB;QACvGnB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAEoB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;wDAErCvB,YAAA,CACiDiB,oBAAA;QADtCC,IAAI,EAAC,QAAQ;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAEQ,QAAA,CAAAwB,YAAY,CAACP,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACG,GAAG;QAAGlB,IAAI,EAAC,gBAAgB;QACvGnB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAEoB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;wDACrCvB,YAAA,CACmDiB,oBAAA;QADxCC,IAAI,EAAC,SAAS;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAEQ,QAAA,CAAAyB,aAAa,CAACR,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACG,GAAG;QAAGlB,IAAI,EAAC,eAAe;QACxGnB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAIoB,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;wDACvCvB,YAAA,CACmDiB,oBAAA;QADxCC,IAAI,EAAC,SAAS;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAEQ,QAAA,CAAA0B,YAAY,CAACT,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACG,GAAG;QAAGlB,IAAI,EAAC,eAAe;QACvGnB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAIoB,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;;;;sDAxB2BhB,KAAA,CAAAwC,WAAW,E,GA4BnF/C,YAAA,CAE8DgD,wBAAA;IAF9CC,eAAc,EAAE7B,QAAA,CAAA8B,mBAAmB;IAAG,cAAY,EAAE3C,KAAA,CAAA4C,IAAI,CAACC,WAAW;IAAG,WAAS,EAAE7C,KAAA,CAAA4C,IAAI,CAACE,QAAQ;IAC7GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAEjD,KAAA,CAAA4C,IAAI,CAACM,UAAU;IAC5EtD,KAA2C,EAA3C;MAAA;MAAA;IAAA", "ignoreList": []}]}