{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyList.vue?vue&type=template&id=06b4ad36", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyList.vue", "mtime": 1741615257159}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImRpdmxpc3QiPg0KICAgIDx1bD4NCiAgICAgIDxsaSBjbGFzcz0id2lkdGhrNCIgdi1mb3I9Iml0ZW0gaW4gY29saXN0IiA6a2V5PSJpdGVtLmNpZCI+DQogICAgICAgIDxhIDpocmVmPSInY29tcGFueVZpZXc/aWQ9JyArIGl0ZW0uY2lkIj48aW1nIDpzcmM9IidodHRwOi8vbG9jYWxob3N0OjgwODgvSm9iSHVudGluZ1N5c3RlbS8nICsgaXRlbS5sb2dvIg0KICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxNTRweDsgaGVpZ2h0OiAxMjBweCIgLz48L2E+DQogICAgICAgIDxzcGFuIGNsYXNzPSJ3c3BhbiI+PGEgOmhyZWY9Iidjb21wYW55Vmlldz9pZD0nICsgaXRlbS5jaWQiPnt7IGl0ZW0uY29tbmFtZSB9fTwvYT48L3NwYW4+DQogICAgICA8L2xpPg0KICAgIDwvdWw+DQogIDwvZGl2Pg0KICA8ZWwtcGFnaW5hdGlvbiBAY3VycmVudC1jaGFuZ2U9ImhhbmRsZUN1cnJlbnRDaGFuZ2UiIDpjdXJyZW50LXBhZ2U9InBhZ2UuY3VycmVudFBhZ2UiIDpwYWdlLXNpemU9InBhZ2UucGFnZVNpemUiDQogICAgYmFja2dyb3VuZCBsYXlvdXQ9InRvdGFsLCBwcmV2LCBwYWdlciwgbmV4dCwganVtcGVyIiA6dG90YWw9InBhZ2UudG90YWxDb3VudCINCiAgICBzdHlsZT0iZmxvYXQ6IHJpZ2h0OyBtYXJnaW46IDEwcHggMjBweCAwIDAiPjwvZWwtcGFnaW5hdGlvbj4NCg=="}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyList.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1F,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/CompanyList.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"divlist\">\r\n    <ul>\r\n      <li class=\"widthk4\" v-for=\"item in colist\" :key=\"item.cid\">\r\n        <a :href=\"'companyView?id=' + item.cid\"><img :src=\"'http://localhost:8088/JobHuntingSystem/' + item.logo\"\r\n            style=\"width: 154px; height: 120px\" /></a>\r\n        <span class=\"wspan\"><a :href=\"'companyView?id=' + item.cid\">{{ item.comname }}</a></span>\r\n      </li>\r\n    </ul>\r\n  </div>\r\n  <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\n    background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\n    style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n  export default {\r\n    name: 'CompanyList',\r\n    data() {\r\n      return {\r\n        page: {\r\n          currentPage: 1, // 当前页\r\n          pageSize: 12, // 每页显示条目个数\r\n          totalCount: 0, // 总条目数\r\n        },\r\n        colist: '',\r\n      };\r\n    },\r\n    created() {\r\n      this.getDatas();\r\n    },\r\n    methods: {\r\n      // 分页\r\n      handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n      },\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n          cflag: \"审核通过\",\r\n        };\r\n        this.listLoading = true;\r\n        let url =\r\n          base +\r\n          '/company/list?currentPage=' +\r\n          this.page.currentPage +\r\n          '&pageSize=' +\r\n          this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n          if (res.resdata.length > 0) {\r\n            this.isPage = true;\r\n          } else {\r\n            this.isPage = false;\r\n          }\r\n          this.page.totalCount = res.count;\r\n          this.colist = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style></style>"]}]}