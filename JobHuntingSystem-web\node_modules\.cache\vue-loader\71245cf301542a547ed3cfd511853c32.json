{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue?vue&type=template&id=7a4880f2&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue", "mtime": 1741617073822}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpF,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;IAML,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Sweclome.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"welcome-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <div class=\"banner-content\">\r\n        <div class=\"user-avatar\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" alt=\"用户头像\" />\r\n        </div>\r\n        <div class=\"user-info\">\r\n          <h2 class=\"welcome-text\">欢迎回来，<span class=\"user-name\">{{ lname }}</span></h2>\r\n          <p class=\"login-time\" style=\"color: white;\">登录时间：{{ time }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n\r\n\r\n    <!-- 快捷操作 -->\r\n    <div class=\"quick-actions\">\r\n      <div class=\"section-title\">\r\n        <h3>快捷操作</h3>\r\n      </div>\r\n\r\n      <div class=\"row\">\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-plus-circle\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">创建简历</h4>\r\n            <p class=\"action-desc\">创建一份专业的简历，展示你的技能和经验</p>\r\n            <a href=\"/resume_Add\" class=\"action-link\">立即创建</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-search\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">搜索职位</h4>\r\n            <p class=\"action-desc\">浏览最新的职位信息，寻找适合你的工作机会</p>\r\n            <a href=\"/positionsList\" class=\"action-link\">开始搜索</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-robot\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">AI顾问</h4>\r\n            <p class=\"action-desc\">获取个性化的职业建议和简历优化指导</p>\r\n            <a href=\"/ai\" class=\"action-link\">咨询顾问</a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n  export default {\r\n    name: 'Sweclome',\r\n    data() {\r\n      return {\r\n        lname: '',\r\n        formData: {}, //表单数据\r\n        time: '', //当前时间\r\n        resumeCount: 0,\r\n        deliveryCount: 0,\r\n        browseCount: 0,\r\n        favoriteCount: 0\r\n      };\r\n    },\r\n    created() {\r\n      this.lname = sessionStorage.getItem('lname');\r\n      this.getDatas();\r\n      this.time = new Date().toLocaleString();\r\n    },\r\n    methods: {\r\n      //获取个人信息数据\r\n      getDatas() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + '/students/get?id=' + this.lname;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n  \r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 欢迎页面容器 */\r\n  .welcome-container {\r\n    padding: 20px;\r\n  }\r\n\r\n  /* 欢迎横幅 */\r\n  .welcome-banner {\r\n    background: linear-gradient(135deg, #3498db, #2c3e50);\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    margin-bottom: 30px;\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .banner-content {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .user-avatar {\r\n    margin-right: 30px;\r\n  }\r\n\r\n  .user-avatar img {\r\n    width: 100px;\r\n    height: 100px;\r\n    border-radius: 50%;\r\n    border: 4px solid rgba(255, 255, 255, 0.3);\r\n    object-fit: cover;\r\n  }\r\n\r\n  .user-info {\r\n    color: #fff;\r\n  }\r\n\r\n  .welcome-text {\r\n    font-size: 24px;\r\n    margin-bottom: 10px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .user-name {\r\n    color: #f1c40f;\r\n    font-weight: 700;\r\n  }\r\n\r\n  .login-time {\r\n    font-size: 14px;\r\n    opacity: 0.8;\r\n    margin: 0;\r\n  }\r\n\r\n  /* 统计卡片 */\r\n  .stats-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .stats-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .stats-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .stats-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 10px;\r\n    background-color: rgba(231, 76, 60, 0.1);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 15px;\r\n  }\r\n\r\n  .stats-icon i {\r\n    font-size: 24px;\r\n    color: #e74c3c;\r\n  }\r\n\r\n  .stats-icon.blue {\r\n    background-color: rgba(52, 152, 219, 0.1);\r\n  }\r\n\r\n  .stats-icon.blue i {\r\n    color: #3498db;\r\n  }\r\n\r\n  .stats-icon.green {\r\n    background-color: rgba(46, 204, 113, 0.1);\r\n  }\r\n\r\n  .stats-icon.green i {\r\n    color: #2ecc71;\r\n  }\r\n\r\n  .stats-icon.purple {\r\n    background-color: rgba(155, 89, 182, 0.1);\r\n  }\r\n\r\n  .stats-icon.purple i {\r\n    color: #9b59b6;\r\n  }\r\n\r\n  .stats-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .stats-title {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin: 0 0 5px;\r\n  }\r\n\r\n  .stats-value {\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    color: #2c3e50;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .stats-action a {\r\n    color: #3498db;\r\n    font-size: 13px;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .stats-action a:hover {\r\n    color: #2980b9;\r\n  }\r\n\r\n  .stats-action i {\r\n    font-size: 10px;\r\n    margin-left: 3px;\r\n  }\r\n\r\n  /* 个人信息卡片 */\r\n  .profile-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .section-title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .section-title h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0;\r\n    position: relative;\r\n    padding-left: 15px;\r\n  }\r\n\r\n  .section-title h3::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: 0;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 5px;\r\n    height: 20px;\r\n    background-color: #3498db;\r\n    border-radius: 2.5px;\r\n  }\r\n\r\n  .edit-link {\r\n    color: #3498db;\r\n    font-size: 14px;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .edit-link:hover {\r\n    color: #2980b9;\r\n  }\r\n\r\n  .profile-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 25px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  }\r\n\r\n  .info-group {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-label {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .info-value {\r\n    font-size: 16px;\r\n    color: #2c3e50;\r\n    font-weight: 500;\r\n  }\r\n\r\n  /* 快捷操作 */\r\n  .quick-actions {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .action-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 25px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n    height: 100%;\r\n  }\r\n\r\n  .action-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .action-icon {\r\n    width: 70px;\r\n    height: 70px;\r\n    border-radius: 50%;\r\n    background-color: rgba(52, 152, 219, 0.1);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin: 0 auto 20px;\r\n  }\r\n\r\n  .action-icon i {\r\n    font-size: 30px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .action-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .action-desc {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin-bottom: 20px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  .action-link {\r\n    display: inline-block;\r\n    padding: 8px 20px;\r\n    background-color: #3498db;\r\n    color: #fff;\r\n    border-radius: 5px;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .action-link:hover {\r\n    background-color: #2980b9;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n  @media (max-width: 768px) {\r\n    .banner-content {\r\n      flex-direction: column;\r\n      text-align: center;\r\n    }\r\n\r\n    .user-avatar {\r\n      margin-right: 0;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .welcome-text {\r\n      font-size: 20px;\r\n    }\r\n  }\r\n</style>"]}]}