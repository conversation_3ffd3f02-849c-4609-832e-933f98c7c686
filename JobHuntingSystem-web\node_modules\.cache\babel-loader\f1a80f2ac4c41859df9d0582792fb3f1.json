{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\bbs\\BbsDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\bbs\\BbsDetail.vue", "mtime": 1741614414000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0Jic0RldGFpbCcsCiAgY29tcG9uZW50czoge30sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlkOiAnJywKICAgICAgZm9ybURhdGE6IHt9IC8v6KGo5Y2V5pWw5o2uICAgICAgICAgCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsgLy/ojrflj5blj4LmlbAKICAgIHRoaXMuZ2V0RGF0YXMoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICBnZXREYXRhcygpIHsKICAgICAgbGV0IHBhcmEgPSB7fTsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9iYnMvZ2V0P2lkPSIgKyB0aGlzLmlkOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5mb3JtRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocmVzLnJlc2RhdGEpKTsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOi/lOWbngogICAgYmFjaygpIHsKICAgICAgLy/ov5Tlm57kuIrkuIDpobUKICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "formData", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "back", "$router", "go"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\bbs\\BbsDetail.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">帖子管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">帖子详情</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">帖子详情</h4>\r\n      </div>\r\n    </div>\r\n     <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n<el-form-item label=\"帖子ID\">\r\n{{formData.bid}}</el-form-item>\r\n<el-form-item label=\"标题\">\r\n{{formData.btitle}}</el-form-item>\r\n<el-form-item label=\"内容\" prop=\"bdetail\">\r\n<div v-html=\"formData.bdetail\"></div>\r\n</el-form-item>\r\n<el-form-item label=\"浏览量\">\r\n{{formData.btotal}}</el-form-item>\r\n<el-form-item label=\"发布人\">\r\n{{formData.sno}}</el-form-item>\r\n<el-form-item label=\"发布时间\">\r\n{{formData.addtime}}</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\n        \n        import request, { base } from \"../../../../utils/http\";\n        export default {\n            name: 'BbsDetail',\n            components: {\n            },\n            data() {\n                return {\n                    id: '',\n                    formData: {}, //表单数据         \n        \n                };\n            },\n            created() {\n                this.id = this.$route.query.id; //获取参数\n                this.getDatas();\n            },\n        \n        \n            methods: {\n        \n                //获取列表数据\n                getDatas() {\n                    let para = {\n                    };\n                    this.listLoading = true;\n                    let url = base + \"/bbs/get?id=\" + this.id;\n                    request.post(url, para).then((res) => {\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\n                        this.listLoading = false;\n                    });\n                },\n        \n                // 返回\n                back() {\n                    //返回上一页\n                    this.$router.go(-1);\n                },\n        \n            },\n        }\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": "AAuCQ,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACXC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE,CACZ,CAAC;EACDC,IAAIA,CAAA,EAAG;IACH,OAAO;MACHC,EAAE,EAAE,EAAE;MACNC,QAAQ,EAAE,CAAC,CAAC,CAAE;IAElB,CAAC;EACL,CAAC;EACDC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,EAAC,GAAI,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,EAAE,EAAE;IAChC,IAAI,CAACK,QAAQ,CAAC,CAAC;EACnB,CAAC;EAGDC,OAAO,EAAE;IAEL;IACAD,QAAQA,CAAA,EAAG;MACP,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIb,IAAG,GAAI,cAAa,GAAI,IAAI,CAACI,EAAE;MACzCL,OAAO,CAACe,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAI,CAACX,QAAO,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAC5B,CAAC,CAAC;IACN,CAAC;IAED;IACAS,IAAIA,CAAA,EAAG;MACH;MACA,IAAI,CAACC,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB;EAEJ;AACJ", "ignoreList": []}]}