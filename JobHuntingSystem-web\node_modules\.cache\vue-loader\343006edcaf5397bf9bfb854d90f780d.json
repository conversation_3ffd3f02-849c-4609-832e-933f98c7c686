{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue", "mtime": 1741617283358}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgZXhwb3J0IGRlZmF1bHQgewogICAgbmFtZTogJ01lbnUnLAogICAgZGF0YSgpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBjdXJyZW50UGF0aDogJycKICAgICAgfTsKICAgIH0sCiAgICBtb3VudGVkKCkgewogICAgICAvL+WIpOaWreaYr+WQpueZu+W9lQogICAgICB2YXIgbG5hbWUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdsbmFtZScpOwogICAgICBpZiAobG5hbWUgPT0gbnVsbCkgewogICAgICAgIC8v5by55Ye65o+Q56S6CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAn6K+35YWI55m75b2VJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgIG9mZnNldDogMzIwLAogICAgICAgIH0pOwogICAgICAgIC8v6Lez6L2s5Yiw55m75b2V6aG16Z2iCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9zbG9naW4nKTsKICAgICAgfQoKICAgICAgLy8g6I635Y+W5b2T5YmN6Lev5b6ECiAgICAgIHRoaXMuY3VycmVudFBhdGggPSB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWU7CiAgICB9LAogICAgbWV0aG9kczogewogICAgICBxdWl0KCkgewogICAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k6YCA5Ye65ZCXPycsICfmj5DnpLonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICB9KQogICAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCdsbmFtZScpOwogICAgICAgICAgICBfdGhpcy4kcm91dGVyLnB1c2goJy9zbG9naW4nKTsKICAgICAgICAgIH0pCiAgICAgICAgICAuY2F0Y2goKCkgPT4geyB9KTsKICAgICAgfSwKICAgICAgaXNBY3RpdmUocGF0aCkgewogICAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRQYXRoID09PSBwYXRoOwogICAgICB9CiAgICB9LAogIH07Cg=="}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Menu.vue"], "names": [], "mappings": ";EA0GE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACb,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;UACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;MACrB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/components/Menu.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div class=\"col-lg-3 sidebar sidebar--left\">\n  <div class=\"sidebar-container\">\n    <div class=\"sidebar-header\">\n      <h3 class=\"sidebar-title\">个人中心</h3>\n      <div class=\"sidebar-divider\"></div>\n    </div>\n\n    <div class=\"sidebar-menu\">\n      <div class=\"menu-group\">\n     \n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Sweclome') }\">\n            <a href=\"/Sweclome\">\n              <i class=\"fas fa-home\"></i>\n              <span>欢迎页面</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-file-alt\"></i>\n          <span>简历管理</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resume_Add') }\">\n            <a href=\"/resume_Add\">\n              <i class=\"fas fa-plus-circle\"></i>\n              <span>创建简历</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resume_manage') }\">\n            <a href=\"/resume_manage\">\n              <i class=\"fas fa-tasks\"></i>\n              <span>管理简历</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-history\"></i>\n          <span>记录查询</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/resumedelivery_manage') }\">\n            <a href=\"/resumedelivery_manage\">\n              <i class=\"fas fa-paper-plane\"></i>\n              <span>我的投递记录</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/browsingrecords_manage') }\">\n            <a href=\"/browsingrecords_manage\">\n              <i class=\"fas fa-eye\"></i>\n              <span>我的浏览记录</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n\n      <div class=\"menu-group\">  \n      \n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/bbs_manage') }\">\n            <a href=\"/bbs_manage\">\n              <i class=\"fas fa-comments\"></i>\n              <span>我的帖子</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n      <div class=\"menu-group\">\n        <div class=\"menu-group-title\">\n          <i class=\"fas fa-user-cog\"></i>\n          <span>账户设置</span>\n        </div>\n        <ul class=\"menu-list\">\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Sinfo') }\">\n            <a href=\"/Sinfo\">\n              <i class=\"fas fa-user-edit\"></i>\n              <span>修改个人信息</span>\n            </a>\n          </li>\n          <li class=\"menu-item\" :class=\"{ 'active': isActive('/Spassword') }\">\n            <a href=\"/Spassword\">\n              <i class=\"fas fa-lock\"></i>\n              <span>修改密码</span>\n            </a>\n          </li>\n          <li class=\"menu-item\">\n            <a @click=\"quit()\" style=\"cursor: pointer\">\n              <i class=\"fas fa-sign-out-alt\"></i>\n              <span>退出登录</span>\n            </a>\n          </li>\n        </ul>\n      </div>\n    </div>\n  </div>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: 'Menu',\n    data() {\n      return {\n        currentPath: ''\n      };\n    },\n    mounted() {\n      //判断是否登录\n      var lname = sessionStorage.getItem('lname');\n      if (lname == null) {\n        //弹出提示\n        this.$message({\n          message: '请先登录',\n          type: 'warning',\n          offset: 320,\n        });\n        //跳转到登录页面\n        this.$router.push('/slogin');\n      }\n\n      // 获取当前路径\n      this.currentPath = window.location.pathname;\n    },\n    methods: {\n      quit() {\n        var _this = this;\n        this.$confirm('确认退出吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            sessionStorage.removeItem('lname');\n            _this.$router.push('/slogin');\n          })\n          .catch(() => { });\n      },\n      isActive(path) {\n        return this.currentPath === path;\n      }\n    },\n  };\n</script>\n\n<style scoped>\n  .sidebar-container {\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n    overflow: hidden;\n  }\n\n  .sidebar-header {\n    padding: 20px;\n    background-color: #3498db;\n    color: #fff;\n  }\n\n  .sidebar-title {\n    margin: 0;\n    font-size: 20px;\n    font-weight: 600;\n  }\n\n  .sidebar-divider {\n    height: 3px;\n    width: 50px;\n    background-color: rgba(255, 255, 255, 0.5);\n    margin-top: 10px;\n    border-radius: 1.5px;\n  }\n\n  .sidebar-menu {\n    padding: 15px 0;\n  }\n\n  .menu-group {\n    margin-bottom: 15px;\n  }\n\n  .menu-group-title {\n    padding: 10px 20px;\n    color: #7f8c8d;\n    font-size: 14px;\n    font-weight: 600;\n    text-transform: uppercase;\n    display: flex;\n    align-items: center;\n  }\n\n  .menu-group-title i {\n    margin-right: 8px;\n    font-size: 16px;\n  }\n\n  .menu-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .menu-item {\n    position: relative;\n  }\n\n  .menu-item a {\n    display: flex;\n    align-items: center;\n    padding: 12px 20px;\n    color: #34495e;\n    transition: all 0.3s ease;\n    text-decoration: none;\n  }\n\n  .menu-item a i {\n    margin-right: 10px;\n    width: 20px;\n    text-align: center;\n    font-size: 16px;\n    color: #7f8c8d;\n    transition: all 0.3s ease;\n  }\n\n  .menu-item a:hover {\n    background-color: #f8f9fa;\n    color: #3498db;\n  }\n\n  .menu-item a:hover i {\n    color: #3498db;\n  }\n\n  .menu-item.active a {\n    background-color: #ebf5fb;\n    color: #3498db;\n    font-weight: 600;\n  }\n\n  .menu-item.active a i {\n    color: #3498db;\n  }\n\n  .menu-item.active::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 0;\n    height: 100%;\n    width: 4px;\n    background-color: #3498db;\n    border-radius: 0 2px 2px 0;\n  }\n\n  /* 响应式设计 */\n  @media (max-width: 992px) {\n    .sidebar-container {\n      margin-bottom: 30px;\n    }\n  }\n</style>"]}]}