{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyManage.vue?vue&type=template&id=04f15326", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyManage.vue", "mtime": 1741615257361}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_col", "span", "style", "_component_el_form", "inline", "model", "$data", "filters", "_component_el_form_item", "_component_el_input", "clname", "$event", "placeholder", "size", "comname", "contact", "_component_el_button", "type", "onClick", "$options", "query", "icon", "_cache", "_createBlock", "_component_el_table", "data", "datalist", "border", "stripe", "_component_el_table_column", "prop", "label", "align", "width", "default", "_withCtx", "scope", "_createElementVNode", "src", "row", "logo", "handleShow", "$index", "handleEdit", "handleDelete", "listLoading", "_component_el_pagination", "onCurrentChange", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyManage.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">企业列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">企业列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.clname\" placeholder=\"企业账号\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-input v-model=\"filters.comname\" placeholder=\"企业名称\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-input v-model=\"filters.contact\" placeholder=\"联系方式\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"clname\" label=\"企业账号\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"password\" label=\"登录密码\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"comname\" label=\"企业名称\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"scale\" label=\"企业规模\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"nature\" label=\"企业性质\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"contact\" label=\"联系方式\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"logo\" label=\"企业logo\" width=\"70\" align=\"center\">\n        <template #default=\"scope\">\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + scope.row.logo\" style=\"width: 50px; height: 50px\" />\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"addtime\" label=\"注册时间\" align=\"center\"></el-table-column>\n      <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\n          <el-button type=\"success\" size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\" icon=\"el-icon-edit\"\n            style=\"padding: 3px 6px 3px 6px\">编辑</el-button>\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n  </div>\n</template>\n<script>\n  import request, { base } from '../../../../utils/http';\n  export default {\n    name: 'company',\n    components: {},\n    data() {\n      return {\n        filters: {\n          //列表查询参数\n          clname: '',\n          comname: '',\n          contact: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n    methods: {\n      // 删除企业\n      handleDelete(index, row) {\n        this.$confirm('确认删除该记录吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            this.listLoading = true;\n            let url = base + '/company/del?id=' + row.cid;\n            request.post(url).then((res) => {\n              this.listLoading = false;\n\n              this.$message({\n                message: '删除成功',\n                type: 'success',\n                offset: 320,\n              });\n              this.getDatas();\n            });\n          })\n          .catch(() => { });\n      },\n\n      // 分页\n      handleCurrentChange(val) {\n        this.page.currentPage = val;\n        this.getDatas();\n      },\n\n      //获取列表数据\n      getDatas() {\n        let para = {\n          clname: this.filters.clname,\n          comname: this.filters.comname,\n          contact: this.filters.contact,\n          cflag: \"审核通过\",\n        };\n        this.listLoading = true;\n        let url =\n          base +\n          '/company/list?currentPage=' +\n          this.page.currentPage +\n          '&pageSize=' +\n          this.page.pageSize;\n        request.post(url, para).then((res) => {\n          if (res.resdata.length > 0) {\n            this.isPage = true;\n          } else {\n            this.isPage = false;\n          }\n          this.page.totalCount = res.count;\n          this.datalist = res.resdata;\n          this.listLoading = false;\n        });\n      },\n      //查询\n      query() {\n        this.getDatas();\n      },\n\n      // 查看\n      handleShow(index, row) {\n        this.$router.push({\n          path: '/CompanyDetail',\n          query: {\n            id: row.cid,\n          },\n        });\n      },\n\n      // 编辑\n      handleEdit(index, row) {\n        this.$router.push({\n          path: '/CompanyEdit',\n          query: {\n            id: row.cid,\n          },\n        });\n      },\n    },\n  };\n</script>\n<style scoped></style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAK;;;;;;;;;;;;uBAAhBC,mBAAA,CAyDM,OAzDNC,UAyDM,G,sWA7CJC,YAAA,CAeSC,iBAAA;IAfAC,IAAI,EAAE,EAAE;IAAEC,KAA8C,EAA9C;MAAA;MAAA;IAAA;;sBACjB,MAaU,CAbVH,YAAA,CAaUI,kBAAA;MAbAC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,KAAA,CAAAC;;wBAC9B,MAEe,CAFfR,YAAA,CAEeS,uBAAA;0BADb,MAA8E,CAA9ET,YAAA,CAA8EU,mBAAA;sBAA3DH,KAAA,CAAAC,OAAO,CAACG,MAAM;qEAAdJ,KAAA,CAAAC,OAAO,CAACG,MAAM,GAAAC,MAAA;UAAEC,WAAW,EAAC,MAAM;UAACC,IAAI,EAAC;;;UAE7Dd,YAAA,CAEeS,uBAAA;0BADb,MAA+E,CAA/ET,YAAA,CAA+EU,mBAAA;sBAA5DH,KAAA,CAAAC,OAAO,CAACO,OAAO;qEAAfR,KAAA,CAAAC,OAAO,CAACO,OAAO,GAAAH,MAAA;UAAEC,WAAW,EAAC,MAAM;UAACC,IAAI,EAAC;;;UAE9Dd,YAAA,CAEeS,uBAAA;0BADb,MAA+E,CAA/ET,YAAA,CAA+EU,mBAAA;sBAA5DH,KAAA,CAAAC,OAAO,CAACQ,OAAO;qEAAfT,KAAA,CAAAC,OAAO,CAACQ,OAAO,GAAAJ,MAAA;UAAEC,WAAW,EAAC,MAAM;UAACC,IAAI,EAAC;;;UAE9Dd,YAAA,CAEeS,uBAAA;0BADb,MAA0F,CAA1FT,YAAA,CAA0FiB,oBAAA;UAA/EC,IAAI,EAAC,SAAS;UAACJ,IAAI,EAAC,OAAO;UAAEK,OAAK,EAAEC,QAAA,CAAAC,KAAK;UAAEC,IAAI,EAAC;;4BAAiB,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;;qCAKpFC,YAAA,CAwBWC,mBAAA;IAxBAC,IAAI,EAAEnB,KAAA,CAAAoB,QAAQ;IAAEC,MAAM,EAAN,EAAM;IAACC,MAAM,EAAN,EAAM;IAAC1B,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAAyB,uBAAqB,EAArB,EAAqB;IACxG,YAAU,EAAC,KAAK;IAACW,IAAI,EAAC;;sBACtB,MAA6E,CAA7Ed,YAAA,CAA6E8B,0BAAA;MAA5DC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QAClDjC,YAAA,CAA+E8B,0BAAA;MAA9DC,IAAI,EAAC,UAAU;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACpDjC,YAAA,CAA8E8B,0BAAA;MAA7DC,IAAI,EAAC,SAAS;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACnDjC,YAAA,CAA4E8B,0BAAA;MAA3DC,IAAI,EAAC,OAAO;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACjDjC,YAAA,CAA6E8B,0BAAA;MAA5DC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QAClDjC,YAAA,CAA8E8B,0BAAA;MAA7DC,IAAI,EAAC,SAAS;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACnDjC,YAAA,CAIkB8B,0BAAA;MAJDC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC,QAAQ;MAACE,KAAK,EAAC,IAAI;MAACD,KAAK,EAAC;;MAChDE,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBC,mBAAA,CAA2G;QAArGC,GAAG,8CAA8CF,KAAK,CAACG,GAAG,CAACC,IAAI;QAAEtC,KAAiC,EAAjC;UAAA;UAAA;QAAA;;;QAG3EH,YAAA,CAA8E8B,0BAAA;MAA7DC,IAAI,EAAC,SAAS;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACnDjC,YAAA,CASkB8B,0BAAA;MATDE,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC,KAAK;MAACC,KAAK,EAAC;;MACrCE,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBrC,YAAA,CACiDiB,oBAAA;QADtCC,IAAI,EAAC,SAAS;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAEQ,QAAA,CAAAsB,UAAU,CAACL,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACG,GAAG;QAAGlB,IAAI,EAAC,iBAAiB;QACvGnB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAEoB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;wDACrCvB,YAAA,CACiDiB,oBAAA;QADtCC,IAAI,EAAC,SAAS;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAEQ,QAAA,CAAAwB,UAAU,CAACP,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACG,GAAG;QAAGlB,IAAI,EAAC,cAAc;QACpGnB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAEoB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;wDACrCvB,YAAA,CACiDiB,oBAAA;QADtCC,IAAI,EAAC,QAAQ;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAEQ,QAAA,CAAAyB,YAAY,CAACR,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACG,GAAG;QAAGlB,IAAI,EAAC,gBAAgB;QACvGnB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAEoB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;sDArB6BhB,KAAA,CAAAuC,WAAW,E,GAyBnF9C,YAAA,CAE8D+C,wBAAA;IAF9CC,eAAc,EAAE5B,QAAA,CAAA6B,mBAAmB;IAAG,cAAY,EAAE1C,KAAA,CAAA2C,IAAI,CAACC,WAAW;IAAG,WAAS,EAAE5C,KAAA,CAAA2C,IAAI,CAACE,QAAQ;IAC7GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAEhD,KAAA,CAAA2C,IAAI,CAACM,UAAU;IAC5ErD,KAA2C,EAA3C;MAAA;MAAA;IAAA", "ignoreList": []}]}