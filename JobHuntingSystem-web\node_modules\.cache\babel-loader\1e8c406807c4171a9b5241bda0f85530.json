{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryManage2.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryManage2.vue", "mtime": 1741615349397}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "filters", "sno", "page", "currentPage", "pageSize", "totalCount", "listLoading", "btnLoading", "datalist", "auditDialogVisible", "resumeDialogVisible", "auditForm", "delid", "auditstatus", "auditreply", "resumeData", "studentInfo", "professionalsList", "professionalName", "created", "getDatas", "methods", "getStatusType", "status", "types", "未审核", "通过", "不通过", "user", "JSON", "parse", "sessionStorage", "getItem", "para", "condition", "cid", "res", "post", "params", "resdata", "count", "error", "console", "query", "handleShow", "index", "row", "$router", "push", "path", "id", "handleAudit", "submitAudit", "$message", "message", "type", "offset", "code", "deliveryRes", "studentRes", "studentData", "sflag", "updateRes", "msg", "previewResume", "rid", "resumeRes", "getProfessionals", "handleCurrentChange", "val", "getProfessionalName", "proid", "length", "professional", "find", "p", "proname"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryManage2.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">简历投递管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">简历投递列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">简历投递列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.sno\" placeholder=\"账号\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"by1\" label=\"职位\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"sno\" label=\"账号\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"by2\" label=\"简历\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"submittime\" label=\"投递时间\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"auditstatus\" label=\"审核状态\" align=\"center\">\n        <template #default=\"scope\">\n          <el-tag :type=\"getStatusType(scope.row.auditstatus)\">\n            {{ scope.row.auditstatus }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"auditreply\" label=\"审核回复\" align=\"center\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.auditreply\">{{ scope.row.auditreply.substring(0, 20) }}</span>\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">\n            详情\n          </el-button>\n          <el-button v-if=\"scope.row.auditstatus === '待审核'\" type=\"success\" size=\"mini\"\n            @click=\"handleAudit(scope.$index, scope.row)\" icon=\"el-icon-check\" style=\"padding: 3px 6px 3px 6px\">\n            审核\n          </el-button>\n          <el-button type=\"info\" size=\"mini\" @click=\"previewResume(scope.row.rid)\" icon=\"el-icon-document\"\n            style=\"padding: 3px 6px 3px 6px\">\n            预览简历\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n    <!-- 审核对话框 -->\n    <el-dialog title=\"简历审核\" v-model=\"auditDialogVisible\" width=\"500px\">\n      <el-form :model=\"auditForm\" label-width=\"100px\">\n        <el-form-item label=\"审核结果\">\n          <el-radio-group v-model=\"auditForm.auditstatus\">\n            <el-radio label=\"同意\">同意</el-radio>\n            <el-radio label=\"拒绝\">拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"审核回复\">\n          <el-input type=\"textarea\" v-model=\"auditForm.auditreply\" :rows=\"4\" placeholder=\"请输入审核回复\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"auditDialogVisible = false\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"submitAudit\" :loading=\"btnLoading\">确 定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 简历预览对话框 -->\n    <el-dialog title=\"简历预览\" v-model=\"resumeDialogVisible\" width=\"800px\">\n      <div v-if=\"resumeData\" class=\"resume-preview\">\n        <div class=\"resume-header\">\n          <div class=\"header-content\">\n            <div class=\"avatar-section\">\n              <img :src=\"'http://localhost:8088/JobHuntingSystem/' + studentInfo.spic\" class=\"avatar\" />\n            </div>\n            <div class=\"basic-info\">\n              <h1 class=\"name\">{{ studentInfo.sname }}</h1>\n              <div class=\"info-grid\">\n                <div class=\"info-item\">\n                  <i class=\"el-icon-user\"></i>\n                  <span>性别：{{ studentInfo.gender }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-date\"></i>\n                  <span>年龄：{{ studentInfo.age }}岁</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-phone\"></i>\n                  <span>电话：{{ studentInfo.phone }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-school\"></i>\n                  <span>专业：{{ professionalName }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-s-flag\"></i>\n                  <span>就业状态：\n                    <span :class=\"{\n                      'status-employed': studentInfo.sflag === '已就业',\n                      'status-unemployed': studentInfo.sflag === '未就业',\n                    }\">\n                      {{ studentInfo.sflag }}\n                    </span>\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"resume-body\">\n          <div class=\"resume-section\">\n            <h2 class=\"section-title\">\n              <i class=\"el-icon-document\"></i>\n              简历信息\n            </h2>\n            <div class=\"resume-content\">\n              <div class=\"info-row\">\n                <label>教育背景：</label>\n                <div class=\"content\" v-html=\"resumeData.education\"></div>\n              </div>\n              <div class=\"info-row\">\n                <label>实习经历：</label>\n                <div class=\"content\" v-html=\"resumeData.parttimejob\"></div>\n              </div>\n              <div class=\"info-row\">\n                <label>个人介绍：</label>\n                <div class=\"content\" v-html=\"resumeData.introduction\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div v-else class=\"resume-empty\">\n        <el-empty description=\"暂无简历信息\"></el-empty>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import request, { base } from '../../../../utils/http';\n\n  export default {\n    name: 'resumedelivery',\n    data() {\n      return {\n        filters: {\n          sno: '',\n        },\n        page: {\n          currentPage: 1,\n          pageSize: 10,\n          totalCount: 0,\n        },\n        listLoading: false,\n        btnLoading: false,\n        datalist: [],\n        auditDialogVisible: false,\n        resumeDialogVisible: false,\n        auditForm: {\n          delid: '',\n          auditstatus: '',\n          auditreply: '',\n        },\n        resumeData: null,\n        studentInfo: {},\n        professionalsList: [],\n        professionalName: '',\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n    methods: {\n      // 获取状态标签类型\n      getStatusType(status) {\n        const types = {\n          未审核: 'warning',\n          通过: 'success',\n          不通过: 'danger',\n        };\n        return types[status] || 'info';\n      },\n\n      // 获取列表数据\n      async getDatas() {\n        var user = JSON.parse(sessionStorage.getItem('user'));\n        let para = {\n          sno: this.filters.sno,\n          condition: ' and a.pid in(select pid from positions where cid=' + user.cid + ')',\n        };\n        this.listLoading = true;\n        try {\n          const res = await request.post(base + '/resumedelivery/list', para, {\n            params: { currentPage: this.page.currentPage, pageSize: this.page.pageSize },\n          });\n          this.datalist = res.resdata;\n          this.page.totalCount = res.count;\n        } catch (error) {\n          console.error('获取数据失败:', error);\n        } finally {\n          this.listLoading = false;\n        }\n      },\n\n      // 查询\n      query() {\n        this.page.currentPage = 1;\n        this.getDatas();\n      },\n\n      // 查看详情\n      handleShow(index, row) {\n        this.$router.push({\n          path: '/resumedeliveryDetail',\n          query: {\n            id: row.delid,\n          },\n        });\n      },\n\n      // 打开审核对话框\n      handleAudit(index, row) {\n        this.auditForm = {\n          delid: row.delid,\n          auditstatus: '',\n          auditreply: '',\n        };\n        this.auditDialogVisible = true;\n      },\n\n      // 提交审核\n      async submitAudit() {\n        if (!this.auditForm.auditstatus) {\n          this.$message({\n            message: '请选择审核结果',\n            type: 'warning',\n            offset: 320,\n          });\n          return;\n        }\n\n        this.btnLoading = true;\n        try {\n          //  更新简历投递状态\n          const res = await request.post(base + '/resumedelivery/update', this.auditForm);\n          if (res.code === 200) {\n            // 如果审核通过，更新求职者就业状态\n            if (this.auditForm.auditstatus === '通过') {\n              // 先获取投递记录详情，获取求职者账号\n              const deliveryRes = await request.post(\n                base + '/resumedelivery/get?id=' + this.auditForm.delid\n              );\n              if (deliveryRes.code === 200) {\n                const sno = deliveryRes.resdata.sno;\n                // 获取求职者信息\n                const studentRes = await request.post(base + '/students/get?id=' + sno);\n                if (studentRes.code === 200) {\n                  const studentData = studentRes.resdata;\n                  // 更新求职者就业状态\n                  studentData.sflag = '已就业';\n                  const updateRes = await request.post(base + '/students/update', studentData);\n                  if (updateRes.code === 200) {\n                    this.$message({\n                      message: '审核成功',\n                      type: 'success',\n                      offset: 320,\n                    });\n                  } else {\n                    this.$message({\n                      message: '审核成功',\n                      type: 'warning',\n                      offset: 320,\n                    });\n                  }\n                }\n              }\n            } else {\n              this.$message({\n                message: '审核成功',\n                type: 'success',\n                offset: 320,\n              });\n            }\n            this.auditDialogVisible = false;\n            this.getDatas();\n          } else {\n            this.$message({\n              message: res.msg || '审核失败',\n              type: 'error',\n              offset: 320,\n            });\n          }\n        } catch (error) {\n          console.error('审核失败:', error);\n          this.$message({\n            message: '审核失败，请重试',\n            type: 'error',\n            offset: 320,\n          });\n        } finally {\n          this.btnLoading = false;\n        }\n      },\n\n      // 预览简历\n      async previewResume(rid) {\n        try {\n          // 获取简历信息\n          const resumeRes = await request.post(base + '/resume/get?id=' + rid);\n          if (resumeRes.code === 200) {\n            this.resumeData = resumeRes.resdata;\n            // 获取求职者信息\n            const studentRes = await request.post(base + '/students/get?id=' + this.resumeData.sno);\n            if (studentRes.code === 200) {\n              this.studentInfo = studentRes.resdata;\n              // 获取专业信息\n              await this.getProfessionals();\n            }\n            this.resumeDialogVisible = true;\n          }\n        } catch (error) {\n          console.error('获取简历信息失败:', error);\n          this.$message({\n            message: '获取简历信息失败',\n            type: 'error',\n            offset: 320,\n          });\n        }\n      },\n\n      // 分页\n      handleCurrentChange(val) {\n        this.page.currentPage = val;\n        this.getDatas();\n      },\n\n      // 添加获取专业列表方法\n      async getProfessionals() {\n        try {\n          const res = await request.post(\n            base + '/professionals/list',\n            {},\n            { params: { currentPage: 1, pageSize: 100 } }\n          );\n          if (res.code === 200) {\n            this.professionalsList = res.resdata;\n            this.getProfessionalName();\n          }\n        } catch (error) {\n          console.error('获取专业列表失败:', error);\n        }\n      },\n\n      // 添加获取专业名称方法\n      getProfessionalName() {\n        if (this.studentInfo.proid && this.professionalsList.length > 0) {\n          const professional = this.professionalsList.find((p) => p.proid === this.studentInfo.proid);\n          this.professionalName = professional ? professional.proname : '';\n        }\n      },\n    },\n  };\n</script>\n\n<style scoped>\n  .resume-preview {\n    max-width: 800px;\n    margin: 0 auto;\n  }\n\n  .resume-header {\n    background: linear-gradient(135deg, #3bc0c3 0%, #42d3d7 100%);\n    padding: 40px;\n    border-radius: 8px 8px 0 0;\n    color: #fff;\n  }\n\n  .header-content {\n    display: flex;\n    gap: 30px;\n  }\n\n  .avatar-section {\n    width: 120px;\n    height: 120px;\n  }\n\n  .avatar {\n    width: 100%;\n    height: 100%;\n    border-radius: 60px;\n    object-fit: cover;\n    border: 4px solid rgba(255, 255, 255, 0.3);\n  }\n\n  .basic-info {\n    flex: 1;\n  }\n\n  .name {\n    font-size: 24px;\n    margin-bottom: 20px;\n  }\n\n  .info-grid {\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: 15px;\n  }\n\n  .info-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n\n  .resume-body {\n    background: #fff;\n    padding: 30px;\n    border-radius: 0 0 8px 8px;\n  }\n\n  .section-title {\n    font-size: 18px;\n    color: #333;\n    margin-bottom: 20px;\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n\n  .info-row {\n    margin-bottom: 20px;\n  }\n\n  .info-row label {\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 8px;\n    display: block;\n  }\n\n  .content {\n    color: #666;\n    line-height: 1.8;\n  }\n\n  .status-employed {\n    color: #67c23a;\n    font-weight: bold;\n  }\n\n  .status-unemployed {\n    color: #f56c6c;\n    font-weight: bold;\n  }\n\n  .resume-empty {\n    padding: 40px 0;\n    text-align: center;\n  }\n</style>"], "mappings": ";;;AAgKE,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;QACPC,GAAG,EAAE;MACP,CAAC;MACDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QACdC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE;MACd,CAAC;MACDC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,EAAE;MACZC,kBAAkB,EAAE,KAAK;MACzBC,mBAAmB,EAAE,KAAK;MAC1BC,SAAS,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC,CAAC;MACfC,iBAAiB,EAAE,EAAE;MACrBC,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE;IACP;IACAC,aAAaA,CAACC,MAAM,EAAE;MACpB,MAAMC,KAAI,GAAI;QACZC,GAAG,EAAE,SAAS;QACdC,EAAE,EAAE,SAAS;QACbC,GAAG,EAAE;MACP,CAAC;MACD,OAAOH,KAAK,CAACD,MAAM,KAAK,MAAM;IAChC,CAAC;IAED;IACA,MAAMH,QAAQA,CAAA,EAAG;MACf,IAAIQ,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACrD,IAAIC,IAAG,GAAI;QACThC,GAAG,EAAE,IAAI,CAACD,OAAO,CAACC,GAAG;QACrBiC,SAAS,EAAE,oDAAmD,GAAIN,IAAI,CAACO,GAAE,GAAI;MAC/E,CAAC;MACD,IAAI,CAAC7B,WAAU,GAAI,IAAI;MACvB,IAAI;QACF,MAAM8B,GAAE,GAAI,MAAMxC,OAAO,CAACyC,IAAI,CAACxC,IAAG,GAAI,sBAAsB,EAAEoC,IAAI,EAAE;UAClEK,MAAM,EAAE;YAAEnC,WAAW,EAAE,IAAI,CAACD,IAAI,CAACC,WAAW;YAAEC,QAAQ,EAAE,IAAI,CAACF,IAAI,CAACE;UAAS;QAC7E,CAAC,CAAC;QACF,IAAI,CAACI,QAAO,GAAI4B,GAAG,CAACG,OAAO;QAC3B,IAAI,CAACrC,IAAI,CAACG,UAAS,GAAI+B,GAAG,CAACI,KAAK;MAClC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC,UAAU;QACR,IAAI,CAACnC,WAAU,GAAI,KAAK;MAC1B;IACF,CAAC;IAED;IACAqC,KAAKA,CAAA,EAAG;MACN,IAAI,CAACzC,IAAI,CAACC,WAAU,GAAI,CAAC;MACzB,IAAI,CAACiB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAwB,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,uBAAuB;QAC7BN,KAAK,EAAE;UACLO,EAAE,EAAEJ,GAAG,CAAClC;QACV;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAuC,WAAWA,CAACN,KAAK,EAAEC,GAAG,EAAE;MACtB,IAAI,CAACnC,SAAQ,GAAI;QACfC,KAAK,EAAEkC,GAAG,CAAClC,KAAK;QAChBC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE;MACd,CAAC;MACD,IAAI,CAACL,kBAAiB,GAAI,IAAI;IAChC,CAAC;IAED;IACA,MAAM2C,WAAWA,CAAA,EAAG;MAClB,IAAI,CAAC,IAAI,CAACzC,SAAS,CAACE,WAAW,EAAE;QAC/B,IAAI,CAACwC,QAAQ,CAAC;UACZC,OAAO,EAAE,SAAS;UAClBC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;QACF;MACF;MAEA,IAAI,CAACjD,UAAS,GAAI,IAAI;MACtB,IAAI;QACF;QACA,MAAM6B,GAAE,GAAI,MAAMxC,OAAO,CAACyC,IAAI,CAACxC,IAAG,GAAI,wBAAwB,EAAE,IAAI,CAACc,SAAS,CAAC;QAC/E,IAAIyB,GAAG,CAACqB,IAAG,KAAM,GAAG,EAAE;UACpB;UACA,IAAI,IAAI,CAAC9C,SAAS,CAACE,WAAU,KAAM,IAAI,EAAE;YACvC;YACA,MAAM6C,WAAU,GAAI,MAAM9D,OAAO,CAACyC,IAAI,CACpCxC,IAAG,GAAI,yBAAwB,GAAI,IAAI,CAACc,SAAS,CAACC,KACpD,CAAC;YACD,IAAI8C,WAAW,CAACD,IAAG,KAAM,GAAG,EAAE;cAC5B,MAAMxD,GAAE,GAAIyD,WAAW,CAACnB,OAAO,CAACtC,GAAG;cACnC;cACA,MAAM0D,UAAS,GAAI,MAAM/D,OAAO,CAACyC,IAAI,CAACxC,IAAG,GAAI,mBAAkB,GAAII,GAAG,CAAC;cACvE,IAAI0D,UAAU,CAACF,IAAG,KAAM,GAAG,EAAE;gBAC3B,MAAMG,WAAU,GAAID,UAAU,CAACpB,OAAO;gBACtC;gBACAqB,WAAW,CAACC,KAAI,GAAI,KAAK;gBACzB,MAAMC,SAAQ,GAAI,MAAMlE,OAAO,CAACyC,IAAI,CAACxC,IAAG,GAAI,kBAAkB,EAAE+D,WAAW,CAAC;gBAC5E,IAAIE,SAAS,CAACL,IAAG,KAAM,GAAG,EAAE;kBAC1B,IAAI,CAACJ,QAAQ,CAAC;oBACZC,OAAO,EAAE,MAAM;oBACfC,IAAI,EAAE,SAAS;oBACfC,MAAM,EAAE;kBACV,CAAC,CAAC;gBACJ,OAAO;kBACL,IAAI,CAACH,QAAQ,CAAC;oBACZC,OAAO,EAAE,MAAM;oBACfC,IAAI,EAAE,SAAS;oBACfC,MAAM,EAAE;kBACV,CAAC,CAAC;gBACJ;cACF;YACF;UACF,OAAO;YACL,IAAI,CAACH,QAAQ,CAAC;cACZC,OAAO,EAAE,MAAM;cACfC,IAAI,EAAE,SAAS;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ;UACA,IAAI,CAAC/C,kBAAiB,GAAI,KAAK;UAC/B,IAAI,CAACW,QAAQ,CAAC,CAAC;QACjB,OAAO;UACL,IAAI,CAACiC,QAAQ,CAAC;YACZC,OAAO,EAAElB,GAAG,CAAC2B,GAAE,IAAK,MAAM;YAC1BR,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;MACF,EAAE,OAAOf,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7B,IAAI,CAACY,QAAQ,CAAC;UACZC,OAAO,EAAE,UAAU;UACnBC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,UAAU;QACR,IAAI,CAACjD,UAAS,GAAI,KAAK;MACzB;IACF,CAAC;IAED;IACA,MAAMyD,aAAaA,CAACC,GAAG,EAAE;MACvB,IAAI;QACF;QACA,MAAMC,SAAQ,GAAI,MAAMtE,OAAO,CAACyC,IAAI,CAACxC,IAAG,GAAI,iBAAgB,GAAIoE,GAAG,CAAC;QACpE,IAAIC,SAAS,CAACT,IAAG,KAAM,GAAG,EAAE;UAC1B,IAAI,CAAC1C,UAAS,GAAImD,SAAS,CAAC3B,OAAO;UACnC;UACA,MAAMoB,UAAS,GAAI,MAAM/D,OAAO,CAACyC,IAAI,CAACxC,IAAG,GAAI,mBAAkB,GAAI,IAAI,CAACkB,UAAU,CAACd,GAAG,CAAC;UACvF,IAAI0D,UAAU,CAACF,IAAG,KAAM,GAAG,EAAE;YAC3B,IAAI,CAACzC,WAAU,GAAI2C,UAAU,CAACpB,OAAO;YACrC;YACA,MAAM,IAAI,CAAC4B,gBAAgB,CAAC,CAAC;UAC/B;UACA,IAAI,CAACzD,mBAAkB,GAAI,IAAI;QACjC;MACF,EAAE,OAAO+B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACY,QAAQ,CAAC;UACZC,OAAO,EAAE,UAAU;UACnBC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC;IAED;IACAY,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAACnE,IAAI,CAACC,WAAU,GAAIkE,GAAG;MAC3B,IAAI,CAACjD,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACA,MAAM+C,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAM/B,GAAE,GAAI,MAAMxC,OAAO,CAACyC,IAAI,CAC5BxC,IAAG,GAAI,qBAAqB,EAC5B,CAAC,CAAC,EACF;UAAEyC,MAAM,EAAE;YAAEnC,WAAW,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAI;QAAE,CAC9C,CAAC;QACD,IAAIgC,GAAG,CAACqB,IAAG,KAAM,GAAG,EAAE;UACpB,IAAI,CAACxC,iBAAgB,GAAImB,GAAG,CAACG,OAAO;UACpC,IAAI,CAAC+B,mBAAmB,CAAC,CAAC;QAC5B;MACF,EAAE,OAAO7B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;IAED;IACA6B,mBAAmBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAACtD,WAAW,CAACuD,KAAI,IAAK,IAAI,CAACtD,iBAAiB,CAACuD,MAAK,GAAI,CAAC,EAAE;QAC/D,MAAMC,YAAW,GAAI,IAAI,CAACxD,iBAAiB,CAACyD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACJ,KAAI,KAAM,IAAI,CAACvD,WAAW,CAACuD,KAAK,CAAC;QAC3F,IAAI,CAACrD,gBAAe,GAAIuD,YAAW,GAAIA,YAAY,CAACG,OAAM,GAAI,EAAE;MAClE;IACF;EACF;AACF,CAAC", "ignoreList": []}]}