{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Header.vue?vue&type=style&index=0&id=61dd7a3d&lang=css", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Header.vue", "mtime": 1741615313504}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5sb2dvLXNtIHsNCiAgICBmb250LXNpemU6IDIwcHg7DQogICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgLyog6LaF5a696Ieq5Yqo6ZqQ6JePICovDQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICB9DQoNCiAgLmxvZ28tbGcgew0KICAgIGZvbnQtc2l6ZTogMjBweDsNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgfQ0K"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Header.vue"], "names": [], "mappings": ";EAmGE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/components/Header.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"navbar-custom\">\r\n    <div class=\"topbar container-fluid\">\r\n      <div class=\"d-flex align-items-center gap-1\">\r\n        <div class=\"logo-topbar\">\r\n          <a href=\"\" class=\"logo-light\">\r\n            <span class=\"logo-lg\"> 求职系统 </span>\r\n            <span class=\"logo-sm\"> 求职系统 </span>\r\n          </a>\r\n          <a href=\"\" class=\"logo-dark\">\r\n            <span class=\"logo-lg\"> 求职系统 </span>\r\n            <span class=\"logo-sm\"> 求职系统 </span>\r\n          </a>\r\n        </div>\r\n      </div>\r\n      <ul class=\"topbar-menu d-flex align-items-center gap-3\">\r\n        <li class=\"dropdown\">\r\n          <a class=\"nav-link dropdown-toggle arrow-none nav-user\" data-bs-toggle=\"dropdown\" href=\"#\" role=\"button\"\r\n            aria-haspopup=\"false\" aria-expanded=\"false\">\r\n            <span class=\"account-user-avatar\">\r\n              <img :src=\"plogo\" alt=\"user-image\" width=\"32\" class=\"rounded-circle\" style=\"width: 32px; height: 32px\" />\r\n            </span>\r\n            <span class=\"d-lg-block d-none\">\r\n              <h5 class=\"my-0 fw-normal\">\r\n                【<b style=\"color: #d03f3f\">{{ role }}</b>】{{ userLname }}\r\n                <i class=\"ri-arrow-down-s-line d-none d-sm-inline-block align-middle\"></i>\r\n              </h5>\r\n            </span>\r\n          </a>\r\n          <div class=\"dropdown-menu dropdown-menu-end dropdown-menu-animated profile-dropdown\">\r\n            <div class=\"dropdown-header noti-title\">\r\n              <h6 class=\"text-overflow m-0\">欢迎您 !</h6>\r\n            </div>\r\n\r\n            <a href=\"/\" class=\"dropdown-item\" target=\"_blank\">\r\n              <i class=\"ri-home-2-fill fs-18 align-middle me-1\"></i>\r\n              <span>网站首页</span>\r\n            </a>\r\n            <a href=\"/Password\" class=\"dropdown-item\">\r\n              <i class=\"ri-lock-password-line fs-18 align-middle me-1\"></i>\r\n              <span>修改密码</span>\r\n            </a>\r\n            <a href=\"javascript:void(0)\" class=\"dropdown-item\" @click=\"exit\">\r\n              <i class=\"ri-logout-box-line fs-18 align-middle me-1\"></i>\r\n              <span>退出登录</span>\r\n            </a>\r\n          </div>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        plogo: require('../assets/img/avatar-1.jpg'),\r\n        userLname: '',\r\n        role: '',\r\n        clicknav: false,\r\n      };\r\n    },\r\n    mounted() {\r\n      this.userLname = sessionStorage.getItem('userLname');\r\n      this.role = sessionStorage.getItem('role');\r\n\r\n      if (this.role === '企业') {\r\n        var user = JSON.parse(sessionStorage.getItem('user'));\r\n        this.plogo = 'http://localhost:8088/JobHuntingSystem/' + user.logo;\r\n      }\r\n    },\r\n    methods: {\r\n      handleSelect(key, keyPath) {\r\n        console.log(key, keyPath);\r\n      },\r\n      showexists() {\r\n        console.log(333);\r\n        this.showexist = !this.showexist;\r\n      },\r\n\r\n      exit: function () {\r\n        var _this = this;\r\n        this.$confirm('确认退出吗?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        })\r\n          .then(() => {\r\n            sessionStorage.removeItem('userLname');\r\n            sessionStorage.removeItem('role');\r\n            _this.$router.push('/login');\r\n          })\r\n          .catch(() => { });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style>\r\n  .logo-sm {\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    /* 超宽自动隐藏 */\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .logo-lg {\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n  }\r\n</style>"]}]}