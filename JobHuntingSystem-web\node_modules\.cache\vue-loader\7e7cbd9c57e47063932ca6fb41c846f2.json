{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Login.vue?vue&type=style&index=0&id=26084dc2&scoped=true&lang=css", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Login.vue", "mtime": 1741615327525}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Login.vue"], "names": [], "mappings": ";EA0VE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/Login.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-form\">\n        <div class=\"login-header\">\n          <h2>求职系统</h2>\n          <p class=\"subtitle\">Job Hunting System</p>\n        </div>\n\n        <el-form :model=\"loginModel\" class=\"login-form-body\">\n          <el-form-item>\n            <el-input v-model=\"loginModel.username\" prefix-icon=\"User\" placeholder=\"请输入账号\" size=\"large\" />\n          </el-form-item>\n\n          <el-form-item>\n            <el-input v-model=\"loginModel.password\" prefix-icon=\"Lock\" type=\"password\" placeholder=\"请输入密码\" size=\"large\"\n              show-password />\n          </el-form-item>\n\n          <el-form-item class=\"role-select\">\n            <el-radio v-model=\"loginModel.role\" label=\"管理员\">管理员</el-radio>\n            <el-radio v-model=\"loginModel.role\" label=\"企业\">企业用户</el-radio>\n          </el-form-item>\n\n          <el-form-item>\n            <el-button type=\"primary\" class=\"login-button\" :loading=\"loading\" @click=\"login\" size=\"large\">\n              登 录\n            </el-button>\n          </el-form-item>\n        </el-form>\n\n        <div class=\"login-footer\">\n          <el-link type=\"primary\" @click=\"toreg\" href=\"#\">企业用户注册</el-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- 保留原有的注册对话框代码 -->\n    <el-dialog title=\"企业注册\" v-model=\"formVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n      <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\n        <el-form-item label=\"企业账号\" prop=\"clname\">\n          <el-input v-model=\"formData.clname\" placeholder=\"企业账号\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"登录密码\" prop=\"password\">\n          <el-input type=\"password\" v-model=\"formData.password\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"确认密码\" prop=\"password2\">\n          <el-input type=\"password\" v-model=\"formData.password2\" placeholder=\"确认密码\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"企业名称\" prop=\"comname\">\n          <el-input v-model=\"formData.comname\" placeholder=\"企业名称\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"企业规模\" prop=\"scale\">\n          <el-input v-model=\"formData.scale\" placeholder=\"企业规模\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"企业性质\" prop=\"nature\">\n          <el-input v-model=\"formData.nature\" placeholder=\"企业性质\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"联系方式\" prop=\"contact\">\n          <el-input v-model=\"formData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"联系地址\" prop=\"address\">\n          <el-input v-model=\"formData.address\" placeholder=\"联系地址\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item prop=\"logo\" label=\"企业logo\" min-width=\"20%\">\n          <el-input v-model=\"formData.logo\" placeholder=\"企业logo\" readonly=\"true\" style=\"width:50%;\"></el-input>\n          <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n        </el-form-item>\n        <el-form-item label=\"企业介绍\" prop=\"introduction\">\n          <el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.introduction\" placeholder=\"企业介绍\"\n            size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"reg\" :loading=\"btnLoading\">注 册</el-button>\n        </el-form-item>\n      </el-form>\n    </el-dialog>\n    <el-dialog v-model=\"uploadVisible\" title=\"附件上传\" custom-class=\"el-dialog-widthSmall\" @close=\"closeDialog\">\n      <div>\n        <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n      </div>\n      <el-upload action=\"http://localhost:8088/gouwu/api/common/uploadFile\" style=\"\n        margin: auto;\n        margin-top: 10px;\n        border: 1px solid #dcdfe6;\n        border-radius: 4px;\n      \" drag :limit=\"1\" :on-preview=\"handlePreview\" :on-remove=\"handleRemove\" :file-list=\"fileList\"\n        :on-exceed=\"handleExceed\" :auto-upload=\"false\" name=\"file\" :on-change=\"fileListChange\">\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">\n          将数据文件拖到此处，或<em>点击上传</em>\n        </div>\n        <div class=\"el-upload__tip\">\n          <div style=\"display: inline; color: #d70000; font-size: 14px\" class=\"uploadFileWarning\"\n            id=\"uploadFileWarning\"></div>\n        </div>\n      </el-upload>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"hideUpload\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import request, { base } from \"../../utils/http\";\n  export default {\n    name: \"Login\",\n    data() {\n      return {\n        year: new Date().getFullYear(),\n        loginModel: {\n          username: \"\",\n          password: \"\",\n          role: \"管理员\"\n        },\n        loginModel2: {},\n        add: true, //是否是添加\n        formVisible: false,\n        formData: {},\n\n        addrules: {\n          clname: [{ required: true, message: '请输入企业账号', trigger: 'blur' },],\n          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n          password2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n          comname: [{ required: true, message: '请输入企业名称', trigger: 'blur' },],\n          scale: [{ required: true, message: '请输入企业规模', trigger: 'blur' },],\n          nature: [{ required: true, message: '请输入企业性质', trigger: 'blur' },],\n          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },],\n          logo: [{ required: true, message: '请上传企业logo', trigger: 'blur' }],\n          introduction: [{ required: true, message: '请输入企业介绍', trigger: 'blur' },],\n        },\n\n        btnLoading: false, //按钮是否在加载中\n        uploadVisible: false, //上传弹出框\n        loading: false,\n      };\n    },\n    mounted() { },\n    created() {\n\n    },\n    methods: {\n      login() {\n        if (this.loginModel.role === \"管理员\") {\n          let url = base + \"/admin/login\";\n          this.loginModel2.aname = this.loginModel.username;\n          this.loginModel2.password = this.loginModel.password;\n          request.post(url, this.loginModel2).then((res) => {\n            this.loading = false;\n            if (res.code == 200) {\n              console.log(JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"userLname\", res.resdata.aname);\n              sessionStorage.setItem(\"role\", \"管理员\");\n              this.$router.push(\"/main\");\n            } else {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n              });\n            }\n          });\n        } else {\n          // 企业登录逻辑\n          let url = base + \"/company/login\";\n          this.loading = true;\n          this.loginModel2.clname = this.loginModel.username;\n          this.loginModel2.password = this.loginModel.password;\n          request.post(url, this.loginModel2).then((res) => {\n            this.loading = false;\n            if (res.code == 200) {\n              console.log(JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"userLname\", res.resdata.clname);\n              sessionStorage.setItem(\"role\", \"企业\");\n              this.$router.push(\"/main\");\n            } else {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n              });\n            }\n          });\n        }\n      },\n\n      toreg() {\n        this.formVisible = true;\n        this.add = true;\n        this.isClear = true;\n        this.rules = this.addrules;\n        this.$nextTick(() => {\n          this.$refs[\"formDataRef\"].resetFields();\n        });\n      },\n\n      //注册\n      reg() {\n        //表单验证\n        this.$refs[\"formDataRef\"].validate((valid) => {\n\n          if (valid) {\n            let url = base + \"/company/add\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口             \n              if (res.code == 200) {\n                this.$message({\n                  message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.formVisible = false; //关闭表单\n                this.btnLoading = false; //按钮加载状态\n                this.$refs[\"formDataRef\"].resetFields(); //重置表单\n                this.$refs[\"formDataRef\"].clearValidate();\n              }\n              else if (res.code == 201) {\n                this.$message({\n                  message: res.msg,\n                  type: \"error\",\n                  offset: 320,\n                });\n              }\n              else {\n                this.$message({\n                  message: \"服务器错误\",\n                  type: \"error\",\n                  offset: 320,\n                });\n              }\n            });\n          }\n        });\n      },\n      //显示上传框\n      showUpload() {\n        this.uploadVisible = true;\n      },\n\n      //隐藏上传框\n      hideUpload() {\n        this.uploadVisible = false;\n      },\n      //上传\n      handleRemove(file, fileList) {\n        this.fileList = fileList;\n      },\n      handlePreview(file) {\n        console.log(file);\n      },\n      handleExceed(files, fileList) {\n        this.$message({\n          duration: 1000,\n          message: \"只能上传一个文件\",\n          type: \"error\",\n          offset: 320,\n        });\n      },\n      // 判断上传文件后缀\n      fileListChange(file, fileList) {\n        let extendFileName = \"png,jpg\";\n        let extendFileNames = extendFileName.split(\",\");\n        let regExpRules = [];\n        for (let i = 0; i < extendFileNames.length; i++) {\n          regExpRules.push(\n            new RegExp(\"(.*).(\" + extendFileNames[i] + \")$\", \"gim\")\n          );\n        }\n        let fileNames = [];\n        let files = [];\n        let that = this;\n        fileList.forEach(function (key, val) {\n          let ret = false;\n          for (let i = 0; i < regExpRules.length; i++) {\n            ret = ret || regExpRules[i].test(key[\"name\"]);\n          }\n          if (!ret) {\n            console.log(key[\"name\"] + \":\" + ret);\n            that.$message({\n              duration: 1000,\n              message: \"上传的文件后缀必须为\" + extendFileName + \"格式！\",\n              type: \"error\",\n              offset: 320,\n            });\n            return false;\n          }\n          if (fileNames.indexOf(key[\"name\"]) != -1) {\n            that.$message({\n              duration: 1000,\n              message: \"上传的文件重复！\",\n              type: \"error\",\n              offset: 320,\n            });\n            return false;\n          }\n          //只能上传一个文件，用最后上传的覆盖\n          if (!that.multiFiles) {\n            files = [];\n            fileNames = [];\n          }\n          files.push(key);\n          fileNames.push(key[\"name\"]);\n          if (fileNames !== \"\") {\n            // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n          }\n          //$(\".uploadFileWarning\").text(\"\");\n        });\n        this.files = fileNames;\n        this.fileList = files;\n      },\n      /**\n       * 确认按钮\n       */\n      handleConfirm() {\n        let filePath = this.fileList;\n        if (filePath.length === 0) {\n          this.$message({\n            duration: 1000,\n            message: \"请选择文件！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        let formData = new FormData();\n        this.fileList.forEach((file) => {\n          formData.append(\"file\", file.raw, file.raw.name);\n        });\n        let url = base + \"/common/uploadFile\";\n        console.log(\"url=\" + url);\n        request.post(url, formData).then((res) => {\n          console.log(res);\n          let furl = res.resdata.filePath;\n          this.formData.logo = furl;  // 上传文件的路径\n          this.hideUpload();\n          console.log(res);\n        });\n      },\n    },\n  };\n</script>\n\n<style scoped>\n  .login-container {\n    min-height: 100vh;\n    background: linear-gradient(135deg, #1e4db7 0%, #1e4db7 100%);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 0;\n    padding: 0;\n  }\n\n  .login-box {\n    width: 420px;\n    padding: 35px;\n    background: rgba(255, 255, 255, 0.98);\n    border-radius: 12px;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  }\n\n  .login-header {\n    text-align: center;\n    margin-bottom: 35px;\n  }\n\n  .login-header h2 {\n    color: #1e4db7;\n    font-size: 26px;\n    margin: 0;\n    font-weight: 600;\n  }\n\n  .subtitle {\n    color: #666;\n    font-size: 14px;\n    margin-top: 8px;\n  }\n\n  .login-form-body {\n    margin-top: 20px;\n  }\n\n  .login-form-body :deep(.el-form-item) {\n    margin-bottom: 25px;\n  }\n\n  .role-select {\n    text-align: center;\n    margin: 5px 0 20px;\n  }\n\n  .role-select :deep(.el-radio) {\n    margin-right: 30px;\n  }\n\n  .role-select :deep(.el-radio__label) {\n    color: #666;\n  }\n\n  .role-select :deep(.el-radio__input.is-checked + .el-radio__label) {\n    color: #1e4db7;\n  }\n\n  .login-button {\n    width: 100%;\n    height: 44px;\n    font-size: 16px;\n    background: #1e4db7;\n    border-color: #1e4db7;\n  }\n\n  .login-button:hover {\n    background: #2857c1;\n    border-color: #2857c1;\n  }\n\n  .login-footer {\n    margin-top: 20px;\n    text-align: center;\n  }\n\n  :deep(.el-input__wrapper) {\n    box-shadow: 0 0 0 1px #dcdfe6 inset;\n    background: #f5f7fa;\n  }\n\n  :deep(.el-input__wrapper:hover) {\n    box-shadow: 0 0 0 1px #1e4db7 inset;\n  }\n\n  :deep(.el-input__wrapper.is-focus) {\n    box-shadow: 0 0 0 1px #1e4db7 inset !important;\n  }\n\n  :deep(.el-radio__inner) {\n    border-color: #dcdfe6;\n  }\n\n  :deep(.el-radio__input.is-checked .el-radio__inner) {\n    background: #1e4db7;\n    border-color: #1e4db7;\n  }\n</style>"]}]}