{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyEdit.vue", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "clname", "required", "message", "trigger", "password", "comname", "scale", "nature", "contact", "address", "logo", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "$refs", "editor", "txt", "html", "introduction", "save", "validate", "valid", "code", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack", "showUpload", "hideUpload", "handleRemove", "file", "fileList", "handlePreview", "console", "log", "handleExceed", "files", "duration", "fileListChange", "extendFileName", "extendFileNames", "split", "regExpRules", "i", "length", "RegExp", "fileNames", "that", "for<PERSON>ach", "key", "val", "ret", "test", "indexOf", "multiFiles", "handleConfirm", "filePath", "FormData", "append", "raw", "furl", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyEdit.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">编辑企业</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">编辑企业</h4>\r\n      </div>\r\n    </div>\r\n     <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"企业账号\" prop=\"clname\">\r\n<el-input v-model=\"formData.clname\" placeholder=\"企业账号\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"登录密码\" prop=\"password\">\r\n<el-input v-model=\"formData.password\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"企业名称\" prop=\"comname\">\r\n<el-input v-model=\"formData.comname\" placeholder=\"企业名称\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"企业规模\" prop=\"scale\">\r\n<el-input v-model=\"formData.scale\" placeholder=\"企业规模\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"企业性质\" prop=\"nature\">\r\n<el-input v-model=\"formData.nature\" placeholder=\"企业性质\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系方式\" prop=\"contact\">\r\n<el-input v-model=\"formData.contact\" placeholder=\"联系方式\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系地址\" prop=\"address\">\r\n<el-input v-model=\"formData.address\" placeholder=\"联系地址\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item prop=\"logo\" label=\"企业logo\"  min-width=\"20%\">\r\n<el-input  v-model=\"formData.logo\" placeholder=\"企业logo\"  readonly=\"true\" style=\"width:50%;\" ></el-input>\r\n<el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\r\n</el-form-item>\r\n<el-form-item label=\"企业介绍\" prop=\"introduction\">\r\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.introduction\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n     <el-dialog\n        v-model=\"uploadVisible\"\n        title=\"附件上传\"\n        custom-class=\"el-dialog-widthSmall\"\n        @close=\"closeDialog\"\n      >\n        <div>\n          <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n        </div>\n        <el-upload\n          action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n          style=\"\n            margin: auto;\n            margin-top: 10px;\n            border: 1px solid #dcdfe6;\n            border-radius: 4px;\n          \"\n          drag\n          :limit=\"1\"\n          :on-preview=\"handlePreview\"\n          :on-remove=\"handleRemove\"\n          :file-list=\"fileList\"\n          :on-exceed=\"handleExceed\"\n          :auto-upload=\"false\"\n          name=\"file\"\n          :on-change=\"fileListChange\"\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">\n            将数据文件拖到此处，或<em>点击上传</em>\n          </div>\n          <div class=\"el-upload__tip\">\n            <div\n              style=\"display: inline; color: #d70000; font-size: 14px\"\n              class=\"uploadFileWarning\"\n              id=\"uploadFileWarning\"\n            ></div>\n          </div>\n        </el-upload>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"hideUpload\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n        </span>\n      </el-dialog>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\nimport WangEditor from \"../../../components/WangEditor\";\nexport default {\n  name: 'CompanyEdit',\n  components: {\n    WangEditor,\n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          clname: [{ required: true, message: '请输入企业账号', trigger: 'blur' },\r\n],          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' },\r\n],          comname: [{ required: true, message: '请输入企业名称', trigger: 'blur' },\r\n],          scale: [{ required: true, message: '请输入企业规模', trigger: 'blur' },\r\n],          nature: [{ required: true, message: '请输入企业性质', trigger: 'blur' },\r\n],          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },\r\n],          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },\r\n],          logo: [{ required: true, message: '请输入企业logo', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n    },\r\n\r\n \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/company/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            this.$refs[\"wangEditorRef\"].editor.txt.html(this.formData.introduction);\n            \n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/company/update\";\n              this.btnLoading = true;\n              \n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });\n                  this.$router.push({\n                    path: \"/CompanyManage\",\n                  });\n                } else {\n                  this.$message({\n                    message:res.msg,\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n                this.btnLoading = false;\n              });\n            }\n    \n          });\n        },\n        \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/CompanyManage\",\n          });\n        },       \n              \n          \n            //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: \"只能上传一个文件\",\n        type: \"error\",\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = \"png,jpg\";\n      let extendFileNames = extendFileName.split(\",\");\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(\n          new RegExp(\"(.*).(\" + extendFileNames[i] + \")$\", \"gim\")\n        );\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key[\"name\"]);\n        }\n        if (!ret) {\n          console.log(key[\"name\"] + \":\" + ret);\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件后缀必须为\" + extendFileName + \"格式！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key[\"name\"]) != -1) {\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件重复！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key[\"name\"]);\n        if (fileNames !== \"\") {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: \"请选择文件！\",\n          type: \"error\",\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append(\"file\", file.raw, file.raw.name);\n      });\n      let url = base + \"/common/uploadFile\";\n      console.log(\"url=\" + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.logo = furl;  // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n\r\n   \n            // 富文本编辑器\n    editorChange(val) {\n      this.formData.introduction = val;\n    },\r\n   \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;;AAkGA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAOC,UAAS,MAAO,gCAAgC;AACvD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVF;EACF,CAAC;EACCG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CACzE;QAAWC,QAAQ,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC7E;QAAWE,OAAO,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC5E;QAAWG,KAAK,EAAE,CAAC;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC1E;QAAWI,MAAM,EAAE,CAAC;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC3E;QAAWK,OAAO,EAAE,CAAC;UAAEP,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC5E;QAAWM,OAAO,EAAE,CAAC;UAAER,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC5E;QAAWO,IAAI,EAAE,CAAC;UAAET,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC;MAClE;IAEJ,CAAC;EACH,CAAC;EACDQ,OAAOA,CAAA,EAAG;IACV,IAAI,CAACjB,EAAC,GAAI,IAAI,CAACkB,MAAM,CAACC,KAAK,CAACnB,EAAE;IAC5B,IAAI,CAACoB,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAEb;IACQD,QAAQA,CAAA,EAAG;MACT,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAI7B,IAAG,GAAI,kBAAiB,GAAI,IAAI,CAACK,EAAE;MAC7CN,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACvB,QAAO,GAAIwB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;QACxB,IAAI,CAACS,KAAK,CAAC,eAAe,CAAC,CAACC,MAAM,CAACC,GAAG,CAACC,IAAI,CAAC,IAAI,CAAC/B,QAAQ,CAACgC,YAAY,CAAC;MAEzE,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACL,KAAK,CAAC,aAAa,CAAC,CAACM,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIf,GAAE,GAAI7B,IAAG,GAAI,iBAAiB;UAClC,IAAI,CAACQ,UAAS,GAAI,IAAI;UAEtBT,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAE,IAAI,CAACpB,QAAQ,CAAC,CAACsB,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACa,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZjC,OAAO,EAAE,MAAM;gBACfkC,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZjC,OAAO,EAACmB,GAAG,CAACoB,GAAG;gBACfL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAACxC,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACJ,CAAC;IAEF;IACC6C,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAGG;IACRG,UAAUA,CAAA,EAAG;MACX,IAAI,CAAC/C,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACAgD,UAAUA,CAAA,EAAG;MACX,IAAI,CAAChD,aAAY,GAAI,KAAK;IAC5B,CAAC;IACD;IACAiD,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAO,GAAIA,QAAQ;IAC1B,CAAC;IACDC,aAAaA,CAACF,IAAI,EAAE;MAClBG,OAAO,CAACC,GAAG,CAACJ,IAAI,CAAC;IACnB,CAAC;IACDK,YAAYA,CAACC,KAAK,EAAEL,QAAQ,EAAE;MAC5B,IAAI,CAACZ,QAAQ,CAAC;QACZkB,QAAQ,EAAE,IAAI;QACdnD,OAAO,EAAE,UAAU;QACnBkC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD;IACAiB,cAAcA,CAACR,IAAI,EAAEC,QAAQ,EAAE;MAC7B,IAAIQ,cAAa,GAAI,SAAS;MAC9B,IAAIC,eAAc,GAAID,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC;MAC/C,IAAIC,WAAU,GAAI,EAAE;MACpB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,eAAe,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/CD,WAAW,CAACnB,IAAI,CACd,IAAIsB,MAAM,CAAC,QAAO,GAAIL,eAAe,CAACG,CAAC,IAAI,IAAI,EAAE,KAAK,CACxD,CAAC;MACH;MACA,IAAIG,SAAQ,GAAI,EAAE;MAClB,IAAIV,KAAI,GAAI,EAAE;MACd,IAAIW,IAAG,GAAI,IAAI;MACfhB,QAAQ,CAACiB,OAAO,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnC,IAAIC,GAAE,GAAI,KAAK;QACf,KAAK,IAAIR,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3CQ,GAAE,GAAIA,GAAE,IAAKT,WAAW,CAACC,CAAC,CAAC,CAACS,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C;QACA,IAAI,CAACE,GAAG,EAAE;UACRlB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC,MAAM,IAAI,GAAE,GAAIE,GAAG,CAAC;UACpCJ,IAAI,CAAC5B,QAAQ,CAAC;YACZkB,QAAQ,EAAE,IAAI;YACdnD,OAAO,EAAE,YAAW,GAAIqD,cAAa,GAAI,KAAK;YAC9CnB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA,IAAIyB,SAAS,CAACO,OAAO,CAACJ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;UACxCF,IAAI,CAAC5B,QAAQ,CAAC;YACZkB,QAAQ,EAAE,IAAI;YACdnD,OAAO,EAAE,UAAU;YACnBkC,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA;QACA,IAAI,CAAC0B,IAAI,CAACO,UAAU,EAAE;UACpBlB,KAAI,GAAI,EAAE;UACVU,SAAQ,GAAI,EAAE;QAChB;QACAV,KAAK,CAACb,IAAI,CAAC0B,GAAG,CAAC;QACfH,SAAS,CAACvB,IAAI,CAAC0B,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAIH,SAAQ,KAAM,EAAE,EAAE;UACpB;QAAA;QAEF;MACF,CAAC,CAAC;MACF,IAAI,CAACV,KAAI,GAAIU,SAAS;MACtB,IAAI,CAACf,QAAO,GAAIK,KAAK;IACvB,CAAC;IACD;;;IAGAmB,aAAaA,CAAA,EAAG;MACd,IAAIC,QAAO,GAAI,IAAI,CAACzB,QAAQ;MAC5B,IAAIyB,QAAQ,CAACZ,MAAK,KAAM,CAAC,EAAE;QACzB,IAAI,CAACzB,QAAQ,CAAC;UACZkB,QAAQ,EAAE,IAAI;UACdnD,OAAO,EAAE,QAAQ;UACjBkC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MACA,IAAIvC,QAAO,GAAI,IAAI2E,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC1B,QAAQ,CAACiB,OAAO,CAAElB,IAAI,IAAK;QAC9BhD,QAAQ,CAAC4E,MAAM,CAAC,MAAM,EAAE5B,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC6B,GAAG,CAACpF,IAAI,CAAC;MAClD,CAAC,CAAC;MACF,IAAI2B,GAAE,GAAI7B,IAAG,GAAI,oBAAoB;MACrC4D,OAAO,CAACC,GAAG,CAAC,MAAK,GAAIhC,GAAG,CAAC;MACzB9B,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAEpB,QAAQ,CAAC,CAACsB,IAAI,CAAEC,GAAG,IAAK;QACxC4B,OAAO,CAACC,GAAG,CAAC7B,GAAG,CAAC;QAChB,IAAIuD,IAAG,GAAIvD,GAAG,CAACI,OAAO,CAAC+C,QAAQ;QAC/B,IAAI,CAAC1E,QAAQ,CAACY,IAAG,GAAIkE,IAAI,EAAG;QAC5B,IAAI,CAAChC,UAAU,CAAC,CAAC;QACjBK,OAAO,CAACC,GAAG,CAAC7B,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC;IAGO;IACRwD,YAAYA,CAACX,GAAG,EAAE;MAChB,IAAI,CAACpE,QAAQ,CAACgC,YAAW,GAAIoC,GAAG;IAClC;EAEE;AACN", "ignoreList": []}]}