{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs.vue?vue&type=template&id=310a34a4", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs.vue", "mtime": 1741614414000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["width", "style", "class", "_createElementBlock", "_Fragment", "_renderList", "$data", "bbslist", "item", "key", "bid", "_createElementVNode", "href", "btitle", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_toDisplayString", "addtime", "_hoisted_4", "btotal", "_hoisted_5", "sno", "_hoisted_6", "_createVNode", "_component_el_pagination", "onCurrentChange", "$options", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount", "_component_el_form", "model", "formData", "ref", "rules", "align", "_component_el_form_item", "label", "prop", "_component_el_input", "$event", "placeholder", "_component_WangEditor", "bdetail", "config", "_ctx", "editorConfig", "isClear", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "height", "_component_el_button", "type", "onClick", "save", "loading", "btnLoading", "_cache"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs.vue"], "sourcesContent": ["<template>\r\n  <table style=\"width: 100%;line-height:32px;font-size:13px\" class=\"bbs\" v-for=\"item in bbslist\" :key=\"item.bid\">\r\n    <tr>\r\n      <td>\r\n        <a :href=\"'bbsView?id=' + item.bid\">{{ item.btitle }}\r\n        </a>\r\n      </td>\r\n      <td width=\"400\">\r\n        <span style=\"float:right;\">\r\n          {{ item.addtime }}\r\n        </span>\r\n        <span class=\"cu-tag line-red\" style=\"height:28px;float:right;  margin-right: 10px;\">点击量：{{\r\n            item.btotal\r\n          }}</span>\r\n        <span class=\"cu-tag line-green light\" style=\"float:right; margin-right: 10px; height:28px; line-height:28px \">\r\n          发布人：{{ item.sno }}\r\n        </span>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n\r\n  <div style=\"width: 100%;display: inline-table;\">\r\n    <el-pagination @current-change=\"handleCurrentChange\"\r\n                   :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" background\r\n                   layout=\"total, prev, pager, next, jumper\"\r\n                   :total=\"page.totalCount\" style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n  </div>\r\n\r\n\r\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\r\n    <el-form-item label=\"帖子标题\" prop=\"btitle\">\r\n      <el-input v-model=\"formData.btitle\" placeholder=\"帖子标题\" style=\"width:50%;\"></el-input>\r\n    </el-form-item>\r\n    <el-form-item label=\"帖子内容\" prop=\"bdetail\">\r\n      <WangEditor ref=\"wangEditorRef\" v-model=\"formData.bdetail\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n                  @change=\"editorChange\" height=\"200\"></WangEditor>\r\n    </el-form-item>\r\n    <el-form-item label=\"\">\r\n      <el-button type=\"primary\" @click=\"save\" :loading=\"btnLoading\">提 交</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport request, {base} from \"../../../utils/http\";\r\nimport WangEditor from \"../../components/WangEditor\";\r\n\r\nexport default {\r\n  name: \"bbs\",\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 15, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n      },\r\n      bbslist: \"\",\r\n\r\n      formData: {}, //表单数据\r\n      rules: {\r\n        btitle: [\r\n          {required: true, message: \"请输入帖子标题\", trigger: \"blur\"},\r\n        ],\r\n        bdetail: [\r\n          {required: true, message: \"请输入帖子内容\", trigger: \"blur\"},\r\n        ],\r\n      },\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.keyword = this.$route.query.keyword;\r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 分页\r\n    handleCurrentChange(val) {\r\n      this.page.currentPage = val;\r\n\r\n      this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {      \r\n\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/bbs/list?currentPage=\" + this.page.currentPage + \"&pageSize=\" + this.page.pageSize;\r\n      request.post(url, para).then((res) => {\r\n        if (res.resdata.length > 0) {\r\n          this.isPage = true;\r\n        } else {\r\n          this.isPage = false;\r\n        }\r\n        this.page.totalCount = res.count;\r\n        this.bbslist = res.resdata;\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n\r\n    //保存\r\n    save() {\r\n\r\n      var lname = sessionStorage.getItem(\"lname\");\r\n      if (lname == null) {\r\n        this.$message({\r\n          message: \"请先登录\",\r\n          type: \"error\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs.formDataRef.validate((valid) => {\r\n        if (valid) {\r\n          let para = {\r\n            bdetail: this.formData.bdetail,\r\n            btitle: this.formData.btitle,\r\n            sno: lname,\r\n            btotal: 0,\r\n          };\r\n          this.btnLoading = true;\r\n          let url = base + \"/bbs/add\";\r\n          request.post(url, para).then((res) => {\r\n            this.btnLoading = false;\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: \"提交成功\",\r\n                type: \"success\",\r\n              });\r\n              this.formData = {};\r\n\r\n              //设置富文本编辑器内容\r\n              this.$nextTick(() => {\r\n                this.$refs[\"wangEditorRef\"].editor.txt.html(\"\");\r\n              });\r\n\r\n              this.getDatas();\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.bdetail = val;\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 200px;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";;;EAOUA,KAAK,EAAC;AAAK;;EACPC,KAAoB,EAApB;IAAA;EAAA;AAAoB;;EAGpBC,KAAK,EAAC,iBAAiB;EAACD,KAAqD,EAArD;IAAA;IAAA;IAAA;EAAA;;;EAGxBC,KAAK,EAAC,yBAAyB;EAACD,KAAuE,EAAvE;IAAA;IAAA;IAAA;IAAA;EAAA;;;EAQvCA,KAA0C,EAA1C;IAAA;IAAA;EAAA;AAA0C;;;;;;;;gFArB/CE,mBAAA,CAkBQC,SAAA,QAAAC,WAAA,CAlB8EC,KAAA,CAAAC,OAAO,EAAfC,IAAI;yBAAlFL,mBAAA,CAkBQ;MAlBDF,KAAmD,EAAnD;QAAA;QAAA;QAAA;MAAA,CAAmD;MAACC,KAAK,EAAC,KAAK;MAA0BO,GAAG,EAAED,IAAI,CAACE;QACxGC,mBAAA,CAgBK,aAfHA,mBAAA,CAGK,aAFHA,mBAAA,CACI;MADAC,IAAI,kBAAkBJ,IAAI,CAACE;wBAAQF,IAAI,CAACK,MAAM,wBAAAC,UAAA,E,GAGpDH,mBAAA,CAUK,MAVLI,UAUK,GATHJ,mBAAA,CAEO,QAFPK,UAEO,EAAAC,gBAAA,CADFT,IAAI,CAACU,OAAO,kBAEjBP,mBAAA,CAEW,QAFXQ,UAEW,EAFyE,MAAI,GAAAF,gBAAA,CACpFT,IAAI,CAACY,MAAM,kBAEfT,mBAAA,CAEO,QAFPU,UAEO,EAFuG,OACxG,GAAAJ,gBAAA,CAAGT,IAAI,CAACc,GAAG,iB;kCAOvBX,mBAAA,CAKM,OALNY,UAKM,GAJJC,YAAA,CAGoGC,wBAAA;IAHpFC,eAAc,EAAEC,QAAA,CAAAC,mBAAmB;IACnC,cAAY,EAAEtB,KAAA,CAAAuB,IAAI,CAACC,WAAW;IAAG,WAAS,EAAExB,KAAA,CAAAuB,IAAI,CAACE,QAAQ;IAAEC,UAAU,EAAV,EAAU;IACtEC,MAAM,EAAC,kCAAkC;IACxCC,KAAK,EAAE5B,KAAA,CAAAuB,IAAI,CAACM,UAAU;IAAElC,KAA2C,EAA3C;MAAA;MAAA;IAAA;wFAI1CuB,YAAA,CAWUY,kBAAA;IAXAC,KAAK,EAAE/B,KAAA,CAAAgC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAElC,KAAA,CAAAkC,KAAK;IAAEC,KAAK,EAAC;;sBAClF,MAEe,CAFfjB,YAAA,CAEekB,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAqF,CAArFpB,YAAA,CAAqFqB,mBAAA;oBAAlEvC,KAAA,CAAAgC,QAAQ,CAACzB,MAAM;mEAAfP,KAAA,CAAAgC,QAAQ,CAACzB,MAAM,GAAAiC,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAC9C,KAAkB,EAAlB;UAAA;QAAA;;;QAEzDuB,YAAA,CAGekB,uBAAA;MAHDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAC6D,CAD7DpB,YAAA,CAC6DwB,qBAAA;QADjDT,GAAG,EAAC,eAAe;oBAAUjC,KAAA,CAAAgC,QAAQ,CAACW,OAAO;mEAAhB3C,KAAA,CAAAgC,QAAQ,CAACW,OAAO,GAAAH,MAAA;QAAGI,MAAM,EAAEC,IAAA,CAAAC,YAAY;QAAGC,OAAO,EAAEF,IAAA,CAAAE,OAAO;QACtFC,QAAM,EAAE3B,QAAA,CAAA4B,YAAY;QAAEC,MAAM,EAAC;;;QAE5ChC,YAAA,CAEekB,uBAAA;MAFDC,KAAK,EAAC;IAAE;wBACpB,MAA6E,CAA7EnB,YAAA,CAA6EiC,oBAAA;QAAlEC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAEhC,QAAA,CAAAiC,IAAI;QAAGC,OAAO,EAAEV,IAAA,CAAAW;;0BAAY,MAAGC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E", "ignoreList": []}]}