{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue?vue&type=style&index=0&id=91b6ff58&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue", "mtime": 1741616960745}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue"], "names": [], "mappings": ";EAmKE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,EAAE,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd;EACF", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/components/TopMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"top-menu\">\r\n    <!-- 顶部信息栏 -->\r\n    <div class=\"top-bar\">\r\n      <div class=\"container\">\r\n        <div class=\"top-bar-content\">\r\n          <div class=\"contact-info\">\r\n            <a href=\"mailto:<EMAIL>\" class=\"contact-item\">\r\n              <i class=\"fas fa-envelope\"></i>\r\n              <span><EMAIL></span>\r\n            </a>\r\n            <a href=\"tel:01066666666\" class=\"contact-item\">\r\n              <i class=\"fas fa-phone\"></i>\r\n              <span>010 6666 6666</span>\r\n            </a>\r\n          </div>\r\n          <div class=\"user-info\">\r\n            <template v-if=\"islogin\">\r\n              <a href=\"/Sreg\" class=\"user-link\">求职者注册</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"/Slogin\" class=\"user-link\">求职者登录</a>\r\n            </template>\r\n            <template v-else>\r\n              <span class=\"welcome-text\">欢迎您：</span>\r\n              <a href=\"/sweclome\" class=\"user-name\">{{ lname }}</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"javascript:void(0);\" @click=\"exit\" class=\"logout-link\">退出登录</a>\r\n            </template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主导航栏 -->\r\n    <div class=\"main-nav\">\r\n      <div class=\"container\">\r\n        <div class=\"nav-content\">\r\n          <!-- Logo -->\r\n          <a href=\"/index\" class=\"logo\">\r\n            <img src=\"../assets/images/main-logo.png\" alt=\"求职系统\" />\r\n          </a>\r\n\r\n          <!-- 移动端菜单按钮 -->\r\n          <div class=\"mobile-toggle\" @click=\"toggleMobileMenu\">\r\n            <span></span>\r\n            <span></span>\r\n            <span></span>\r\n          </div>\r\n\r\n          <!-- 导航菜单 -->\r\n          <div class=\"nav-menu\" :class=\"{ 'active': mobileMenuActive }\">\r\n            <ul class=\"nav-list\">\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/index' }\">\r\n                <a href=\"/index\" class=\"nav-link\">网站首页</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/positionsList' }\">\r\n                <a href=\"/positionsList\" class=\"nav-link\">招聘职位</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/companyList' }\">\r\n                <a href=\"/companyList\" class=\"nav-link\">企业展示</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/bbs' }\">\r\n                <a href=\"/bbs\" class=\"nav-link\">交流论坛</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/ai' }\">\r\n                <a href=\"/ai\" class=\"nav-link\">AI顾问</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/sweclome' }\">\r\n                <a href=\"/sweclome\" class=\"nav-link\">个人中心</a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- 搜索按钮 -->\r\n          <div class=\"search-btn\" @click=\"showSearchDialog\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索对话框 -->\r\n    <el-dialog v-model=\"searchDialogVisible\" title=\"职位搜索\" width=\"30%\" class=\"search-dialog\">\r\n      <el-form>\r\n        <el-form-item>\r\n          <el-input v-model=\"searchKeyword\" placeholder=\"请输入职位关键词\" prefix-icon=\"el-icon-search\"\r\n            @keyup.enter=\"handleSearch\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"searchDialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">搜 索</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../utils/http';\r\n  import '../assets/css/font-awesome.min.css';\r\n  import '../assets/css/qbootstrap.min.css';\r\n  import '../assets/css/qanimate.css';\r\n  import '../assets/css/owl.carousel.min.css';\r\n  import '../assets/css/owl.theme.default.min.css';\r\n  import '../assets/css/qmain.css';\r\n  import { ElMessage } from 'element-plus';\r\n\r\n  export default {\r\n    name: 'TopMenu',\r\n    data() {\r\n      return {\r\n        islogin: true,\r\n        lname: '',\r\n        ishow: false,\r\n        key: '',\r\n        searchDialogVisible: false,\r\n        searchKeyword: '',\r\n        mobileMenuActive: false,\r\n        activePath: ''\r\n      };\r\n    },\r\n    mounted() {\r\n      this.lname = sessionStorage.getItem('lname');\r\n      if (this.lname) {\r\n        this.islogin = false;\r\n      }\r\n      // 获取当前路径\r\n      this.activePath = window.location.pathname;\r\n    },\r\n    methods: {\r\n      exit: function () {\r\n        var _this = this;\r\n        this.$confirm('确认退出吗?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        })\r\n          .then(() => {\r\n            sessionStorage.removeItem('lname');\r\n            location.href = '/index';\r\n          })\r\n          .catch(() => { });\r\n      },\r\n      showSearchDialog() {\r\n        this.searchDialogVisible = true;\r\n      },\r\n      handleSearch() {\r\n        if (!this.searchKeyword) {\r\n          ElMessage.warning('请输入搜索关键词');\r\n          return;\r\n        }\r\n        location.href = '/positionsList?keyword=' + this.searchKeyword;\r\n      },\r\n      toggleMobileMenu() {\r\n        this.mobileMenuActive = !this.mobileMenuActive;\r\n      }\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 整体容器 */\r\n  .top-menu {\r\n    font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\r\n  }\r\n\r\n  /* 顶部信息栏 */\r\n  .top-bar {\r\n    background-color: #f8f9fa;\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .top-bar-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .contact-info {\r\n    display: flex;\r\n  }\r\n\r\n  .contact-item {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #666;\r\n    margin-right: 20px;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .contact-item:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .contact-item i {\r\n    margin-right: 8px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-info {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .user-link,\r\n  .user-name,\r\n  .logout-link {\r\n    color: #666;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .user-link:hover,\r\n  .user-name:hover,\r\n  .logout-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-name {\r\n    color: #3498db;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .welcome-text {\r\n    color: #666;\r\n  }\r\n\r\n  .divider {\r\n    margin: 0 10px;\r\n    color: #ccc;\r\n  }\r\n\r\n  /* 主导航栏 */\r\n  .main-nav {\r\n    background-color: #fff;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n    position: relative;\r\n    z-index: 100;\r\n  }\r\n\r\n  .nav-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 0;\r\n    position: relative;\r\n  }\r\n\r\n  .logo {\r\n    display: block;\r\n    margin-right: 30px;\r\n  }\r\n\r\n  .logo img {\r\n    height: 50px;\r\n    display: block;\r\n  }\r\n\r\n  .mobile-toggle {\r\n    display: none;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    width: 30px;\r\n    height: 22px;\r\n    cursor: pointer;\r\n    z-index: 101;\r\n  }\r\n\r\n  .mobile-toggle span {\r\n    display: block;\r\n    height: 3px;\r\n    width: 100%;\r\n    background-color: #333;\r\n    border-radius: 3px;\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  .nav-menu {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .nav-list {\r\n    display: flex;\r\n    list-style: none;\r\n    margin: 0;\r\n    padding: 0;\r\n  }\r\n\r\n  .nav-item {\r\n    position: relative;\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .nav-link {\r\n    display: block;\r\n    padding: 10px 15px;\r\n    color: #333;\r\n    font-weight: 500;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .nav-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active .nav-link {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -15px;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 3px;\r\n    background-color: #3498db;\r\n  }\r\n\r\n  .search-btn {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f8f9fa;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.3s;\r\n    margin-left: 15px;\r\n  }\r\n\r\n  .search-btn:hover {\r\n    background-color: #e9ecef;\r\n  }\r\n\r\n  .search-btn i {\r\n    color: #666;\r\n    font-size: 18px;\r\n  }\r\n\r\n  /* 搜索对话框 */\r\n  .search-dialog :deep(.el-dialog__header) {\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__body) {\r\n    padding: 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__footer) {\r\n    border-top: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n  @media (max-width: 992px) {\r\n    .mobile-toggle {\r\n      display: flex;\r\n    }\r\n\r\n    .nav-menu {\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100vh;\r\n      background-color: rgba(255, 255, 255, 0.95);\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      align-items: center;\r\n      transform: translateX(-100%);\r\n      transition: transform 0.3s ease;\r\n      z-index: 100;\r\n    }\r\n\r\n    .nav-menu.active {\r\n      transform: translateX(0);\r\n    }\r\n\r\n    .nav-list {\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n\r\n    .nav-item {\r\n      margin: 10px 0;\r\n    }\r\n\r\n    .nav-link {\r\n      font-size: 18px;\r\n      padding: 10px 20px;\r\n    }\r\n\r\n    .nav-item.active::after {\r\n      display: none;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .top-bar-content {\r\n      flex-direction: column;\r\n    }\r\n\r\n    .contact-info {\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .logo img {\r\n      height: 40px;\r\n    }\r\n  }\r\n</style>"]}]}