{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue", "mtime": 1741536460000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gJy4uLy4uLy4uL3V0aWxzL2h0dHAnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1Bvc2l0aW9uc0xpc3QnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBwYWdlOiB7CiAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgLy8g5b2T5YmN6aG1CiAgICAgICAgcGFnZVNpemU6IDgsCiAgICAgICAgLy8g5q+P6aG15pi+56S65p2h55uu5Liq5pWwCiAgICAgICAgdG90YWxDb3VudDogMCAvLyDmgLvmnaHnm67mlbAKICAgICAgfSwKICAgICAgcG9saXN0OiBbXSwKICAgICAgY2F0aWQ6ICcnLAogICAgICBrZXl3b3JkOiAnJwogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmNhdGlkID0gdGhpcy4kcm91dGUucXVlcnkuY2F0aWQgPT0gbnVsbCA/ICcnIDogdGhpcy4kcm91dGUucXVlcnkuY2F0aWQ7CiAgICB0aGlzLmtleXdvcmQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5rZXl3b3JkID09IG51bGwgPyAnJyA6IHRoaXMuJHJvdXRlLnF1ZXJ5LmtleXdvcmQ7CiAgICB0aGlzLmdldERhdGFzKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliIbpobUKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMucGFnZS5jdXJyZW50UGFnZSA9IHZhbDsKICAgICAgdGhpcy5nZXREYXRhcygpOwogICAgfSwKICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICBnZXREYXRhcygpIHsKICAgICAgbGV0IHBhcmEgPSB7CiAgICAgICAgcGZsYWc6ICflvIDmlL4nLAogICAgICAgIC8vIOWPquaYvuekuuW8gOaUvueKtuaAgeeahOiBjOS9jQogICAgICAgIHBmbGFnMjogJ+WuoeaguOmAmui/hycsCiAgICAgICAgLy8g5Y+q5pi+56S65a6h5qC46YCa6L+H55qE6IGM5L2NCiAgICAgICAgY2F0aWQ6IHRoaXMuY2F0aWQsCiAgICAgICAgY29uZGl0aW9uOiAnIGFuZCBwbmFtZSBsaWtlICIlJyArIHRoaXMua2V5d29yZCArICclIiAnCiAgICAgIH07CiAgICAgIGxldCB1cmwgPSBiYXNlICsgJy9wb3NpdGlvbnMvbGlzdD9jdXJyZW50UGFnZT0nICsgdGhpcy5wYWdlLmN1cnJlbnRQYWdlICsgJyZwYWdlU2l6ZT0nICsgdGhpcy5wYWdlLnBhZ2VTaXplOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5wb2xpc3QgPSByZXMucmVzZGF0YTsKICAgICAgICB0aGlzLnBhZ2UudG90YWxDb3VudCA9IHJlcy5jb3VudDsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["request", "base", "name", "data", "page", "currentPage", "pageSize", "totalCount", "polist", "catid", "keyword", "created", "$route", "query", "getDatas", "methods", "handleCurrentChange", "val", "para", "pflag", "pflag2", "condition", "url", "post", "then", "res", "resdata", "count"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"positions-list\">\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-6 col-md-6 wow fadeIn\" data-wow-delay=\"300ms\" v-for=\"item in polist\" :key=\"item.pid\">\r\n        <div class=\"icon-box icon-box--border\">\r\n          <div class=\"icon-box__heading\">\r\n            <h3 class=\"heading-title\">{{ item.pname }}</h3>\r\n          </div>\r\n          <div class=\"icon-box__content\">\r\n            <div class=\"job-info\">\r\n              <p><i class=\"el-icon-location\"></i> 工作地点：{{ item.wlocation }}</p>\r\n              <p><i class=\"el-icon-user\"></i> 招聘人数：{{ item.rnumber }}</p>\r\n              <p>\r\n                <i class=\"el-icon-money\"></i> 薪资待遇：<span class=\"salary\">{{\r\n                  item.streatment\r\n                }}</span>\r\n              </p>\r\n              <p><i class=\"el-icon-time\"></i> 发布时间：{{ item.ptime }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon-box__btn\">\r\n            <a :href=\"'positionsView?id=' + item.pid\">\r\n              查看详情<span><i class=\"fa fa-chevron-right\"></i></span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" class=\"pagination\">\r\n    </el-pagination>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from '../../../utils/http';\r\nexport default {\r\n  name: 'PositionsList',\r\n  data() {\r\n    return {\r\n      page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 8, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n      },\r\n      polist: [],\r\n      catid: '',\r\n      keyword: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.catid = this.$route.query.catid == null ? '' : this.$route.query.catid;\r\n    this.keyword = this.$route.query.keyword == null ? '' : this.$route.query.keyword;\r\n    this.getDatas();\r\n  },\r\n  methods: {\r\n    // 分页\r\n    handleCurrentChange(val) {\r\n      this.page.currentPage = val;\r\n      this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {\r\n        pflag: '开放', // 只显示开放状态的职位\r\n        pflag2: '审核通过', // 只显示审核通过的职位\r\n        catid: this.catid,\r\n        condition: ' and pname like \"%' + this.keyword + '%\" ',\r\n      };\r\n      let url =\r\n        base +\r\n        '/positions/list?currentPage=' +\r\n        this.page.currentPage +\r\n        '&pageSize=' +\r\n        this.page.pageSize;\r\n      request.post(url, para).then((res) => {\r\n        this.polist = res.resdata;\r\n        this.page.totalCount = res.count;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.positions-list {\r\n  padding: 20px;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -15px;\r\n}\r\n\r\n.col-lg-6 {\r\n  padding: 15px;\r\n  width: 50%;\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .col-lg-6 {\r\n    width: 50%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .col-lg-6 {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.job-info {\r\n  text-align: left;\r\n  padding: 10px 0;\r\n}\r\n\r\n.job-info p {\r\n  margin: 5px 0;\r\n  color: #666;\r\n}\r\n\r\n.job-info i {\r\n  margin-right: 5px;\r\n  color: #3bc0c3;\r\n}\r\n\r\n.icon-box {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  height: 100%;\r\n}\r\n\r\n.icon-box:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.heading-title {\r\n  color: #333;\r\n  font-size: 18px;\r\n  margin-bottom: 15px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.icon-box__btn {\r\n  margin-top: 15px;\r\n  text-align: right;\r\n}\r\n\r\n.icon-box__btn a {\r\n  color: #3bc0c3;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.icon-box__btn a:hover {\r\n  color: #2a8f91;\r\n}\r\n\r\n.salary {\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n}\r\n\r\n.pagination {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n</style>\r\n"], "mappings": "AAoCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,CAAC;QAAE;QACbC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACF,KAAI,GAAI,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,KAAI,IAAK,IAAG,GAAI,EAAC,GAAI,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,KAAK;IAC3E,IAAI,CAACC,OAAM,GAAI,IAAI,CAACE,MAAM,CAACC,KAAK,CAACH,OAAM,IAAK,IAAG,GAAI,EAAC,GAAI,IAAI,CAACE,MAAM,CAACC,KAAK,CAACH,OAAO;IACjF,IAAI,CAACI,QAAQ,CAAC,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE;IACP;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAACb,IAAI,CAACC,WAAU,GAAIY,GAAG;MAC3B,IAAI,CAACH,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAII,IAAG,GAAI;QACTC,KAAK,EAAE,IAAI;QAAE;QACbC,MAAM,EAAE,MAAM;QAAE;QAChBX,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBY,SAAS,EAAE,oBAAmB,GAAI,IAAI,CAACX,OAAM,GAAI;MACnD,CAAC;MACD,IAAIY,GAAE,GACJrB,IAAG,GACH,8BAA6B,GAC7B,IAAI,CAACG,IAAI,CAACC,WAAU,GACpB,YAAW,GACX,IAAI,CAACD,IAAI,CAACE,QAAQ;MACpBN,OAAO,CAACuB,IAAI,CAACD,GAAG,EAAEJ,IAAI,CAAC,CAACM,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACjB,MAAK,GAAIiB,GAAG,CAACC,OAAO;QACzB,IAAI,CAACtB,IAAI,CAACG,UAAS,GAAIkB,GAAG,CAACE,KAAK;MAClC,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}