{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Browsingrecords_Manage.vue?vue&type=template&id=5a3e2f7e", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Browsingrecords_Manage.vue", "mtime": 1741608933000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGVsLXRhYmxlCiAgICA6ZGF0YT0iZGF0YWxpc3QiCiAgICBib3JkZXIKICAgIHN0cmlwZQogICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgdi1sb2FkaW5nPSJsaXN0TG9hZGluZyIKICAgIGhpZ2hsaWdodC1jdXJyZW50LXJvdwogICAgbWF4LWhlaWdodD0iNjAwIgogICAgc2l6ZT0ic21hbGwiCiAgPgogICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJieTEiIGxhYmVsPSLogYzkvY3lkI3np7AiIGFsaWduPSJjZW50ZXIiPjwvZWwtdGFibGUtY29sdW1uPgogICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJicm93c2V0aW1lIiBsYWJlbD0i5rWP6KeI5pe26Ze0IiBhbGlnbj0iY2VudGVyIj48L2VsLXRhYmxlLWNvbHVtbj4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuaTjeS9nCIgbWluLXdpZHRoPSIyMDAiIGFsaWduPSJjZW50ZXIiPgogICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9InNjb3BlIj4KICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICAgIEBjbGljaz0iaGFuZGxlU2hvdyhzY29wZS4kaW5kZXgsIHNjb3BlLnJvdykiCiAgICAgICAgICBpY29uPSJlbC1pY29uLXZpZXciCiAgICAgICAgICBzdHlsZT0icGFkZGluZzogM3B4IDZweCAzcHggNnB4IgogICAgICAgICAgPuivpuaDhTwvZWwtYnV0dG9uCiAgICAgICAgPgogICAgICAgIDxlbC1idXR0b24KICAgICAgICAgIHR5cGU9ImRhbmdlciIKICAgICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgICBAY2xpY2s9ImhhbmRsZURlbGV0ZShzY29wZS4kaW5kZXgsIHNjb3BlLnJvdykiCiAgICAgICAgICBpY29uPSJlbC1pY29uLWRlbGV0ZSIKICAgICAgICAgIHN0eWxlPSJwYWRkaW5nOiAzcHggNnB4IDNweCA2cHgiCiAgICAgICAgICA+5Yig6ZmkPC9lbC1idXR0b24KICAgICAgICA+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICA8L2VsLXRhYmxlPgogIDxlbC1wYWdpbmF0aW9uCiAgICBAY3VycmVudC1jaGFuZ2U9ImhhbmRsZUN1cnJlbnRDaGFuZ2UiCiAgICA6Y3VycmVudC1wYWdlPSJwYWdlLmN1cnJlbnRQYWdlIgogICAgOnBhZ2Utc2l6ZT0icGFnZS5wYWdlU2l6ZSIKICAgIGJhY2tncm91bmQKICAgIGxheW91dD0idG90YWwsIHByZXYsIHBhZ2VyLCBuZXh0LCBqdW1wZXIiCiAgICA6dG90YWw9InBhZ2UudG90YWxDb3VudCIKICAgIHN0eWxlPSJmbG9hdDogcmlnaHQ7IG1hcmdpbjogMTBweCAyMHB4IDAgMCIKICA+PC9lbC1wYWdpbmF0aW9uPgo="}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Browsingrecords_Manage.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Browsingrecords_Manage.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <el-table\n    :data=\"datalist\"\n    border\n    stripe\n    style=\"width: 100%\"\n    v-loading=\"listLoading\"\n    highlight-current-row\n    max-height=\"600\"\n    size=\"small\"\n  >\n    <el-table-column prop=\"by1\" label=\"职位名称\" align=\"center\"></el-table-column>\n    <el-table-column prop=\"browsetime\" label=\"浏览时间\" align=\"center\"></el-table-column>\n    <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n      <template #default=\"scope\">\n        <el-button\n          type=\"primary\"\n          size=\"mini\"\n          @click=\"handleShow(scope.$index, scope.row)\"\n          icon=\"el-icon-view\"\n          style=\"padding: 3px 6px 3px 6px\"\n          >详情</el-button\n        >\n        <el-button\n          type=\"danger\"\n          size=\"mini\"\n          @click=\"handleDelete(scope.$index, scope.row)\"\n          icon=\"el-icon-delete\"\n          style=\"padding: 3px 6px 3px 6px\"\n          >删除</el-button\n        >\n      </template>\n    </el-table-column>\n  </el-table>\n  <el-pagination\n    @current-change=\"handleCurrentChange\"\n    :current-page=\"page.currentPage\"\n    :page-size=\"page.pageSize\"\n    background\n    layout=\"total, prev, pager, next, jumper\"\n    :total=\"page.totalCount\"\n    style=\"float: right; margin: 10px 20px 0 0\"\n  ></el-pagination>\n</template>\n<script>\nimport request, { base } from '../../../utils/http';\nexport default {\n  name: 'browsingrecords',\n  components: {},\n  data() {\n    return {\n      page: {\n        currentPage: 1, // 当前页\n        pageSize: 10, // 每页显示条目个数\n        totalCount: 0, // 总条目数\n      },\n      isClear: false,\n\n      listLoading: false, //列表加载状态\n      btnLoading: false, //保存按钮加载状态\n      datalist: [], //表格数据\n    };\n  },\n  created() {\n    this.getDatas();\n  },\n\n  methods: {\n    // 删除浏览记录\n    handleDelete(index, row) {\n      this.$confirm('确认删除该记录吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      })\n        .then(() => {\n          this.listLoading = true;\n          let url = base + '/browsingrecords/del?id=' + row.brid;\n          request.post(url).then((res) => {\n            this.listLoading = false;\n\n            this.$message({\n              message: '删除成功',\n              type: 'success',\n              offset: 320,\n            });\n            this.getDatas();\n          });\n        })\n        .catch(() => {});\n    },\n\n    // 分页\n    handleCurrentChange(val) {\n      this.page.currentPage = val;\n      this.getDatas();\n    },\n\n    //获取列表数据\n    getDatas() {\n      let lname = sessionStorage.getItem('lname');\n      let para = {\n        sno: lname,\n      };\n      this.listLoading = true;\n      let url =\n        base +\n        '/browsingrecords/list?currentPage=' +\n        this.page.currentPage +\n        '&pageSize=' +\n        this.page.pageSize;\n      request.post(url, para).then((res) => {\n        if (res.resdata.length > 0) {\n          this.isPage = true;\n        } else {\n          this.isPage = false;\n        }\n        this.page.totalCount = res.count;\n        this.datalist = res.resdata;\n        this.listLoading = false;\n      });\n    },\n\n    // 查看\n    handleShow(index, row) {\n      this.$router.push({\n        path: '/PositionsView',\n        query: {\n          id: row.pid,\n        },\n      });\n    },\n\n    // 编辑\n    handleEdit(index, row) {\n      this.$router.push({\n        path: '/Browsingrecords_Edit',\n        query: {\n          id: row.brid,\n        },\n      });\n    },\n  },\n};\n</script>\n\n<style></style>\n"]}]}