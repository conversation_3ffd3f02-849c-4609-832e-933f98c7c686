{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue?vue&type=template&id=7a4880f2&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue", "mtime": 1741617073822}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "src", "$data", "formData", "spic", "alt", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "lname", "_hoisted_9", "time"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sweclome.vue"], "sourcesContent": ["<template>\r\n  <div class=\"welcome-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <div class=\"banner-content\">\r\n        <div class=\"user-avatar\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" alt=\"用户头像\" />\r\n        </div>\r\n        <div class=\"user-info\">\r\n          <h2 class=\"welcome-text\">欢迎回来，<span class=\"user-name\">{{ lname }}</span></h2>\r\n          <p class=\"login-time\" style=\"color: white;\">登录时间：{{ time }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n\r\n\r\n    <!-- 快捷操作 -->\r\n    <div class=\"quick-actions\">\r\n      <div class=\"section-title\">\r\n        <h3>快捷操作</h3>\r\n      </div>\r\n\r\n      <div class=\"row\">\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-plus-circle\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">创建简历</h4>\r\n            <p class=\"action-desc\">创建一份专业的简历，展示你的技能和经验</p>\r\n            <a href=\"/resume_Add\" class=\"action-link\">立即创建</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-search\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">搜索职位</h4>\r\n            <p class=\"action-desc\">浏览最新的职位信息，寻找适合你的工作机会</p>\r\n            <a href=\"/positionsList\" class=\"action-link\">开始搜索</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-4\">\r\n          <div class=\"action-card\">\r\n            <div class=\"action-icon\">\r\n              <i class=\"fas fa-robot\"></i>\r\n            </div>\r\n            <h4 class=\"action-title\">AI顾问</h4>\r\n            <p class=\"action-desc\">获取个性化的职业建议和简历优化指导</p>\r\n            <a href=\"/ai\" class=\"action-link\">咨询顾问</a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n  export default {\r\n    name: 'Sweclome',\r\n    data() {\r\n      return {\r\n        lname: '',\r\n        formData: {}, //表单数据\r\n        time: '', //当前时间\r\n        resumeCount: 0,\r\n        deliveryCount: 0,\r\n        browseCount: 0,\r\n        favoriteCount: 0\r\n      };\r\n    },\r\n    created() {\r\n      this.lname = sessionStorage.getItem('lname');\r\n      this.getDatas();\r\n      this.time = new Date().toLocaleString();\r\n    },\r\n    methods: {\r\n      //获取个人信息数据\r\n      getDatas() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + '/students/get?id=' + this.lname;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n  \r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 欢迎页面容器 */\r\n  .welcome-container {\r\n    padding: 20px;\r\n  }\r\n\r\n  /* 欢迎横幅 */\r\n  .welcome-banner {\r\n    background: linear-gradient(135deg, #3498db, #2c3e50);\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    margin-bottom: 30px;\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .banner-content {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .user-avatar {\r\n    margin-right: 30px;\r\n  }\r\n\r\n  .user-avatar img {\r\n    width: 100px;\r\n    height: 100px;\r\n    border-radius: 50%;\r\n    border: 4px solid rgba(255, 255, 255, 0.3);\r\n    object-fit: cover;\r\n  }\r\n\r\n  .user-info {\r\n    color: #fff;\r\n  }\r\n\r\n  .welcome-text {\r\n    font-size: 24px;\r\n    margin-bottom: 10px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .user-name {\r\n    color: #f1c40f;\r\n    font-weight: 700;\r\n  }\r\n\r\n  .login-time {\r\n    font-size: 14px;\r\n    opacity: 0.8;\r\n    margin: 0;\r\n  }\r\n\r\n  /* 统计卡片 */\r\n  .stats-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .stats-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .stats-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .stats-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 10px;\r\n    background-color: rgba(231, 76, 60, 0.1);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 15px;\r\n  }\r\n\r\n  .stats-icon i {\r\n    font-size: 24px;\r\n    color: #e74c3c;\r\n  }\r\n\r\n  .stats-icon.blue {\r\n    background-color: rgba(52, 152, 219, 0.1);\r\n  }\r\n\r\n  .stats-icon.blue i {\r\n    color: #3498db;\r\n  }\r\n\r\n  .stats-icon.green {\r\n    background-color: rgba(46, 204, 113, 0.1);\r\n  }\r\n\r\n  .stats-icon.green i {\r\n    color: #2ecc71;\r\n  }\r\n\r\n  .stats-icon.purple {\r\n    background-color: rgba(155, 89, 182, 0.1);\r\n  }\r\n\r\n  .stats-icon.purple i {\r\n    color: #9b59b6;\r\n  }\r\n\r\n  .stats-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .stats-title {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin: 0 0 5px;\r\n  }\r\n\r\n  .stats-value {\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    color: #2c3e50;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .stats-action a {\r\n    color: #3498db;\r\n    font-size: 13px;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .stats-action a:hover {\r\n    color: #2980b9;\r\n  }\r\n\r\n  .stats-action i {\r\n    font-size: 10px;\r\n    margin-left: 3px;\r\n  }\r\n\r\n  /* 个人信息卡片 */\r\n  .profile-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .section-title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .section-title h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0;\r\n    position: relative;\r\n    padding-left: 15px;\r\n  }\r\n\r\n  .section-title h3::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: 0;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 5px;\r\n    height: 20px;\r\n    background-color: #3498db;\r\n    border-radius: 2.5px;\r\n  }\r\n\r\n  .edit-link {\r\n    color: #3498db;\r\n    font-size: 14px;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .edit-link:hover {\r\n    color: #2980b9;\r\n  }\r\n\r\n  .profile-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 25px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  }\r\n\r\n  .info-group {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-label {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .info-value {\r\n    font-size: 16px;\r\n    color: #2c3e50;\r\n    font-weight: 500;\r\n  }\r\n\r\n  /* 快捷操作 */\r\n  .quick-actions {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .action-card {\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    padding: 25px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n    height: 100%;\r\n  }\r\n\r\n  .action-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .action-icon {\r\n    width: 70px;\r\n    height: 70px;\r\n    border-radius: 50%;\r\n    background-color: rgba(52, 152, 219, 0.1);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin: 0 auto 20px;\r\n  }\r\n\r\n  .action-icon i {\r\n    font-size: 30px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .action-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .action-desc {\r\n    font-size: 14px;\r\n    color: #7f8c8d;\r\n    margin-bottom: 20px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  .action-link {\r\n    display: inline-block;\r\n    padding: 8px 20px;\r\n    background-color: #3498db;\r\n    color: #fff;\r\n    border-radius: 5px;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .action-link:hover {\r\n    background-color: #2980b9;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n  @media (max-width: 768px) {\r\n    .banner-content {\r\n      flex-direction: column;\r\n      text-align: center;\r\n    }\r\n\r\n    .user-avatar {\r\n      margin-right: 0;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .welcome-text {\r\n      font-size: 20px;\r\n    }\r\n  }\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;;EAGnBA,KAAK,EAAC;AAAW;;EAChBA,KAAK,EAAC;AAAc;;EAAYA,KAAK,EAAC;AAAW;;EAClDA,KAAK,EAAC,YAAY;EAACC,KAAqB,EAArB;IAAA;EAAA;;;uBAT9BC,mBAAA,CA2DM,OA3DNC,UA2DM,GA1DJC,mBAAA,UAAa,EACbC,mBAAA,CAUM,OAVNC,UAUM,GATJD,mBAAA,CAQM,OARNE,UAQM,GAPJF,mBAAA,CAEM,OAFNG,UAEM,GADJH,mBAAA,CAAmF;IAA7EI,GAAG,8CAA8CC,KAAA,CAAAC,QAAQ,CAACC,IAAI;IAAEC,GAAG,EAAC;yCAE5ER,mBAAA,CAGM,OAHNS,UAGM,GAFJT,mBAAA,CAA6E,MAA7EU,UAA6E,G,2CAApD,OAAK,IAAAV,mBAAA,CAA0C,QAA1CW,UAA0C,EAAAC,gBAAA,CAAfP,KAAA,CAAAQ,KAAK,iB,GAC9Db,mBAAA,CAA+D,KAA/Dc,UAA+D,EAAnB,OAAK,GAAAF,gBAAA,CAAGP,KAAA,CAAAU,IAAI,iB,OAS9DhB,mBAAA,UAAa,E", "ignoreList": []}]}