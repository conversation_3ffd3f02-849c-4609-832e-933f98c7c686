{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsManage.vue?vue&type=template&id=8920e880", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsManage.vue", "mtime": 1741536360000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_col", "span", "style", "_component_el_form", "inline", "model", "$data", "filters", "_component_el_form_item", "_component_el_input", "pname", "$event", "placeholder", "size", "label", "prop", "_component_el_select", "catid", "_component_el_option", "value", "_Fragment", "_renderList", "jobcategoriesList", "item", "_createBlock", "key", "catname", "_component_el_button", "type", "onClick", "$options", "query", "icon", "_cache", "_component_el_table", "data", "datalist", "border", "stripe", "_component_el_table_column", "align", "default", "_withCtx", "scope", "_createElementVNode", "_toDisplayString", "getCategoryName", "row", "handleShow", "$index", "handleEdit", "handleDelete", "listLoading", "_component_el_pagination", "onCurrentChange", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsManage.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">职位管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">职位列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">职位列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.pname\" placeholder=\"职位名称\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"职位分类\" prop=\"catid\">\n          <el-select v-model=\"filters.catid\" placeholder=\"请选择\" size=\"small\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option v-for=\"item in jobcategoriesList\" :key=\"item.catid\" :label=\"item.catname\" :value=\"item.catid\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"pname\" label=\"职位名称\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"catid\" label=\"职位分类\" align=\"center\">\n        <template #default=\"scope\">\n          <span>{{ getCategoryName(scope.row.catid) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"wlocation\" label=\"工作地点\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"rnumber\" label=\"招聘人数\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"streatment\" label=\"薪资待遇\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"pflag\" label=\"招聘状态\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"ptime\" label=\"发布时间\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"pflag2\" label=\"审核状态\" align=\"center\"></el-table-column>\n      <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\n          <el-button type=\"success\" size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\" icon=\"el-icon-edit\"\n            style=\"padding: 3px 6px 3px 6px\">编辑</el-button>\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n  </div>\n</template>\n<script>\nimport request, { base } from '../../../../utils/http';\nexport default {\n  name: 'positions',\n  components: {},\n  data() {\n    return {\n      filters: {\n        //列表查询参数\n        pname: '',\n        catid: '',\n      },\n      jobcategoriesList: [], // 职位分类列表\n      page: {\n        currentPage: 1, // 当前页\n        pageSize: 10, // 每页显示条目个数\n        totalCount: 0, // 总条目数\n      },\n      isClear: false,\n\n      listLoading: false, //列表加载状态\n      btnLoading: false, //保存按钮加载状态\n      datalist: [], //表格数据\n    };\n  },\n  created() {\n    this.getDatas();\n    this.getJobCategories();\n  },\n\n  methods: {\n    // 删除职位\n    handleDelete(index, row) {\n      this.$confirm('确认删除该记录吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      })\n        .then(() => {\n          this.listLoading = true;\n          let url = base + '/positions/del?id=' + row.pid;\n          request.post(url).then((res) => {\n            this.listLoading = false;\n\n            this.$message({\n              message: '删除成功',\n              type: 'success',\n              offset: 320,\n            });\n            this.getDatas();\n          });\n        })\n        .catch(() => { });\n    },\n\n    // 分页\n    handleCurrentChange(val) {\n      this.page.currentPage = val;\n      this.getDatas();\n    },\n\n    //获取列表数据\n    getDatas() {\n      var user = JSON.parse(sessionStorage.getItem('user'));\n      let para = {\n        pname: this.filters.pname,\n        catid: this.filters.catid,\n        cid: user.cid,\n      };\n      this.listLoading = true;\n      let url =\n        base +\n        '/positions/list?currentPage=' +\n        this.page.currentPage +\n        '&pageSize=' +\n        this.page.pageSize;\n      request.post(url, para).then((res) => {\n        if (res.resdata.length > 0) {\n          this.isPage = true;\n        } else {\n          this.isPage = false;\n        }\n        this.page.totalCount = res.count;\n        this.datalist = res.resdata;\n        this.listLoading = false;\n      });\n    },\n    //查询\n    query() {\n      this.getDatas();\n    },\n\n    // 查看\n    handleShow(index, row) {\n      this.$router.push({\n        path: '/PositionsDetail',\n        query: {\n          id: row.pid,\n        },\n      });\n    },\n\n    // 编辑\n    handleEdit(index, row) {\n      this.$router.push({\n        path: '/PositionsEdit',\n        query: {\n          id: row.pid,\n        },\n      });\n    },\n\n    // 获取职位分类列表\n    getJobCategories() {\n      let url = base + '/jobcategories/list';\n      request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\n        if (res.code == 200) {\n          this.jobcategoriesList = res.resdata;\n        }\n      });\n    },\n\n    // 获取职位分类名称\n    getCategoryName(catid) {\n      const category = this.jobcategoriesList.find((item) => item.catid === catid);\n      return category ? category.catname : '';\n    },\n  },\n};\n</script>\n<style scoped></style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAK;;;;;;;;;;;;;uBAAhBC,mBAAA,CA0DM,OA1DNC,UA0DM,G,sWA9CJC,YAAA,CAgBSC,iBAAA;IAhBAC,IAAI,EAAE,EAAE;IAAEC,KAA8C,EAA9C;MAAA;MAAA;IAAA;;sBACjB,MAcU,CAdVH,YAAA,CAcUI,kBAAA;MAdAC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,KAAA,CAAAC;;wBAC9B,MAEe,CAFfR,YAAA,CAEeS,uBAAA;0BADb,MAA6E,CAA7ET,YAAA,CAA6EU,mBAAA;sBAA1DH,KAAA,CAAAC,OAAO,CAACG,KAAK;qEAAbJ,KAAA,CAAAC,OAAO,CAACG,KAAK,GAAAC,MAAA;UAAEC,WAAW,EAAC,MAAM;UAACC,IAAI,EAAC;;;UAE5Dd,YAAA,CAMeS,uBAAA;QANDM,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAIY,CAJZhB,YAAA,CAIYiB,oBAAA;sBAJQV,KAAA,CAAAC,OAAO,CAACU,KAAK;qEAAbX,KAAA,CAAAC,OAAO,CAACU,KAAK,GAAAN,MAAA;UAAEC,WAAW,EAAC,KAAK;UAACC,IAAI,EAAC;;4BACxD,MAA2C,CAA3Cd,YAAA,CAA2CmB,oBAAA;YAAhCJ,KAAK,EAAC,IAAI;YAACK,KAAK,EAAC;iCAC5BtB,mBAAA,CACYuB,SAAA,QAAAC,WAAA,CADcf,KAAA,CAAAgB,iBAAiB,EAAzBC,IAAI;iCAAtBC,YAAA,CACYN,oBAAA;cADkCO,GAAG,EAAEF,IAAI,CAACN,KAAK;cAAGH,KAAK,EAAES,IAAI,CAACG,OAAO;cAAGP,KAAK,EAAEI,IAAI,CAACN;;;;;;UAItGlB,YAAA,CAEeS,uBAAA;0BADb,MAA0F,CAA1FT,YAAA,CAA0F4B,oBAAA;UAA/EC,IAAI,EAAC,SAAS;UAACf,IAAI,EAAC,OAAO;UAAEgB,OAAK,EAAEC,QAAA,CAAAC,KAAK;UAAEC,IAAI,EAAC;;4BAAiB,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;;qCAKpFT,YAAA,CAwBWU,mBAAA;IAxBAC,IAAI,EAAE7B,KAAA,CAAA8B,QAAQ;IAAEC,MAAM,EAAN,EAAM;IAACC,MAAM,EAAN,EAAM;IAACpC,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAAyB,uBAAqB,EAArB,EAAqB;IACxG,YAAU,EAAC,KAAK;IAACW,IAAI,EAAC;;sBACtB,MAA4E,CAA5Ed,YAAA,CAA4EwC,0BAAA;MAA3DxB,IAAI,EAAC,OAAO;MAACD,KAAK,EAAC,MAAM;MAAC0B,KAAK,EAAC;QACjDzC,YAAA,CAIkBwC,0BAAA;MAJDxB,IAAI,EAAC,OAAO;MAACD,KAAK,EAAC,MAAM;MAAC0B,KAAK,EAAC;;MACpCC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBC,mBAAA,CAAmD,cAAAC,gBAAA,CAA1Cf,QAAA,CAAAgB,eAAe,CAACH,KAAK,CAACI,GAAG,CAAC9B,KAAK,kB;;QAG5ClB,YAAA,CAAgFwC,0BAAA;MAA/DxB,IAAI,EAAC,WAAW;MAACD,KAAK,EAAC,MAAM;MAAC0B,KAAK,EAAC;QACrDzC,YAAA,CAA8EwC,0BAAA;MAA7DxB,IAAI,EAAC,SAAS;MAACD,KAAK,EAAC,MAAM;MAAC0B,KAAK,EAAC;QACnDzC,YAAA,CAAiFwC,0BAAA;MAAhExB,IAAI,EAAC,YAAY;MAACD,KAAK,EAAC,MAAM;MAAC0B,KAAK,EAAC;QACtDzC,YAAA,CAA4EwC,0BAAA;MAA3DxB,IAAI,EAAC,OAAO;MAACD,KAAK,EAAC,MAAM;MAAC0B,KAAK,EAAC;QACjDzC,YAAA,CAA4EwC,0BAAA;MAA3DxB,IAAI,EAAC,OAAO;MAACD,KAAK,EAAC,MAAM;MAAC0B,KAAK,EAAC;QACjDzC,YAAA,CAA6EwC,0BAAA;MAA5DxB,IAAI,EAAC,QAAQ;MAACD,KAAK,EAAC,MAAM;MAAC0B,KAAK,EAAC;QAClDzC,YAAA,CASkBwC,0BAAA;MATDzB,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC,KAAK;MAAC0B,KAAK,EAAC;;MACrCC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvB5C,YAAA,CACiD4B,oBAAA;QADtCC,IAAI,EAAC,SAAS;QAACf,IAAI,EAAC,MAAM;QAAEgB,OAAK,EAAAlB,MAAA,IAAEmB,QAAA,CAAAkB,UAAU,CAACL,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACI,GAAG;QAAGf,IAAI,EAAC,iBAAiB;QACvG9B,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAE+B,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;wDACrClC,YAAA,CACiD4B,oBAAA;QADtCC,IAAI,EAAC,SAAS;QAACf,IAAI,EAAC,MAAM;QAAEgB,OAAK,EAAAlB,MAAA,IAAEmB,QAAA,CAAAoB,UAAU,CAACP,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACI,GAAG;QAAGf,IAAI,EAAC,cAAc;QACpG9B,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAE+B,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;wDACrClC,YAAA,CACiD4B,oBAAA;QADtCC,IAAI,EAAC,QAAQ;QAACf,IAAI,EAAC,MAAM;QAAEgB,OAAK,EAAAlB,MAAA,IAAEmB,QAAA,CAAAqB,YAAY,CAACR,KAAK,CAACM,MAAM,EAAEN,KAAK,CAACI,GAAG;QAAGf,IAAI,EAAC,gBAAgB;QACvG9B,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAE+B,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;sDArB6B3B,KAAA,CAAA8C,WAAW,E,GAyBnFrD,YAAA,CAE8DsD,wBAAA;IAF9CC,eAAc,EAAExB,QAAA,CAAAyB,mBAAmB;IAAG,cAAY,EAAEjD,KAAA,CAAAkD,IAAI,CAACC,WAAW;IAAG,WAAS,EAAEnD,KAAA,CAAAkD,IAAI,CAACE,QAAQ;IAC7GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAEvD,KAAA,CAAAkD,IAAI,CAACM,UAAU;IAC5E5D,KAA2C,EAA3C;MAAA;MAAA;IAAA", "ignoreList": []}]}