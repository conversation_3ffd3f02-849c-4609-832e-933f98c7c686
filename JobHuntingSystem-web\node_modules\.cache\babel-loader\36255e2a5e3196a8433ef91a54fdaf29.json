{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryDetail.vue?vue&type=template&id=5a9e2db6", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryDetail.vue", "mtime": 1741614867102}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "align", "_component_el_form_item", "label", "delid", "by1", "sno", "by2", "submittime", "auditstatus", "auditreply", "_component_el_button", "type", "size", "onClick", "$options", "back", "icon", "_cache"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">简历投递管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">简历投递详情</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">简历投递详情</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n      <el-form-item label=\"投递\"> {{ formData.delid }}</el-form-item>\r\n      <el-form-item label=\"职位\"> {{ formData.by1 }}</el-form-item>\r\n      <el-form-item label=\"账号\"> {{ formData.sno }}</el-form-item>\r\n      <el-form-item label=\"简历\"> {{ formData.by2 }}</el-form-item>\r\n      <el-form-item label=\"投递时间\"> {{ formData.submittime }}</el-form-item>\r\n      <el-form-item label=\"审核状态\"> {{ formData.auditstatus }}</el-form-item>\r\n      <el-form-item label=\"审核回复\"> {{ formData.auditreply }}</el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport request, { base } from '../../../../utils/http';\r\nexport default {\r\n  name: 'ResumedeliveryDetail',\r\n  components: {},\r\n  data() {\r\n    return {\r\n      id: '',\r\n      formData: {}, //表单数据\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id; //获取参数\r\n    this.getDatas();\r\n  },\r\n\r\n  methods: {\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + '/resumedelivery/get?id=' + this.id;\r\n      request.post(url, para).then((res) => {\r\n        this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n\r\n    // 返回\r\n    back() {\r\n      //返回上一页\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped></style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAK;;;;;uBAAhBC,mBAAA,CAwBM,OAxBNC,UAwBM,G,4WAZJC,YAAA,CAWUC,kBAAA;IAXAC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,KAAK,EAAC;;sBACjD,MAA6D,CAA7DL,YAAA,CAA6DM,uBAAA;MAA/CC,KAAK,EAAC;IAAI;wBAAE,MAAoB,C,kCAAjBJ,KAAA,CAAAC,QAAQ,CAACI,KAAK,iB;;QAC3CR,YAAA,CAA2DM,uBAAA;MAA7CC,KAAK,EAAC;IAAI;wBAAE,MAAkB,C,kCAAfJ,KAAA,CAAAC,QAAQ,CAACK,GAAG,iB;;QACzCT,YAAA,CAA2DM,uBAAA;MAA7CC,KAAK,EAAC;IAAI;wBAAE,MAAkB,C,kCAAfJ,KAAA,CAAAC,QAAQ,CAACM,GAAG,iB;;QACzCV,YAAA,CAA2DM,uBAAA;MAA7CC,KAAK,EAAC;IAAI;wBAAE,MAAkB,C,kCAAfJ,KAAA,CAAAC,QAAQ,CAACO,GAAG,iB;;QACzCX,YAAA,CAAoEM,uBAAA;MAAtDC,KAAK,EAAC;IAAM;wBAAE,MAAyB,C,kCAAtBJ,KAAA,CAAAC,QAAQ,CAACQ,UAAU,iB;;QAClDZ,YAAA,CAAqEM,uBAAA;MAAvDC,KAAK,EAAC;IAAM;wBAAE,MAA0B,C,kCAAvBJ,KAAA,CAAAC,QAAQ,CAACS,WAAW,iB;;QACnDb,YAAA,CAAoEM,uBAAA;MAAtDC,KAAK,EAAC;IAAM;wBAAE,MAAyB,C,kCAAtBJ,KAAA,CAAAC,QAAQ,CAACU,UAAU,iB;;QAClDd,YAAA,CAEeM,uBAAA;wBADb,MAAqF,CAArFN,YAAA,CAAqFe,oBAAA;QAA1EC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC,IAAI;QAAEC,IAAI,EAAC;;0BAAe,MAAGC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E", "ignoreList": []}]}