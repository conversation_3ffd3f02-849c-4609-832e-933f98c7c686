{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyDetail.vue?vue&type=template&id=32817812", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyDetail.vue", "mtime": 1741615257361}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "align", "_component_el_form_item", "label", "cid", "clname", "password", "comname", "scale", "nature", "contact", "address", "prop", "_createElementVNode", "src", "logo", "style", "innerHTML", "introduction", "addtime", "_component_el_button", "type", "size", "onClick", "$options", "back", "icon", "_cache"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyDetail.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">企业详情</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">企业详情</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n      <el-form-item label=\"企业id\">\r\n        {{ formData.cid }}</el-form-item>\r\n      <el-form-item label=\"企业账号\">\r\n        {{ formData.clname }}</el-form-item>\r\n      <el-form-item label=\"登录密码\">\r\n        {{ formData.password }}</el-form-item>\r\n      <el-form-item label=\"企业名称\">\r\n        {{ formData.comname }}</el-form-item>\r\n      <el-form-item label=\"企业规模\">\r\n        {{ formData.scale }}</el-form-item>\r\n      <el-form-item label=\"企业性质\">\r\n        {{ formData.nature }}</el-form-item>\r\n      <el-form-item label=\"联系方式\">\r\n        {{ formData.contact }}</el-form-item>\r\n      <el-form-item label=\"联系地址\">\r\n        {{ formData.address }}</el-form-item>\r\n      <el-form-item label=\"企业logo\" prop=\"logo\">\r\n        <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.logo\" style=\"width: 150px;height: 150px\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"企业介绍\" prop=\"introduction\">\r\n        <div v-html=\"formData.introduction\"></div>\r\n      </el-form-item>\r\n      <el-form-item label=\"注册时间\">\r\n        {{ formData.addtime }}</el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\n\r\n  import request, { base } from \"../../../../utils/http\";\r\n  export default {\r\n    name: 'CompanyDetail',\r\n    components: {\r\n    },\r\n    data() {\r\n      return {\r\n        id: '',\r\n        formData: {}, //表单数据         \r\n\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id; //获取参数\r\n      this.getDatas();\r\n    },\r\n\r\n\r\n    methods: {\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/company/get?id=\" + this.id;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 返回\r\n      back() {\r\n        //返回上一页\r\n        this.$router.go(-1);\r\n      },\r\n\r\n    },\r\n  }\r\n\r\n</script>\r\n<style scoped></style>"], "mappings": ";;EAEOA,KAAK,EAAC;AAAK;;;;;;;uBAAhBC,mBAAA,CA2CM,OA3CNC,UA2CM,G,sWA/BJC,YAAA,CA4BUC,kBAAA;IA5BAC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,KAAK,EAAC;;sBACjD,MACmC,CADnCL,YAAA,CACmCM,uBAAA;MADrBC,KAAK,EAAC;IAAM;wBACxB,MAAkB,C,kCAAfJ,KAAA,CAAAC,QAAQ,CAACI,GAAG,iB;;QACjBR,YAAA,CACsCM,uBAAA;MADxBC,KAAK,EAAC;IAAM;wBACxB,MAAqB,C,kCAAlBJ,KAAA,CAAAC,QAAQ,CAACK,MAAM,iB;;QACpBT,YAAA,CACwCM,uBAAA;MAD1BC,KAAK,EAAC;IAAM;wBACxB,MAAuB,C,kCAApBJ,KAAA,CAAAC,QAAQ,CAACM,QAAQ,iB;;QACtBV,YAAA,CACuCM,uBAAA;MADzBC,KAAK,EAAC;IAAM;wBACxB,MAAsB,C,kCAAnBJ,KAAA,CAAAC,QAAQ,CAACO,OAAO,iB;;QACrBX,YAAA,CACqCM,uBAAA;MADvBC,KAAK,EAAC;IAAM;wBACxB,MAAoB,C,kCAAjBJ,KAAA,CAAAC,QAAQ,CAACQ,KAAK,iB;;QACnBZ,YAAA,CACsCM,uBAAA;MADxBC,KAAK,EAAC;IAAM;wBACxB,MAAqB,C,kCAAlBJ,KAAA,CAAAC,QAAQ,CAACS,MAAM,iB;;QACpBb,YAAA,CACuCM,uBAAA;MADzBC,KAAK,EAAC;IAAM;wBACxB,MAAsB,C,kCAAnBJ,KAAA,CAAAC,QAAQ,CAACU,OAAO,iB;;QACrBd,YAAA,CACuCM,uBAAA;MADzBC,KAAK,EAAC;IAAM;wBACxB,MAAsB,C,kCAAnBJ,KAAA,CAAAC,QAAQ,CAACW,OAAO,iB;;QACrBf,YAAA,CAEeM,uBAAA;MAFDC,KAAK,EAAC,QAAQ;MAACS,IAAI,EAAC;;wBAChC,MAA2G,CAA3GC,mBAAA,CAA2G;QAArGC,GAAG,8CAA8Cf,KAAA,CAAAC,QAAQ,CAACe,IAAI;QAAEC,KAAkC,EAAlC;UAAA;UAAA;QAAA;;;QAExEpB,YAAA,CAEeM,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACS,IAAI,EAAC;;wBAC9B,MAA0C,CAA1CC,mBAAA,CAA0C;QAArCI,SAA8B,EAAtBlB,KAAA,CAAAC,QAAQ,CAACkB;;;QAExBtB,YAAA,CACuCM,uBAAA;MADzBC,KAAK,EAAC;IAAM;wBACxB,MAAsB,C,kCAAnBJ,KAAA,CAAAC,QAAQ,CAACmB,OAAO,iB;;QACrBvB,YAAA,CAEeM,uBAAA;wBADb,MAAqF,CAArFN,YAAA,CAAqFwB,oBAAA;QAA1EC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC,IAAI;QAAEC,IAAI,EAAC;;0BAAe,MAAGC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E", "ignoreList": []}]}