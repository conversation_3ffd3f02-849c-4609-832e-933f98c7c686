{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryManage.vue?vue&type=style&index=0&id=2d0e08ca&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryManage.vue", "mtime": 1741615349398}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnJlc3VtZS1wcmV2aWV3IHsKICAgIG1heC13aWR0aDogODAwcHg7CiAgICBtYXJnaW46IDAgYXV0bzsKICB9CgogIC5yZXN1bWUtaGVhZGVyIHsKICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzYmMwYzMgMCUsICM0MmQzZDcgMTAwJSk7CiAgICBwYWRkaW5nOiA0MHB4OwogICAgYm9yZGVyLXJhZGl1czogOHB4IDhweCAwIDA7CiAgICBjb2xvcjogI2ZmZjsKICB9CgogIC5oZWFkZXItY29udGVudCB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgZ2FwOiAzMHB4OwogIH0KCiAgLmF2YXRhci1zZWN0aW9uIHsKICAgIHdpZHRoOiAxMjBweDsKICAgIGhlaWdodDogMTIwcHg7CiAgfQoKICAuYXZhdGFyIHsKICAgIHdpZHRoOiAxMDAlOwogICAgaGVpZ2h0OiAxMDAlOwogICAgYm9yZGVyLXJhZGl1czogNjBweDsKICAgIG9iamVjdC1maXQ6IGNvdmVyOwogICAgYm9yZGVyOiA0cHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpOwogIH0KCiAgLmJhc2ljLWluZm8gewogICAgZmxleDogMTsKICB9CgogIC5uYW1lIHsKICAgIGZvbnQtc2l6ZTogMjRweDsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgfQoKICAuaW5mby1ncmlkIHsKICAgIGRpc3BsYXk6IGdyaWQ7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpOwogICAgZ2FwOiAxNXB4OwogIH0KCiAgLmluZm8taXRlbSB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGdhcDogOHB4OwogIH0KCiAgLnJlc3VtZS1ib2R5IHsKICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICBwYWRkaW5nOiAzMHB4OwogICAgYm9yZGVyLXJhZGl1czogMCAwIDhweCA4cHg7CiAgfQoKICAuc2VjdGlvbi10aXRsZSB7CiAgICBmb250LXNpemU6IDE4cHg7CiAgICBjb2xvcjogIzMzMzsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGdhcDogOHB4OwogIH0KCiAgLmluZm8tcm93IHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgfQoKICAuaW5mby1yb3cgbGFiZWwgewogICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICBjb2xvcjogIzMzMzsKICAgIG1hcmdpbi1ib3R0b206IDhweDsKICAgIGRpc3BsYXk6IGJsb2NrOwogIH0KCiAgLmNvbnRlbnQgewogICAgY29sb3I6ICM2NjY7CiAgICBsaW5lLWhlaWdodDogMS44OwogIH0KCiAgLnN0YXR1cy1lbXBsb3llZCB7CiAgICBjb2xvcjogIzY3YzIzYTsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogIH0KCiAgLnN0YXR1cy11bmVtcGxveWVkIHsKICAgIGNvbG9yOiAjZjU2YzZjOwogICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgfQoKICAucmVzdW1lLWVtcHR5IHsKICAgIHBhZGRpbmc6IDQwcHggMDsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICB9Cg=="}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryManage.vue"], "names": [], "mappings": ";EAkSE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/resumedelivery/ResumedeliveryManage.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">简历投递管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">简历投递列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">简历投递列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.sno\" placeholder=\"账号\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"by1\" label=\"职位\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"sno\" label=\"账号\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"by2\" label=\"简历\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"submittime\" label=\"投递时间\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"auditstatus\" label=\"审核状态\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"auditreply\" label=\"审核回复\" align=\"center\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.auditreply != null\">{{\n            scope.row.auditreply.substring(0, 10)\n            }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\n          <el-button type=\"info\" size=\"mini\" @click=\"previewResume(scope.row.rid)\" icon=\"el-icon-document\"\n            style=\"padding: 3px 6px 3px 6px\">预览简历</el-button>\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n    <!-- 简历预览对话框 -->\n    <el-dialog title=\"简历预览\" v-model=\"resumeDialogVisible\" width=\"800px\">\n      <div v-if=\"resumeData\" class=\"resume-preview\">\n        <div class=\"resume-header\">\n          <div class=\"header-content\">\n            <div class=\"avatar-section\">\n              <img :src=\"'http://localhost:8088/JobHuntingSystem/' + studentInfo.spic\" class=\"avatar\" />\n            </div>\n            <div class=\"basic-info\">\n              <h1 class=\"name\">{{ studentInfo.sname }}</h1>\n              <div class=\"info-grid\">\n                <div class=\"info-item\">\n                  <i class=\"el-icon-user\"></i>\n                  <span>性别：{{ studentInfo.gender }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-date\"></i>\n                  <span>年龄：{{ studentInfo.age }}岁</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-phone\"></i>\n                  <span>电话：{{ studentInfo.phone }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-school\"></i>\n                  <span>专业：{{ professionalName }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-s-flag\"></i>\n                  <span>就业状态：\n                    <span :class=\"{\n                      'status-employed': studentInfo.sflag === '已就业',\n                      'status-unemployed': studentInfo.sflag === '未就业',\n                    }\">\n                      {{ studentInfo.sflag }}\n                    </span>\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"resume-body\">\n          <div class=\"resume-section\">\n            <h2 class=\"section-title\">\n              <i class=\"el-icon-document\"></i>\n              简历信息\n            </h2>\n            <div class=\"resume-content\">\n              <div class=\"info-row\">\n                <label>教育背景：</label>\n                <div class=\"content\" v-html=\"resumeData.education\"></div>\n              </div>\n              <div class=\"info-row\">\n                <label>实习经历：</label>\n                <div class=\"content\" v-html=\"resumeData.parttimejob\"></div>\n              </div>\n              <div class=\"info-row\">\n                <label>个人介绍：</label>\n                <div class=\"content\" v-html=\"resumeData.introduction\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div v-else class=\"resume-empty\">\n        <el-empty description=\"暂无简历信息\"></el-empty>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\n  import request, { base } from '../../../../utils/http';\n  export default {\n    name: 'resumedelivery',\n    components: {},\n    data() {\n      return {\n        filters: {\n          //列表查询参数\n          sno: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n        resumeDialogVisible: false,\n        resumeData: null,\n        studentInfo: {},\n        professionalsList: [],\n        professionalName: '',\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n    methods: {\n      // 删除简历投递\n      handleDelete(index, row) {\n        this.$confirm('确认删除该记录吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            this.listLoading = true;\n            let url = base + '/resumedelivery/del?id=' + row.delid;\n            request.post(url).then((res) => {\n              this.listLoading = false;\n\n              this.$message({\n                message: '删除成功',\n                type: 'success',\n                offset: 320,\n              });\n              this.getDatas();\n            });\n          })\n          .catch(() => { });\n      },\n\n      // 分页\n      handleCurrentChange(val) {\n        this.page.currentPage = val;\n        this.getDatas();\n      },\n\n      //获取列表数据\n      getDatas() {\n        let para = {\n          sno: this.filters.sno,\n        };\n        this.listLoading = true;\n        let url =\n          base +\n          '/resumedelivery/list?currentPage=' +\n          this.page.currentPage +\n          '&pageSize=' +\n          this.page.pageSize;\n        request.post(url, para).then((res) => {\n          if (res.resdata.length > 0) {\n            this.isPage = true;\n          } else {\n            this.isPage = false;\n          }\n          this.page.totalCount = res.count;\n          this.datalist = res.resdata;\n          this.listLoading = false;\n        });\n      },\n      //查询\n      query() {\n        this.getDatas();\n      },\n\n      // 查看\n      handleShow(index, row) {\n        this.$router.push({\n          path: '/ResumedeliveryDetail',\n          query: {\n            id: row.delid,\n          },\n        });\n      },\n\n      // 编辑\n      handleEdit(index, row) {\n        this.$router.push({\n          path: '/ResumedeliveryEdit',\n          query: {\n            id: row.delid,\n          },\n        });\n      },\n\n      // 预览简历\n      async previewResume(rid) {\n        try {\n          // 获取简历信息\n          const resumeRes = await request.post(base + '/resume/get?id=' + rid);\n          if (resumeRes.code === 200) {\n            this.resumeData = resumeRes.resdata;\n            // 获取求职者信息\n            const studentRes = await request.post(base + '/students/get?id=' + this.resumeData.sno);\n            if (studentRes.code === 200) {\n              this.studentInfo = studentRes.resdata;\n              // 获取专业信息\n              await this.getProfessionals();\n            }\n            this.resumeDialogVisible = true;\n          }\n        } catch (error) {\n          console.error('获取简历信息失败:', error);\n          this.$message({\n            message: '获取简历信息失败',\n            type: 'error',\n            offset: 320,\n          });\n        }\n      },\n\n      // 获取专业列表\n      async getProfessionals() {\n        try {\n          const res = await request.post(\n            base + '/professionals/list',\n            {},\n            { params: { currentPage: 1, pageSize: 100 } }\n          );\n          if (res.code === 200) {\n            this.professionalsList = res.resdata;\n            this.getProfessionalName();\n          }\n        } catch (error) {\n          console.error('获取专业列表失败:', error);\n        }\n      },\n\n      // 获取专业名称\n      getProfessionalName() {\n        if (this.studentInfo.proid && this.professionalsList.length > 0) {\n          const professional = this.professionalsList.find((p) => p.proid === this.studentInfo.proid);\n          this.professionalName = professional ? professional.proname : '';\n        }\n      },\n    },\n  };\n</script>\n<style scoped>\n  .resume-preview {\n    max-width: 800px;\n    margin: 0 auto;\n  }\n\n  .resume-header {\n    background: linear-gradient(135deg, #3bc0c3 0%, #42d3d7 100%);\n    padding: 40px;\n    border-radius: 8px 8px 0 0;\n    color: #fff;\n  }\n\n  .header-content {\n    display: flex;\n    gap: 30px;\n  }\n\n  .avatar-section {\n    width: 120px;\n    height: 120px;\n  }\n\n  .avatar {\n    width: 100%;\n    height: 100%;\n    border-radius: 60px;\n    object-fit: cover;\n    border: 4px solid rgba(255, 255, 255, 0.3);\n  }\n\n  .basic-info {\n    flex: 1;\n  }\n\n  .name {\n    font-size: 24px;\n    margin-bottom: 20px;\n  }\n\n  .info-grid {\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: 15px;\n  }\n\n  .info-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n\n  .resume-body {\n    background: #fff;\n    padding: 30px;\n    border-radius: 0 0 8px 8px;\n  }\n\n  .section-title {\n    font-size: 18px;\n    color: #333;\n    margin-bottom: 20px;\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n\n  .info-row {\n    margin-bottom: 20px;\n  }\n\n  .info-row label {\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 8px;\n    display: block;\n  }\n\n  .content {\n    color: #666;\n    line-height: 1.8;\n  }\n\n  .status-employed {\n    color: #67c23a;\n    font-weight: bold;\n  }\n\n  .status-unemployed {\n    color: #f56c6c;\n    font-weight: bold;\n  }\n\n  .resume-empty {\n    padding: 40px 0;\n    text-align: center;\n  }\n</style>"]}]}