{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsAdd.vue", "mtime": 1741615869026}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "sno", "required", "message", "trigger", "password", "sname", "gender", "phone", "pattern", "proid", "spic", "mounted", "getprofessionalsList", "methods", "save", "$refs", "validate", "valid", "url", "post", "then", "res", "code", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack", "para", "listLoading", "professionalsList", "resdata", "showUpload", "hideUpload", "handleRemove", "file", "fileList", "handlePreview", "console", "log", "handleExceed", "files", "duration", "fileListChange", "extendFileName", "extendFileNames", "split", "regExpRules", "i", "length", "RegExp", "fileNames", "that", "for<PERSON>ach", "key", "val", "ret", "test", "indexOf", "multiFiles", "handleConfirm", "filePath", "FormData", "append", "raw", "furl"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsAdd.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">求职者管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">添加求职者</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">添加求职者</h4>\n      </div>\n    </div>\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n      <el-form-item label=\"账号\" prop=\"sno\">\n        <el-input v-model=\"formData.sno\" placeholder=\"账号\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"密码\" prop=\"password\">\n        <el-input v-model=\"formData.password\" placeholder=\"密码\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"sname\">\n        <el-input v-model=\"formData.sname\" placeholder=\"姓名\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"formData.gender\">\n          <el-radio label=\"男\"> 男 </el-radio>\n          <el-radio label=\"女\"> 女 </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"年龄\" prop=\"age\">\n        <el-input v-model=\"formData.age\" placeholder=\"年龄\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"手机号码\" prop=\"phone\">\n        <el-input v-model=\"formData.phone\" placeholder=\"手机号码\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"专业\" prop=\"proid\">\n        <el-select v-model=\"formData.proid\" placeholder=\"请选择\" size=\"small\">\n          <el-option v-for=\"item in professionalsList\" :key=\"item.proid\" :label=\"item.proname\"\n            :value=\"item.proid\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item prop=\"spic\" label=\"照片\" min-width=\"20%\">\n        <el-input v-model=\"formData.spic\" placeholder=\"照片\" readonly=\"true\" style=\"width: 50%\"></el-input>\n        <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\n        <el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\n      </el-form-item>\n    </el-form>\n    <el-dialog v-model=\"uploadVisible\" title=\"附件上传\" custom-class=\"el-dialog-widthSmall\" @close=\"closeDialog\">\n      <div>\n        <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n      </div>\n      <el-upload action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n        style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\" drag :limit=\"1\"\n        :on-preview=\"handlePreview\" :on-remove=\"handleRemove\" :file-list=\"fileList\" :on-exceed=\"handleExceed\"\n        :auto-upload=\"false\" name=\"file\" :on-change=\"fileListChange\">\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\">\n          <div style=\"display: inline; color: #d70000; font-size: 14px\" class=\"uploadFileWarning\"\n            id=\"uploadFileWarning\"></div>\n        </div>\n      </el-upload>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"hideUpload\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n<script>\n  import request, { base } from '../../../../utils/http';\n\n  export default {\n    name: 'StudentsAdd',\n    components: {},\n    data() {\n      return {\n        uploadVisible: false,\n        btnLoading: false, //保存按钮加载状态\n        formData: {}, //表单数据\n        addrules: {\n          sno: [{ required: true, message: '请输入账号', trigger: 'blur' }],\n          password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\n          sname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\n          gender: [{ required: true, message: '请输入性别', trigger: 'blur' }],\n          phone: [\n            { required: true, message: '请输入手机号码', trigger: 'blur' },\n            { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\n          ],\n          proid: [{ required: true, message: '请选择专业', trigger: 'onchange' }],\n          spic: [{ required: true, message: '请输入照片', trigger: 'blur' }],\n         \n        },\n      };\n    },\n    mounted() {\n      this.getprofessionalsList();\n    },\n\n    methods: {\n      // 添加\n      save() {\n        this.$refs['formDataRef'].validate((valid) => {\n          //验证表单\n          if (valid) {\n            let url = base + '/students/add';\n            this.btnLoading = true;\n \n            request.post(url, this.formData).then((res) => {\n              //发送请求\n              if (res.code == 200) {\n                this.$message({\n                  message: '操作成功',\n                  type: 'success',\n                  offset: 320,\n                });\n                this.$router.push({\n                  path: '/StudentsManage',\n                });\n              } else {\n                this.$message({\n                  message: res.msg,\n                  type: 'error',\n                  offset: 320,\n                });\n              }\n              this.btnLoading = false;\n            });\n          }\n        });\n      },\n\n      // 返回\n      goBack() {\n        this.$router.push({\n          path: '/StudentsManage',\n        });\n      },\n\n      getprofessionalsList() {\n        let para = {};\n        this.listLoading = true;\n        let url = base + '/professionals/list?currentPage=1&pageSize=1000';\n        request.post(url, para).then((res) => {\n          this.professionalsList = res.resdata;\n        });\n      },\n\n      //显示上传框\n      showUpload() {\n        this.uploadVisible = true;\n      },\n\n      //隐藏上传框\n      hideUpload() {\n        this.uploadVisible = false;\n      },\n      //上传\n      handleRemove(file, fileList) {\n        this.fileList = fileList;\n      },\n      handlePreview(file) {\n        console.log(file);\n      },\n      handleExceed(files, fileList) {\n        this.$message({\n          duration: 1000,\n          message: '只能上传一个文件',\n          type: 'error',\n          offset: 320,\n        });\n      },\n      // 判断上传文件后缀\n      fileListChange(file, fileList) {\n        let extendFileName = 'png,jpg';\n        let extendFileNames = extendFileName.split(',');\n        let regExpRules = [];\n        for (let i = 0; i < extendFileNames.length; i++) {\n          regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n        }\n        let fileNames = [];\n        let files = [];\n        let that = this;\n        fileList.forEach(function (key, val) {\n          let ret = false;\n          for (let i = 0; i < regExpRules.length; i++) {\n            ret = ret || regExpRules[i].test(key['name']);\n          }\n          if (!ret) {\n            console.log(key['name'] + ':' + ret);\n            that.$message({\n              duration: 1000,\n              message: '上传的文件后缀必须为' + extendFileName + '格式！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          if (fileNames.indexOf(key['name']) != -1) {\n            that.$message({\n              duration: 1000,\n              message: '上传的文件重复！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          //只能上传一个文件，用最后上传的覆盖\n          if (!that.multiFiles) {\n            files = [];\n            fileNames = [];\n          }\n          files.push(key);\n          fileNames.push(key['name']);\n          if (fileNames !== '') {\n            // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n          }\n          //$(\".uploadFileWarning\").text(\"\");\n        });\n        this.files = fileNames;\n        this.fileList = files;\n      },\n      /**\n       * 确认按钮\n       */\n      handleConfirm() {\n        let filePath = this.fileList;\n        if (filePath.length === 0) {\n          this.$message({\n            duration: 1000,\n            message: '请选择文件！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        let formData = new FormData();\n        this.fileList.forEach((file) => {\n          formData.append('file', file.raw, file.raw.name);\n        });\n        let url = base + '/common/uploadFile';\n        console.log('url=' + url);\n        request.post(url, formData).then((res) => {\n          console.log(res);\n          let furl = res.resdata.filePath;\n          this.formData.spic = furl; // 上传文件的路径\n          this.hideUpload();\n          console.log(res);\n        });\n      },\n    },\n  };\n</script>\n<style scoped></style>"], "mappings": ";;;AA0EE,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE,CAAC,CAAC;EACdC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC5DC,QAAQ,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACjEE,KAAK,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC9DG,MAAM,EAAE,CAAC;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC/DI,KAAK,EAAE,CACL;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACvD;UAAEK,OAAO,EAAE,mBAAmB;UAAEN,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CACxE;QACDM,KAAK,EAAE,CAAC;UAAER,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAW,CAAC,CAAC;QAClEO,IAAI,EAAE,CAAC;UAAET,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC;MAE9D;IACF,CAAC;EACH,CAAC;EACDQ,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAC5C;QACA,IAAIA,KAAK,EAAE;UACT,IAAIC,GAAE,GAAI1B,IAAG,GAAI,eAAe;UAChC,IAAI,CAACK,UAAS,GAAI,IAAI;UAEtBN,OAAO,CAAC4B,IAAI,CAACD,GAAG,EAAE,IAAI,CAACpB,QAAQ,CAAC,CAACsB,IAAI,CAAEC,GAAG,IAAK;YAC7C;YACA,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZrB,OAAO,EAAE,MAAM;gBACfsB,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZrB,OAAO,EAAEmB,GAAG,CAACQ,GAAG;gBAChBL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAAC5B,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAiC,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAEDhB,oBAAoBA,CAAA,EAAG;MACrB,IAAImB,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAId,GAAE,GAAI1B,IAAG,GAAI,iDAAiD;MAClED,OAAO,CAAC4B,IAAI,CAACD,GAAG,EAAEa,IAAI,CAAC,CAACX,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACY,iBAAgB,GAAIZ,GAAG,CAACa,OAAO;MACtC,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,UAAUA,CAAA,EAAG;MACX,IAAI,CAACvC,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACAwC,UAAUA,CAAA,EAAG;MACX,IAAI,CAACxC,aAAY,GAAI,KAAK;IAC5B,CAAC;IACD;IACAyC,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAO,GAAIA,QAAQ;IAC1B,CAAC;IACDC,aAAaA,CAACF,IAAI,EAAE;MAClBG,OAAO,CAACC,GAAG,CAACJ,IAAI,CAAC;IACnB,CAAC;IACDK,YAAYA,CAACC,KAAK,EAAEL,QAAQ,EAAE;MAC5B,IAAI,CAAChB,QAAQ,CAAC;QACZsB,QAAQ,EAAE,IAAI;QACd3C,OAAO,EAAE,UAAU;QACnBsB,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD;IACAqB,cAAcA,CAACR,IAAI,EAAEC,QAAQ,EAAE;MAC7B,IAAIQ,cAAa,GAAI,SAAS;MAC9B,IAAIC,eAAc,GAAID,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC;MAC/C,IAAIC,WAAU,GAAI,EAAE;MACpB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,eAAe,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/CD,WAAW,CAACvB,IAAI,CAAC,IAAI0B,MAAM,CAAC,QAAO,GAAIL,eAAe,CAACG,CAAC,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC;MAC3E;MACA,IAAIG,SAAQ,GAAI,EAAE;MAClB,IAAIV,KAAI,GAAI,EAAE;MACd,IAAIW,IAAG,GAAI,IAAI;MACfhB,QAAQ,CAACiB,OAAO,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnC,IAAIC,GAAE,GAAI,KAAK;QACf,KAAK,IAAIR,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3CQ,GAAE,GAAIA,GAAE,IAAKT,WAAW,CAACC,CAAC,CAAC,CAACS,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C;QACA,IAAI,CAACE,GAAG,EAAE;UACRlB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC,MAAM,IAAI,GAAE,GAAIE,GAAG,CAAC;UACpCJ,IAAI,CAAChC,QAAQ,CAAC;YACZsB,QAAQ,EAAE,IAAI;YACd3C,OAAO,EAAE,YAAW,GAAI6C,cAAa,GAAI,KAAK;YAC9CvB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA,IAAI6B,SAAS,CAACO,OAAO,CAACJ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;UACxCF,IAAI,CAAChC,QAAQ,CAAC;YACZsB,QAAQ,EAAE,IAAI;YACd3C,OAAO,EAAE,UAAU;YACnBsB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA;QACA,IAAI,CAAC8B,IAAI,CAACO,UAAU,EAAE;UACpBlB,KAAI,GAAI,EAAE;UACVU,SAAQ,GAAI,EAAE;QAChB;QACAV,KAAK,CAACjB,IAAI,CAAC8B,GAAG,CAAC;QACfH,SAAS,CAAC3B,IAAI,CAAC8B,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAIH,SAAQ,KAAM,EAAE,EAAE;UACpB;QAAA;QAEF;MACF,CAAC,CAAC;MACF,IAAI,CAACV,KAAI,GAAIU,SAAS;MACtB,IAAI,CAACf,QAAO,GAAIK,KAAK;IACvB,CAAC;IACD;;;IAGAmB,aAAaA,CAAA,EAAG;MACd,IAAIC,QAAO,GAAI,IAAI,CAACzB,QAAQ;MAC5B,IAAIyB,QAAQ,CAACZ,MAAK,KAAM,CAAC,EAAE;QACzB,IAAI,CAAC7B,QAAQ,CAAC;UACZsB,QAAQ,EAAE,IAAI;UACd3C,OAAO,EAAE,QAAQ;UACjBsB,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MACA,IAAI3B,QAAO,GAAI,IAAImE,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC1B,QAAQ,CAACiB,OAAO,CAAElB,IAAI,IAAK;QAC9BxC,QAAQ,CAACoE,MAAM,CAAC,MAAM,EAAE5B,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC6B,GAAG,CAAC1E,IAAI,CAAC;MAClD,CAAC,CAAC;MACF,IAAIyB,GAAE,GAAI1B,IAAG,GAAI,oBAAoB;MACrCiD,OAAO,CAACC,GAAG,CAAC,MAAK,GAAIxB,GAAG,CAAC;MACzB3B,OAAO,CAAC4B,IAAI,CAACD,GAAG,EAAEpB,QAAQ,CAAC,CAACsB,IAAI,CAAEC,GAAG,IAAK;QACxCoB,OAAO,CAACC,GAAG,CAACrB,GAAG,CAAC;QAChB,IAAI+C,IAAG,GAAI/C,GAAG,CAACa,OAAO,CAAC8B,QAAQ;QAC/B,IAAI,CAAClE,QAAQ,CAACY,IAAG,GAAI0D,IAAI,EAAE;QAC3B,IAAI,CAAChC,UAAU,CAAC,CAAC;QACjBK,OAAO,CAACC,GAAG,CAACrB,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}