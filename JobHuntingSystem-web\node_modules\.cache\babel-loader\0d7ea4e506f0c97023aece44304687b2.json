{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsAdd.vue", "mtime": 1741603034000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "uploadVisible", "btnLoading", "formData", "jobcategoriesList", "add<PERSON><PERSON>", "pname", "required", "message", "trigger", "catid", "wlocation", "rnumber", "streatment", "pflag", "cid", "mounted", "getJobCategories", "methods", "url", "post", "params", "currentPage", "pageSize", "then", "res", "code", "resdata", "save", "$refs", "validate", "valid", "user", "JSON", "parse", "sessionStorage", "getItem", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack", "<PERSON><PERSON><PERSON><PERSON>", "val", "prequirements"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\positions\\PositionsAdd.vue"], "sourcesContent": ["<template>\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">职位管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">添加职位</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">添加职位</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\r\n      <el-form-item label=\"职位名称\" prop=\"pname\">\r\n        <el-input v-model=\"formData.pname\" placeholder=\"职位名称\" style=\"width: 50%\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"职位分类\" prop=\"catid\">\r\n        <el-select v-model=\"formData.catid\" placeholder=\"请选择\" size=\"small\" style=\"width: 50%\">\r\n          <el-option\r\n            v-for=\"item in jobcategoriesList\"\r\n            :key=\"item.catid\"\r\n            :label=\"item.catname\"\r\n            :value=\"item.catid\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"工作地点\" prop=\"wlocation\">\r\n        <el-input v-model=\"formData.wlocation\" placeholder=\"工作地点\" style=\"width: 50%\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"招聘人数\" prop=\"rnumber\">\r\n        <el-input v-model=\"formData.rnumber\" placeholder=\"招聘人数\" style=\"width: 50%\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"薪资待遇\" prop=\"streatment\">\r\n        <el-input\r\n          v-model=\"formData.streatment\"\r\n          placeholder=\"薪资待遇\"\r\n          style=\"width: 50%\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"职位要求\" prop=\"prequirements\">\r\n        <WangEditor\r\n          ref=\"wangEditorRef\"\r\n          v-model=\"formData.prequirements\"\r\n          :config=\"editorConfig\"\r\n          :isClear=\"isClear\"\r\n          @change=\"editorChange\"\r\n        ></WangEditor>\r\n      </el-form-item>\r\n      <el-form-item label=\"招聘状态\" prop=\"pflag\">\r\n        <el-radio-group v-model=\"formData.pflag\">\r\n          <el-radio label=\"开放\"> 开放 </el-radio>\r\n          <el-radio label=\"关闭\"> 关闭 </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          @click=\"save\"\r\n          :loading=\"btnLoading\"\r\n          icon=\"el-icon-upload\"\r\n          >提 交</el-button\r\n        >\r\n        <el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport request, { base } from '../../../../utils/http';\r\nimport WangEditor from '../../../components/WangEditor';\r\nexport default {\r\n  name: 'PositionsAdd',\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      uploadVisible: false,\r\n      btnLoading: false, //保存按钮加载状态\r\n      formData: {}, //表单数据\r\n      jobcategoriesList: [], // 职位分类列表\r\n      addrules: {\r\n        pname: [{ required: true, message: '请输入职位名称', trigger: 'blur' }],\r\n        catid: [{ required: true, message: '请选择职位分类', trigger: 'onchange' }],\r\n        wlocation: [{ required: true, message: '请输入工作地点', trigger: 'blur' }],\r\n        rnumber: [{ required: true, message: '请输入招聘人数', trigger: 'blur' }],\r\n        streatment: [{ required: true, message: '请输入薪资待遇', trigger: 'blur' }],\r\n        pflag: [{ required: true, message: '请输入招聘状态', trigger: 'blur' }],\r\n        cid: [{ required: true, message: '请输入企业id', trigger: 'blur' }],\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getJobCategories();\r\n  },\r\n\r\n  methods: {\r\n    // 获取职位分类列表\r\n    getJobCategories() {\r\n      let url = base + '/jobcategories/list';\r\n      request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.jobcategoriesList = res.resdata;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 添加\r\n    save() {\r\n      this.$refs['formDataRef'].validate((valid) => {\r\n        //验证表单\r\n        if (valid) {\r\n          let url = base + '/positions/add';\r\n\r\n          var user = JSON.parse(sessionStorage.getItem('user'));\r\n\r\n          this.formData.cid = user.cid;\r\n\r\n          this.btnLoading = true;\r\n          request.post(url, this.formData).then((res) => {\r\n            //发送请求\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: '操作成功',\r\n                type: 'success',\r\n                offset: 320,\r\n              });\r\n              this.$router.push({\r\n                path: '/PositionsManage',\r\n              });\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: 'error',\r\n                offset: 320,\r\n              });\r\n            }\r\n            this.btnLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 返回\r\n    goBack() {\r\n      this.$router.push({\r\n        path: '/PositionsManage',\r\n      });\r\n    },\r\n\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.prequirements = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped></style>\r\n"], "mappings": ";AAwEA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAOC,UAAS,MAAO,gCAAgC;AACvD,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,iBAAiB,EAAE,EAAE;MAAE;MACvBC,QAAQ,EAAE;QACRC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAChEC,KAAK,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAW,CAAC,CAAC;QACpEE,SAAS,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACpEG,OAAO,EAAE,CAAC;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAClEI,UAAU,EAAE,CAAC;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACrEK,KAAK,EAAE,CAAC;UAAEP,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAChEM,GAAG,EAAE,CAAC;UAAER,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAC/D;IACF,CAAC;EACH,CAAC;EACDO,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EAEDC,OAAO,EAAE;IACP;IACAD,gBAAgBA,CAAA,EAAG;MACjB,IAAIE,GAAE,GAAIvB,IAAG,GAAI,qBAAqB;MACtCD,OAAO,CAACyB,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,EAAE;QAAEE,MAAM,EAAE;UAAEC,WAAW,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAI;MAAE,CAAC,CAAC,CAACC,IAAI,CAAEC,GAAG,IAAK;QACjF,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAACtB,iBAAgB,GAAIqB,GAAG,CAACE,OAAO;QACtC;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAC5C;QACA,IAAIA,KAAK,EAAE;UACT,IAAIZ,GAAE,GAAIvB,IAAG,GAAI,gBAAgB;UAEjC,IAAIoC,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;UAErD,IAAI,CAACjC,QAAQ,CAACY,GAAE,GAAIiB,IAAI,CAACjB,GAAG;UAE5B,IAAI,CAACb,UAAS,GAAI,IAAI;UACtBP,OAAO,CAACyB,IAAI,CAACD,GAAG,EAAE,IAAI,CAAChB,QAAQ,CAAC,CAACqB,IAAI,CAAEC,GAAG,IAAK;YAC7C;YACA,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACW,QAAQ,CAAC;gBACZ7B,OAAO,EAAE,MAAM;gBACf8B,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZ7B,OAAO,EAAEiB,GAAG,CAACkB,GAAG;gBAChBL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAACrC,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACA0C,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAED;IACAG,YAAYA,CAACC,GAAG,EAAE;MAChB,IAAI,CAAC3C,QAAQ,CAAC4C,aAAY,GAAID,GAAG;IACnC;EACF;AACF,CAAC", "ignoreList": []}]}