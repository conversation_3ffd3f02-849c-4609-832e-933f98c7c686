{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyManage.vue", "mtime": 1741615257361}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "clname", "comname", "contact", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "created", "getDatas", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "cid", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "para", "cflag", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "id", "handleEdit"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyManage.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">企业列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">企业列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.clname\" placeholder=\"企业账号\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-input v-model=\"filters.comname\" placeholder=\"企业名称\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-input v-model=\"filters.contact\" placeholder=\"联系方式\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"clname\" label=\"企业账号\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"password\" label=\"登录密码\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"comname\" label=\"企业名称\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"scale\" label=\"企业规模\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"nature\" label=\"企业性质\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"contact\" label=\"联系方式\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"logo\" label=\"企业logo\" width=\"70\" align=\"center\">\n        <template #default=\"scope\">\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + scope.row.logo\" style=\"width: 50px; height: 50px\" />\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"addtime\" label=\"注册时间\" align=\"center\"></el-table-column>\n      <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\n          <el-button type=\"success\" size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\" icon=\"el-icon-edit\"\n            style=\"padding: 3px 6px 3px 6px\">编辑</el-button>\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n  </div>\n</template>\n<script>\n  import request, { base } from '../../../../utils/http';\n  export default {\n    name: 'company',\n    components: {},\n    data() {\n      return {\n        filters: {\n          //列表查询参数\n          clname: '',\n          comname: '',\n          contact: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n    methods: {\n      // 删除企业\n      handleDelete(index, row) {\n        this.$confirm('确认删除该记录吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            this.listLoading = true;\n            let url = base + '/company/del?id=' + row.cid;\n            request.post(url).then((res) => {\n              this.listLoading = false;\n\n              this.$message({\n                message: '删除成功',\n                type: 'success',\n                offset: 320,\n              });\n              this.getDatas();\n            });\n          })\n          .catch(() => { });\n      },\n\n      // 分页\n      handleCurrentChange(val) {\n        this.page.currentPage = val;\n        this.getDatas();\n      },\n\n      //获取列表数据\n      getDatas() {\n        let para = {\n          clname: this.filters.clname,\n          comname: this.filters.comname,\n          contact: this.filters.contact,\n          cflag: \"审核通过\",\n        };\n        this.listLoading = true;\n        let url =\n          base +\n          '/company/list?currentPage=' +\n          this.page.currentPage +\n          '&pageSize=' +\n          this.page.pageSize;\n        request.post(url, para).then((res) => {\n          if (res.resdata.length > 0) {\n            this.isPage = true;\n          } else {\n            this.isPage = false;\n          }\n          this.page.totalCount = res.count;\n          this.datalist = res.resdata;\n          this.listLoading = false;\n        });\n      },\n      //查询\n      query() {\n        this.getDatas();\n      },\n\n      // 查看\n      handleShow(index, row) {\n        this.$router.push({\n          path: '/CompanyDetail',\n          query: {\n            id: row.cid,\n          },\n        });\n      },\n\n      // 编辑\n      handleEdit(index, row) {\n        this.$router.push({\n          path: '/CompanyEdit',\n          query: {\n            id: row.cid,\n          },\n        });\n      },\n    },\n  };\n</script>\n<style scoped></style>"], "mappings": ";AA6DE,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,CAAC,CAAC;EACdC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;QACP;QACAC,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE;MACX,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE,CAAE;IAChB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACb,WAAU,GAAI,IAAI;QACvB,IAAIc,GAAE,GAAI3B,IAAG,GAAI,kBAAiB,GAAIqB,GAAG,CAACO,GAAG;QAC7C7B,OAAO,CAAC8B,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACjB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACkB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAChB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAiB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAAC5B,IAAI,CAACC,WAAU,GAAI2B,GAAG;MAC3B,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAIoB,IAAG,GAAI;QACThC,MAAM,EAAE,IAAI,CAACD,OAAO,CAACC,MAAM;QAC3BC,OAAO,EAAE,IAAI,CAACF,OAAO,CAACE,OAAO;QAC7BC,OAAO,EAAE,IAAI,CAACH,OAAO,CAACG,OAAO;QAC7B+B,KAAK,EAAE;MACT,CAAC;MACD,IAAI,CAACzB,WAAU,GAAI,IAAI;MACvB,IAAIc,GAAE,GACJ3B,IAAG,GACH,4BAA2B,GAC3B,IAAI,CAACQ,IAAI,CAACC,WAAU,GACpB,YAAW,GACX,IAAI,CAACD,IAAI,CAACE,QAAQ;MACpBX,OAAO,CAAC8B,IAAI,CAACF,GAAG,EAAEU,IAAI,CAAC,CAACX,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACS,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAACjC,IAAI,CAACG,UAAS,GAAImB,GAAG,CAACY,KAAK;QAChC,IAAI,CAAC3B,QAAO,GAAIe,GAAG,CAACS,OAAO;QAC3B,IAAI,CAAC1B,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACD;IACA8B,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC1B,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACA2B,UAAUA,CAACxB,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACwB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,gBAAgB;QACtBJ,KAAK,EAAE;UACLK,EAAE,EAAE3B,GAAG,CAACO;QACV;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAqB,UAAUA,CAAC7B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACwB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,cAAc;QACpBJ,KAAK,EAAE;UACLK,EAAE,EAAE3B,GAAG,CAACO;QACV;MACF,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}