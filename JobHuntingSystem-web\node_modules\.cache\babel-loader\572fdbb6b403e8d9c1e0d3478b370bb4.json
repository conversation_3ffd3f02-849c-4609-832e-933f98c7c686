{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Index.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Index.vue", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFRvcE1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy9Ub3BNZW51IjsKaW1wb3J0IEZvb3QgZnJvbSAiLi4vY29tcG9uZW50cy9Gb290IjsKaW1wb3J0IHsgRWxDb25maWdQcm92aWRlciB9IGZyb20gImVsZW1lbnQtcGx1cyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiSW5kZXhMYXlvdXQiLAogIGNvbXBvbmVudHM6IHsKICAgIFRvcE1lbnUsCiAgICBGb290CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHt9OwogIH0sCiAgc2V0dXAoKSB7fQp9Ow=="}, {"version": 3, "names": ["TopMenu", "Foot", "ElConfigProvider", "name", "components", "data", "setup"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <TopMenu />\r\n  </div>\r\n  <router-view />\r\n  <Foot />\r\n</template>\r\n\r\n<script>\r\nimport TopMenu from \"../components/TopMenu\";\r\nimport Foot from \"../components/Foot\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\n\r\n\r\n\r\nexport default {\r\n  name: \"IndexLayout\",\r\n  components: {\r\n    TopMenu,\r\n    Foot,\r\n  },\r\n  data() {\r\n    return {\r\n\r\n    };\r\n  },\r\n\r\n  setup() {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n"], "mappings": "AASA,OAAOA,OAAM,MAAO,uBAAuB;AAC3C,OAAOC,IAAG,MAAO,oBAAoB;AACrC,SAASC,gBAAe,QAAS,cAAc;AAI/C,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVJ,OAAO;IACPC;EACF,CAAC;EACDI,IAAIA,CAAA,EAAG;IACL,OAAO,CAEP,CAAC;EACH,CAAC;EAEDC,KAAKA,CAAA,EAAG,CAER;AACF,CAAC", "ignoreList": []}]}