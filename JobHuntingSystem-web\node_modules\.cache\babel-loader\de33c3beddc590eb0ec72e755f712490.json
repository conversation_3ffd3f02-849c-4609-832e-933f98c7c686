{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue?vue&type=template&id=9f4da21a", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue", "mtime": 1741614414000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "width", "align", "valign", "class", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_toDisplayString", "$data", "bbsmore", "btitle", "_hoisted_3", "_hoisted_4", "btotal", "_hoisted_5", "by1", "href", "_hoisted_6", "_hoisted_7", "src", "by2", "_hoisted_9", "sno", "_hoisted_10", "_hoisted_11", "_hoisted_12", "innerHTML", "bdetail", "_hoisted_14", "addtime", "_createElementBlock", "_Fragment", "_renderList", "item", "_hoisted_15", "_hoisted_16", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "mdetail", "_hoisted_23", "antime", "_createVNode", "_component_el_form", "model", "formData", "ref", "rules", "_component_el_form_item", "label", "prop", "_component_WangEditor", "$event", "config", "_ctx", "editorConfig", "isClear", "onChange", "$options", "<PERSON><PERSON><PERSON><PERSON>", "height", "_component_el_button", "type", "onClick", "save", "loading", "btnLoading", "_cache"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue"], "sourcesContent": ["<template>\r\n  <table style=\"width:100%;line-height:27px; font-size:12px;border: 1px solid #C2D5E3;  margin-bottom:10px;\">\r\n    <tr>\r\n\r\n      <td> <span style=\"font-size:14px; font-weight:bold\">\r\n          {{ bbsmore.btitle }}\r\n        </span>\r\n\r\n      </td>\r\n      <td width=\"200\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\">\r\n        查看：<b style=\"color:red;\">\r\n          {{ bbsmore.btotal }}\r\n        </b> |\r\n        回复：<b style=\"color:red;\">\r\n          {{ bbsmore.by1 }}\r\n        </b> |\r\n        <a href=\"/bbs\">返回列表</a>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/JobHuntingSystem/' + bbsmore.by2\" />\r\n        </div>\r\n\r\n        <span class=\"cu-tag bg-gradual-green\" style=\" height:28px;\">楼主</span>\r\n        <span class=\"text-red\">\r\n          {{ bbsmore.sno }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"bbsmore.bdetail\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ bbsmore.addtime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \" v-for=\"item in bbsmore.bbsmore\">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/JobHuntingSystem/' + item.by1\" />\r\n        </div>\r\n\r\n        <span class=\"text-red\">\r\n          {{ item.sno }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"item.mdetail\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ item.antime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\r\n\r\n    <el-form-item label=\"回复内容\" prop=\"mdetail\">\r\n      <WangEditor ref=\"wangEditorRef\" v-model=\"formData.mdetail\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n        @change=\"editorChange\" height=\"200\"></WangEditor>\r\n    </el-form-item>\r\n    <el-form-item label=\"\">\r\n      <el-button type=\"primary\" @click=\"save\" :loading=\"btnLoading\">提 交</el-button>\r\n    </el-form-item>\r\n\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport WangEditor from \"../../components/WangEditor\";\r\n\r\nexport default {\r\n  name: \"bbsView\",\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      bbsmore: \"\",\r\n\r\n      formData: {\r\n        mdetail: \"\",\r\n      },\r\n\r\n      rules: {\r\n        mdetail: [\r\n          { required: true, message: \"请输入回复内容\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n\r\n\r\n    };\r\n  },\r\n  created() {\r\n\r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let id = this.$route.query.id;\r\n      let para = {\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/bbs/get?id=\" + id;\r\n      request.post(url, para).then((res) => {\r\n        this.bbsmore = res.resdata;\r\n      });\r\n    },\r\n\r\n    //回复\r\n    save() {\r\n      var lname = sessionStorage.getItem(\"lname\");\r\n      if (lname == null) {\r\n        this.$message({\r\n          message: \"请先登录\",\r\n          type: \"error\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs.formDataRef.validate((valid) => {\r\n        if (valid) {\r\n          this.btnLoading = true;\r\n          let para = {\r\n            bid: this.bbsmore.bid,\r\n            mdetail: this.formData.mdetail,\r\n            sno: lname,\r\n          };\r\n          let url = base + \"/bbsmore/add\";\r\n          request.post(url, para).then((res) => {\r\n            this.btnLoading = false;\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: \"回复成功\",\r\n                type: \"success\",\r\n              });\r\n              this.getDatas();\r\n              this.formData.mdetail = \"\";\r\n\r\n              //设置富文本编辑器内容\r\n              this.$nextTick(() => {\r\n                this.$refs[\"wangEditorRef\"].editor.txt.html(\"\");\r\n              });\r\n\r\n\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.mdetail = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 180px;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;EACSA,KAAmG,EAAnG;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAmG;;EAG3FA,KAAwC,EAAxC;IAAA;IAAA;EAAA;AAAwC;;EAK/CC,KAAK,EAAC,KAAK;EAACC,KAAK,EAAC,QAAQ;EAACF,KAAwC,EAAxC;IAAA;EAAA;;;EACvBA,KAAkB,EAAlB;IAAA;EAAA;AAAkB;;EAGlBA,KAAkB,EAAlB;IAAA;EAAA;AAAkB;;EAOvBA,KAA+E,EAA/E;IAAA;IAAA;IAAA;IAAA;EAAA;AAA+E;;EAE9EC,KAAK,EAAC,KAAK;EAACC,KAAK,EAAC,QAAQ;EAACF,KAAwC,EAAxC;IAAA;EAAA,CAAwC;EAACG,MAAM,EAAC;;;;EAOvEC,KAAK,EAAC;AAAU;;EAIpBD,MAAM,EAAC;AAAK;;EACPF,KAAK,EAAC;AAAM;;EAEXD,KAA2C,EAA3C;IAAA;EAAA;AAA2C;;;EAO3CA,KAA4B,EAA5B;IAAA;EAAA;AAA4B;;EAUnCA,KAA+E,EAA/E;IAAA;IAAA;IAAA;IAAA;EAAA;AAA+E;;EAE9EC,KAAK,EAAC,KAAK;EAACC,KAAK,EAAC,QAAQ;EAACF,KAAwC,EAAxC;IAAA;EAAA,CAAwC;EAACG,MAAM,EAAC;;;;EAMvEC,KAAK,EAAC;AAAU;;EAIpBD,MAAM,EAAC;AAAK;;EACPF,KAAK,EAAC;AAAM;;EAEXD,KAA2C,EAA3C;IAAA;EAAA;AAA2C;;;EAO3CA,KAA4B,EAA5B;IAAA;EAAA;AAA4B;;;;;;6DA1E1CK,mBAAA,CAkBQ,SAlBRC,UAkBQ,GAjBND,mBAAA,CAgBK,aAdHA,mBAAA,CAIK,a,2CAJD,GAAC,IAAAA,mBAAA,CAEI,QAFJE,UAEI,EAAAC,gBAAA,CADFC,KAAA,CAAAC,OAAO,CAACC,MAAM,iB,GAIrBN,mBAAA,CAQK,MARLO,UAQK,G,2CARmE,MACnE,IAAAP,mBAAA,CAEC,KAFDQ,UAEC,EAAAL,gBAAA,CADCC,KAAA,CAAAC,OAAO,CAACI,MAAM,kB,2CACf,QACD,IAAAT,mBAAA,CAEC,KAFDU,UAEC,EAAAP,gBAAA,CADCC,KAAA,CAAAC,OAAO,CAACM,GAAG,kB,2CACZ,KACJ,I,0BAAAX,mBAAA,CAAuB;IAApBY,IAAI,EAAC;EAAM,GAAC,MAAI,qB,OAIzBZ,mBAAA,CA+BQ,SA/BRa,UA+BQ,GA9BNb,mBAAA,CA6BK,aA5BHA,mBAAA,CAUK,MAVLc,UAUK,GATHd,mBAAA,CAGM,cAFJA,mBAAA,CACmE;IAD9DL,KAAmD,EAAnD;MAAA;MAAA;MAAA;IAAA,CAAmD;IACrDoB,GAAG,8CAA8CX,KAAA,CAAAC,OAAO,CAACW;mEAG9DhB,mBAAA,CAAqE;IAA/DD,KAAK,EAAC,yBAAyB;IAACJ,KAAqB,EAArB;MAAA;IAAA;KAAsB,IAAE,sBAC9DK,mBAAA,CAEO,QAFPiB,UAEO,EAAAd,gBAAA,CADFC,KAAA,CAAAC,OAAO,CAACa,GAAG,iB,GAGlBlB,mBAAA,CAgBK,MAhBLmB,WAgBK,GAfHnB,mBAAA,CAaQ,SAbRoB,WAaQ,GAZNpB,mBAAA,CAMK,aALHA,mBAAA,CAIK,MAJLqB,WAIK,GAHHrB,mBAAA,CAEM;IAFDsB,SAAwB,EAAhBlB,KAAA,CAAAC,OAAO,CAACkB;4CAKzBvB,mBAAA,CAIK,aAHHA,mBAAA,CAEK,MAFLwB,WAEK,EAF4B,MAC5B,GAAArB,gBAAA,CAAGC,KAAA,CAAAC,OAAO,CAACoB,OAAO,iB,8BASjCC,mBAAA,CA8BQC,SAAA,QAAAC,WAAA,CA9B8FxB,KAAA,CAAAC,OAAO,CAACA,OAAO,EAAvBwB,IAAI;yBAAlGH,mBAAA,CA8BQ,SA9BRI,WA8BQ,GA7BN9B,mBAAA,CA4BK,aA3BHA,mBAAA,CASK,MATL+B,WASK,GARH/B,mBAAA,CAGM,cAFJA,mBAAA,CACgE;MAD3DL,KAAmD,EAAnD;QAAA;QAAA;QAAA;MAAA,CAAmD;MACrDoB,GAAG,8CAA8Cc,IAAI,CAAClB;4CAG3DX,mBAAA,CAEO,QAFPgC,WAEO,EAAA7B,gBAAA,CADF0B,IAAI,CAACX,GAAG,iB,GAGflB,mBAAA,CAgBK,MAhBLiC,WAgBK,GAfHjC,mBAAA,CAaQ,SAbRkC,WAaQ,GAZNlC,mBAAA,CAMK,aALHA,mBAAA,CAIK,MAJLmC,WAIK,GAHHnC,mBAAA,CAEM;MAFDsB,SAAqB,EAAbO,IAAI,CAACO;8CAKtBpC,mBAAA,CAIK,aAHHA,mBAAA,CAEK,MAFLqC,WAEK,EAF4B,MAC5B,GAAAlC,gBAAA,CAAG0B,IAAI,CAACS,MAAM,iB;oCAS7BC,YAAA,CAUUC,kBAAA;IAVAC,KAAK,EAAErC,KAAA,CAAAsC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAExC,KAAA,CAAAwC,KAAK;IAAE/C,KAAK,EAAC;;sBAElF,MAGe,CAHf0C,YAAA,CAGeM,uBAAA;MAHDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MACmD,CADnDR,YAAA,CACmDS,qBAAA;QADvCL,GAAG,EAAC,eAAe;oBAAUvC,KAAA,CAAAsC,QAAQ,CAACN,OAAO;mEAAhBhC,KAAA,CAAAsC,QAAQ,CAACN,OAAO,GAAAa,MAAA;QAAGC,MAAM,EAAEC,IAAA,CAAAC,YAAY;QAAGC,OAAO,EAAEF,IAAA,CAAAE,OAAO;QAChGC,QAAM,EAAEC,QAAA,CAAAC,YAAY;QAAEC,MAAM,EAAC;;;QAElClB,YAAA,CAEeM,uBAAA;MAFDC,KAAK,EAAC;IAAE;wBACpB,MAA6E,CAA7EP,YAAA,CAA6EmB,oBAAA;QAAlEC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAEL,QAAA,CAAAM,IAAI;QAAGC,OAAO,EAAEX,IAAA,CAAAY;;0BAAY,MAAGC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E", "ignoreList": []}]}