{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsView.vue?vue&type=template&id=7f1421ea&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsView.vue", "mtime": 1741617175000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$data", "formData", "pname", "_hoisted_5", "_hoisted_6", "streatment", "_hoisted_7", "wlocation", "_hoisted_8", "rnumber", "_hoisted_9", "_hoisted_10", "innerHTML", "prequirements", "_hoisted_12", "_hoisted_13", "ptime", "pflag", "_hoisted_14", "_createVNode", "_component_el_button", "type", "size", "onClick", "$options", "handleDelivery", "disabled", "_cache", "_createCommentVNode", "_component_el_dialog", "title", "dialogVisible", "$event", "width", "footer", "_withCtx", "_hoisted_18", "submitDelivery", "selectedResumeId", "resumeList", "length", "_hoisted_15", "_component_el_radio_group", "_Fragment", "_renderList", "resume", "key", "rid", "_component_el_radio", "label", "resumename", "_hoisted_16", "createdat", "_hoisted_17", "_component_el_empty", "description", "goToAddResume"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"position-detail\">\r\n    <div class=\"detail-card\">\r\n      <div class=\"position-header\">\r\n        <h1 class=\"position-title\">{{ formData.pname }}</h1>\r\n        <div class=\"position-meta\">\r\n          <span class=\"salary\">{{ formData.streatment }}</span>\r\n          <span class=\"location\"\r\n            ><i class=\"el-icon-location\"></i>工作地点：{{ formData.wlocation }}</span\r\n          >\r\n          <span class=\"number\"><i class=\"el-icon-user\"></i>招聘人数：{{ formData.rnumber }}人</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"position-body\">\r\n        <div class=\"info-section\">\r\n          <h2 class=\"section-title\"><i class=\"el-icon-office-building\"></i>职位要求</h2>\r\n          <div class=\"section-content\" v-html=\"formData.prequirements\"></div>\r\n        </div>\r\n\r\n        <div class=\"info-section\">\r\n          <h2 class=\"section-title\"><i class=\"el-icon-info\"></i>其他信息</h2>\r\n          <div class=\"section-content\">\r\n            <p>发布时间：{{ formData.ptime }}</p>\r\n            <p>职位状态：{{ formData.pflag }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"position-footer\">\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"large\"\r\n          @click=\"handleDelivery\"\r\n          :disabled=\"formData.pflag !== '开放'\"\r\n        >\r\n          投递简历\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 简历选择对话框 -->\r\n    <el-dialog title=\"选择简历\" v-model=\"dialogVisible\" width=\"500px\">\r\n      <div v-if=\"resumeList.length > 0\">\r\n        <el-radio-group v-model=\"selectedResumeId\">\r\n          <div v-for=\"resume in resumeList\" :key=\"resume.rid\" class=\"resume-item\">\r\n            <el-radio :label=\"resume.rid\">\r\n              {{ resume.resumename }}\r\n              <span class=\"resume-date\">创建时间：{{ resume.createdat }}</span>\r\n            </el-radio>\r\n          </div>\r\n        </el-radio-group>\r\n      </div>\r\n      <div v-else class=\"no-resume\">\r\n        <el-empty description=\"暂无简历\">\r\n          <el-button type=\"primary\" @click=\"goToAddResume\">创建简历</el-button>\r\n        </el-empty>\r\n      </div>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitDelivery\" :disabled=\"!selectedResumeId\">\r\n            确 定\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from '../../../utils/http';\r\n\r\nexport default {\r\n  name: 'PositionsView',\r\n  data() {\r\n    return {\r\n      id: '',\r\n      formData: {},\r\n      dialogVisible: false,\r\n      resumeList: [],\r\n      selectedResumeId: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getPositionData();\r\n  },\r\n  methods: {\r\n    // 获取职位详情\r\n    async getPositionData() {\r\n      try {\r\n        let url = base + '/positions/get?id=' + this.id;\r\n        const res = await request.post(url);\r\n        if (res.code === 200) {\r\n          this.formData = res.resdata;\r\n          // 获取职位信息后记录浏览记录\r\n          await this.recordBrowsingHistory();\r\n        }\r\n      } catch (error) {\r\n        console.error('获取职位详情失败:', error);\r\n        this.$message({\r\n          message: '获取职位信息失败，请重试',\r\n          type: 'error',\r\n          offset: 320,\r\n        });\r\n      }\r\n    },\r\n\r\n    // 检查是否已经投递过\r\n    async checkIfDelivered() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      let url = base + '/resumedelivery/list';\r\n      try {\r\n        const res = await request.post(\r\n          url,\r\n          { sno: lname, pid: this.id },\r\n          { params: { currentPage: 1, pageSize: 1 } }\r\n        );\r\n        if (res.code === 200 && res.resdata.length > 0) {\r\n          return true;\r\n        }\r\n        return false;\r\n      } catch (error) {\r\n        console.error('检查投递记录失败:', error);\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 修改处理投递简历按钮点击方法\r\n    async handleDelivery() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      if (!lname) {\r\n        this.$message({\r\n          message: '请先登录',\r\n          type: 'warning',\r\n          offset: 320,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 检查是否已投递\r\n      const hasDelivered = await this.checkIfDelivered();\r\n      if (hasDelivered) {\r\n        this.$message({\r\n          message: '您已经投递过该职位',\r\n          type: 'warning',\r\n          offset: 320,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 获取用户简历列表\r\n      this.getResumeList();\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n    // 获取用户简历列表\r\n    getResumeList() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      let url = base + '/resume/list';\r\n      request\r\n        .post(url, { sno: lname }, { params: { currentPage: 1, pageSize: 100 } })\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            this.resumeList = res.resdata;\r\n          }\r\n        });\r\n    },\r\n\r\n    // 跳转到创建简历页面\r\n    goToAddResume() {\r\n      this.dialogVisible = false;\r\n      this.$router.push('/resume_add');\r\n    },\r\n\r\n    // 提交简历投递\r\n    submitDelivery() {\r\n      if (!this.selectedResumeId) {\r\n        this.$message({\r\n          message: '请选择要投递的简历',\r\n          type: 'warning',\r\n          offset: 320,\r\n        });\r\n        return;\r\n      }\r\n\r\n      const lname = sessionStorage.getItem('lname');\r\n      const data = {\r\n        pid: this.id,\r\n        sno: lname,\r\n        rid: this.selectedResumeId,\r\n      };\r\n\r\n      let url = base + '/resumedelivery/add';\r\n      data.auditstatus = '待审核';\r\n      request.post(url, data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message({\r\n            message: '简历投递成功',\r\n            type: 'success',\r\n            offset: 320,\r\n          });\r\n          this.dialogVisible = false;\r\n        } else {\r\n          this.$message({\r\n            message: res.msg || '投递失败，请重试',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 记录浏览记录\r\n    async recordBrowsingHistory() {\r\n      const lname = sessionStorage.getItem('lname');\r\n      if (!lname) {\r\n        return; // 未登录不记录浏览记录\r\n      }\r\n\r\n      const data = {\r\n        pid: this.id,\r\n        sno: lname,\r\n        btime: new Date().toLocaleString(),\r\n      };\r\n\r\n      try {\r\n        let url = base + '/browsingrecords/add';\r\n        const res = await request.post(url, data);\r\n        if (res.code !== 200) {\r\n          console.error('记录浏览记录失败:', res.msg);\r\n        }\r\n      } catch (error) {\r\n        console.error('记录浏览记录失败:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.position-detail {\r\n  max-width: 1000px;\r\n  margin: 20px auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n.detail-card {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 30px;\r\n}\r\n\r\n.position-header {\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.position-title {\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.position-meta {\r\n  display: flex;\r\n  gap: 20px;\r\n  color: #666;\r\n}\r\n\r\n.salary {\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n}\r\n\r\n.location,\r\n.number {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.info-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.section-content {\r\n  color: #666;\r\n  line-height: 1.8;\r\n}\r\n\r\n.position-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.resume-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.resume-date {\r\n  margin-left: 10px;\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.no-resume {\r\n  text-align: center;\r\n  padding: 30px 0;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAiB;;EACtBA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAQ;;EACdA,KAAK,EAAC;AAAU;;EAGhBA,KAAK,EAAC;AAAQ;;EAInBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;;EAKpBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAiB;;EAO3BA,KAAK,EAAC;AAAiB;;;;;EAmBdA,KAAK,EAAC;AAAa;;;EAKrBA,KAAK,EAAC;;;EAMVA,KAAK,EAAC;AAAe;;;;;;;uBA1DjCC,mBAAA,CAkEM,OAlENC,UAkEM,GAjEJC,mBAAA,CAqCM,OArCNC,UAqCM,GApCJD,mBAAA,CASM,OATNE,UASM,GARJF,mBAAA,CAAoD,MAApDG,UAAoD,EAAAC,gBAAA,CAAtBC,KAAA,CAAAC,QAAQ,CAACC,KAAK,kBAC5CP,mBAAA,CAMM,OANNQ,UAMM,GALJR,mBAAA,CAAqD,QAArDS,UAAqD,EAAAL,gBAAA,CAA7BC,KAAA,CAAAC,QAAQ,CAACI,UAAU,kBAC3CV,mBAAA,CACmF,QADnFW,UACmF,G,0BAAhFX,mBAAA,CAAgC;IAA7BH,KAAK,EAAC;EAAkB,6B,iBAAK,OAAK,GAAAO,gBAAA,CAAGC,KAAA,CAAAC,QAAQ,CAACM,SAAS,iB,GAE7DZ,mBAAA,CAAoF,QAApFa,UAAoF,G,0BAA/Db,mBAAA,CAA4B;IAAzBH,KAAK,EAAC;EAAc,6B,iBAAK,OAAK,GAAAO,gBAAA,CAAGC,KAAA,CAAAC,QAAQ,CAACQ,OAAO,IAAG,GAAC,gB,OAIjFd,mBAAA,CAaM,OAbNe,UAaM,GAZJf,mBAAA,CAGM,OAHNgB,WAGM,G,0BAFJhB,mBAAA,CAA0E;IAAtEH,KAAK,EAAC;EAAe,IAACG,mBAAA,CAAuC;IAApCH,KAAK,EAAC;EAAyB,I,iBAAK,MAAI,E,sBACrEG,mBAAA,CAAmE;IAA9DH,KAAK,EAAC,iBAAiB;IAACoB,SAA+B,EAAvBZ,KAAA,CAAAC,QAAQ,CAACY;0CAGhDlB,mBAAA,CAMM,OANNmB,WAMM,G,0BALJnB,mBAAA,CAA+D;IAA3DH,KAAK,EAAC;EAAe,IAACG,mBAAA,CAA4B;IAAzBH,KAAK,EAAC;EAAc,I,iBAAK,MAAI,E,sBAC1DG,mBAAA,CAGM,OAHNoB,WAGM,GAFJpB,mBAAA,CAAgC,WAA7B,OAAK,GAAAI,gBAAA,CAAGC,KAAA,CAAAC,QAAQ,CAACe,KAAK,kBACzBrB,mBAAA,CAAgC,WAA7B,OAAK,GAAAI,gBAAA,CAAGC,KAAA,CAAAC,QAAQ,CAACgB,KAAK,iB,OAK/BtB,mBAAA,CASM,OATNuB,WASM,GARJC,YAAA,CAOYC,oBAAA;IANVC,IAAI,EAAC,SAAS;IACdC,IAAI,EAAC,OAAO;IACXC,OAAK,EAAEC,QAAA,CAAAC,cAAc;IACrBC,QAAQ,EAAE1B,KAAA,CAAAC,QAAQ,CAACgB,KAAK;;sBAC1B,MAEDU,MAAA,QAAAA,MAAA,O,iBAFC,QAED,E;;kDAIJC,mBAAA,aAAgB,EAChBT,YAAA,CAwBYU,oBAAA;IAxBDC,KAAK,EAAC,MAAM;gBAAU9B,KAAA,CAAA+B,aAAa;+DAAb/B,KAAA,CAAA+B,aAAa,GAAAC,MAAA;IAAEC,KAAK,EAAC;;IAgBzCC,MAAM,EAAAC,QAAA,CACf,MAKO,CALPxC,mBAAA,CAKO,QALPyC,WAKO,GAJLjB,YAAA,CAAyDC,oBAAA;MAA7CG,OAAK,EAAAI,MAAA,QAAAA,MAAA,MAAAK,MAAA,IAAEhC,KAAA,CAAA+B,aAAa;;wBAAU,MAAGJ,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;QAC7CR,YAAA,CAEYC,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAEC,QAAA,CAAAa,cAAc;MAAGX,QAAQ,GAAG1B,KAAA,CAAAsC;;wBAAkB,MAEhFX,MAAA,SAAAA,MAAA,Q,iBAFgF,OAEhF,E;;;sBApBJ,MASM,CATK3B,KAAA,CAAAuC,UAAU,CAACC,MAAM,Q,cAA5B/C,mBAAA,CASM,OAAAgD,WAAA,GARJtB,YAAA,CAOiBuB,yBAAA;kBAPQ1C,KAAA,CAAAsC,gBAAgB;iEAAhBtC,KAAA,CAAAsC,gBAAgB,GAAAN,MAAA;;wBAClC,MAA4B,E,kBAAjCvC,mBAAA,CAKMkD,SAAA,QAAAC,WAAA,CALgB5C,KAAA,CAAAuC,UAAU,EAApBM,MAAM;6BAAlBpD,mBAAA,CAKM;UAL6BqD,GAAG,EAAED,MAAM,CAACE,GAAG;UAAEvD,KAAK,EAAC;YACxD2B,YAAA,CAGW6B,mBAAA;UAHAC,KAAK,EAAEJ,MAAM,CAACE;;4BACvB,MAAuB,C,kCAApBF,MAAM,CAACK,UAAU,IAAG,GACvB,iBAAAvD,mBAAA,CAA4D,QAA5DwD,WAA4D,EAAlC,OAAK,GAAApD,gBAAA,CAAG8C,MAAM,CAACO,SAAS,iB;;;;;0DAK1D3D,mBAAA,CAIM,OAJN4D,WAIM,GAHJlC,YAAA,CAEWmC,mBAAA;MAFDC,WAAW,EAAC;IAAM;wBAC1B,MAAiE,CAAjEpC,YAAA,CAAiEC,oBAAA;QAAtDC,IAAI,EAAC,SAAS;QAAEE,OAAK,EAAEC,QAAA,CAAAgC;;0BAAe,MAAI7B,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E", "ignoreList": []}]}