{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Main.vue?vue&type=template&id=c1f1971a", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Main.vue", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxodG1sIGRhdGEtYnMtdGhlbWU9ImxpZ2h0IiBkYXRhLWxheW91dC1tb2RlPSJmbHVpZCIgZGF0YS1tZW51LWNvbG9yPSJkYXJrIiBkYXRhLXRvcGJhci1jb2xvcj0ibGlnaHQiDQogICAgICAgIGRhdGEtbGF5b3V0LXBvc2l0aW9uPSJmaXhlZCIgZGF0YS1zaWRlbmF2LXNpemU9ImRlZmF1bHQiIGNsYXNzPSJtZW51aXRlbS1hY3RpdmUiPg0KICA8RWxDb25maWdQcm92aWRlciA6bG9jYWxlPSJsb2NhbGUiPg0KDQogICAgPGRpdiBjbGFzcz0id3JhcHBlciI+DQogICAgICA8SGVhZGVyIC8+DQoNCiAgICAgIDxMZWZ0TWVudSAvPg0KDQogICAgICA8ZGl2IGNsYXNzPSJjb250ZW50LXBhZ2UiPg0KICAgICAgICA8ZGl2IGNsYXNzPSJjb250ZW50Ij4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJjb250YWluZXItZmx1aWQiPg0KICAgICAgICAgICAgPHJvdXRlci12aWV3IC8+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvZGl2PiAgDQogICAgICA8L2Rpdj4NCiAgICA8L2Rpdj4NCiAgPC9FbENvbmZpZ1Byb3ZpZGVyPg0KDQogIDwvaHRtbD4NCg=="}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Main.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9F,CAAC,CA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/Main.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <html data-bs-theme=\"light\" data-layout-mode=\"fluid\" data-menu-color=\"dark\" data-topbar-color=\"light\"\r\n        data-layout-position=\"fixed\" data-sidenav-size=\"default\" class=\"menuitem-active\">\r\n  <ElConfigProvider :locale=\"locale\">\r\n\r\n    <div class=\"wrapper\">\r\n      <Header />\r\n\r\n      <LeftMenu />\r\n\r\n      <div class=\"content-page\">\r\n        <div class=\"content\">\r\n          <div class=\"container-fluid\">\r\n            <router-view />\r\n          </div>\r\n        </div>  \r\n      </div>\r\n    </div>\r\n  </ElConfigProvider>\r\n\r\n  </html>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"../components/Header\";\r\nimport LeftMenu from \"../components/LeftMenu\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\nimport zhCn from \"element-plus/lib/locale/lang/zh-cn\";\r\n\r\nimport \"../assets/js/vendor.min.js\";\r\n\r\nexport default {\r\n  name: \"MainLayout\",\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n@import url(../assets/css/app.min.css);\r\n@import url(../assets/css/icons.min.css);\r\n\r\nbody{\r\n  background-image: url(../assets/img/auth-bg.jpg);\r\n  background-size: cover;\r\n}\r\n.form-control{\r\n  border: 1px solid #3bc0c3;\r\n}\r\n.form-control:focus{\r\n  border: 2px solid #c01750;\r\n  box-shadow: none;\r\n}\r\n</style>\r\n\r\n\r\n"]}]}