{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue", "mtime": 1741536460000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvaHR0cCc7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQb3NpdGlvbnNMaXN0JywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGFnZTogew0KICAgICAgICBjdXJyZW50UGFnZTogMSwgLy8g5b2T5YmN6aG1DQogICAgICAgIHBhZ2VTaXplOiA4LCAvLyDmr4/pobXmmL7npLrmnaHnm67kuKrmlbANCiAgICAgICAgdG90YWxDb3VudDogMCwgLy8g5oC75p2h55uu5pWwDQogICAgICB9LA0KICAgICAgcG9saXN0OiBbXSwNCiAgICAgIGNhdGlkOiAnJywNCiAgICAgIGtleXdvcmQ6ICcnLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5jYXRpZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmNhdGlkID09IG51bGwgPyAnJyA6IHRoaXMuJHJvdXRlLnF1ZXJ5LmNhdGlkOw0KICAgIHRoaXMua2V5d29yZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmtleXdvcmQgPT0gbnVsbCA/ICcnIDogdGhpcy4kcm91dGUucXVlcnkua2V5d29yZDsNCiAgICB0aGlzLmdldERhdGFzKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliIbpobUNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy5wYWdlLmN1cnJlbnRQYWdlID0gdmFsOw0KICAgICAgdGhpcy5nZXREYXRhcygpOw0KICAgIH0sDQoNCiAgICAvL+iOt+WPluWIl+ihqOaVsOaNrg0KICAgIGdldERhdGFzKCkgew0KICAgICAgbGV0IHBhcmEgPSB7DQogICAgICAgIHBmbGFnOiAn5byA5pS+JywgLy8g5Y+q5pi+56S65byA5pS+54q25oCB55qE6IGM5L2NDQogICAgICAgIHBmbGFnMjogJ+WuoeaguOmAmui/hycsIC8vIOWPquaYvuekuuWuoeaguOmAmui/h+eahOiBjOS9jQ0KICAgICAgICBjYXRpZDogdGhpcy5jYXRpZCwNCiAgICAgICAgY29uZGl0aW9uOiAnIGFuZCBwbmFtZSBsaWtlICIlJyArIHRoaXMua2V5d29yZCArICclIiAnLA0KICAgICAgfTsNCiAgICAgIGxldCB1cmwgPQ0KICAgICAgICBiYXNlICsNCiAgICAgICAgJy9wb3NpdGlvbnMvbGlzdD9jdXJyZW50UGFnZT0nICsNCiAgICAgICAgdGhpcy5wYWdlLmN1cnJlbnRQYWdlICsNCiAgICAgICAgJyZwYWdlU2l6ZT0nICsNCiAgICAgICAgdGhpcy5wYWdlLnBhZ2VTaXplOw0KICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMucG9saXN0ID0gcmVzLnJlc2RhdGE7DQogICAgICAgIHRoaXMucGFnZS50b3RhbENvdW50ID0gcmVzLmNvdW50Ow0KICAgICAgfSk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue"], "names": [], "mappings": ";AAoCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>;MACL,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACvB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;MACxD,CAAC;MACD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC;EACH,CAAC;AACH,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/PositionsList.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"positions-list\">\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-6 col-md-6 wow fadeIn\" data-wow-delay=\"300ms\" v-for=\"item in polist\" :key=\"item.pid\">\r\n        <div class=\"icon-box icon-box--border\">\r\n          <div class=\"icon-box__heading\">\r\n            <h3 class=\"heading-title\">{{ item.pname }}</h3>\r\n          </div>\r\n          <div class=\"icon-box__content\">\r\n            <div class=\"job-info\">\r\n              <p><i class=\"el-icon-location\"></i> 工作地点：{{ item.wlocation }}</p>\r\n              <p><i class=\"el-icon-user\"></i> 招聘人数：{{ item.rnumber }}</p>\r\n              <p>\r\n                <i class=\"el-icon-money\"></i> 薪资待遇：<span class=\"salary\">{{\r\n                  item.streatment\r\n                }}</span>\r\n              </p>\r\n              <p><i class=\"el-icon-time\"></i> 发布时间：{{ item.ptime }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon-box__btn\">\r\n            <a :href=\"'positionsView?id=' + item.pid\">\r\n              查看详情<span><i class=\"fa fa-chevron-right\"></i></span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" class=\"pagination\">\r\n    </el-pagination>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from '../../../utils/http';\r\nexport default {\r\n  name: 'PositionsList',\r\n  data() {\r\n    return {\r\n      page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 8, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n      },\r\n      polist: [],\r\n      catid: '',\r\n      keyword: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.catid = this.$route.query.catid == null ? '' : this.$route.query.catid;\r\n    this.keyword = this.$route.query.keyword == null ? '' : this.$route.query.keyword;\r\n    this.getDatas();\r\n  },\r\n  methods: {\r\n    // 分页\r\n    handleCurrentChange(val) {\r\n      this.page.currentPage = val;\r\n      this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {\r\n        pflag: '开放', // 只显示开放状态的职位\r\n        pflag2: '审核通过', // 只显示审核通过的职位\r\n        catid: this.catid,\r\n        condition: ' and pname like \"%' + this.keyword + '%\" ',\r\n      };\r\n      let url =\r\n        base +\r\n        '/positions/list?currentPage=' +\r\n        this.page.currentPage +\r\n        '&pageSize=' +\r\n        this.page.pageSize;\r\n      request.post(url, para).then((res) => {\r\n        this.polist = res.resdata;\r\n        this.page.totalCount = res.count;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.positions-list {\r\n  padding: 20px;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -15px;\r\n}\r\n\r\n.col-lg-6 {\r\n  padding: 15px;\r\n  width: 50%;\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .col-lg-6 {\r\n    width: 50%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .col-lg-6 {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.job-info {\r\n  text-align: left;\r\n  padding: 10px 0;\r\n}\r\n\r\n.job-info p {\r\n  margin: 5px 0;\r\n  color: #666;\r\n}\r\n\r\n.job-info i {\r\n  margin-right: 5px;\r\n  color: #3bc0c3;\r\n}\r\n\r\n.icon-box {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  height: 100%;\r\n}\r\n\r\n.icon-box:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.heading-title {\r\n  color: #333;\r\n  font-size: 18px;\r\n  margin-bottom: 15px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.icon-box__btn {\r\n  margin-top: 15px;\r\n  text-align: right;\r\n}\r\n\r\n.icon-box__btn a {\r\n  color: #3bc0c3;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.icon-box__btn a:hover {\r\n  color: #2a8f91;\r\n}\r\n\r\n.salary {\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n}\r\n\r\n.pagination {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}