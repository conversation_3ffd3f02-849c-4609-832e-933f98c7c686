{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs.vue?vue&type=template&id=310a34a4", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs.vue", "mtime": 1741614414000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5G,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAGP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrG,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAGL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Bbs.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <table style=\"width: 100%;line-height:32px;font-size:13px\" class=\"bbs\" v-for=\"item in bbslist\" :key=\"item.bid\">\r\n    <tr>\r\n      <td>\r\n        <a :href=\"'bbsView?id=' + item.bid\">{{ item.btitle }}\r\n        </a>\r\n      </td>\r\n      <td width=\"400\">\r\n        <span style=\"float:right;\">\r\n          {{ item.addtime }}\r\n        </span>\r\n        <span class=\"cu-tag line-red\" style=\"height:28px;float:right;  margin-right: 10px;\">点击量：{{\r\n            item.btotal\r\n          }}</span>\r\n        <span class=\"cu-tag line-green light\" style=\"float:right; margin-right: 10px; height:28px; line-height:28px \">\r\n          发布人：{{ item.sno }}\r\n        </span>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n\r\n  <div style=\"width: 100%;display: inline-table;\">\r\n    <el-pagination @current-change=\"handleCurrentChange\"\r\n                   :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" background\r\n                   layout=\"total, prev, pager, next, jumper\"\r\n                   :total=\"page.totalCount\" style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n  </div>\r\n\r\n\r\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\r\n    <el-form-item label=\"帖子标题\" prop=\"btitle\">\r\n      <el-input v-model=\"formData.btitle\" placeholder=\"帖子标题\" style=\"width:50%;\"></el-input>\r\n    </el-form-item>\r\n    <el-form-item label=\"帖子内容\" prop=\"bdetail\">\r\n      <WangEditor ref=\"wangEditorRef\" v-model=\"formData.bdetail\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n                  @change=\"editorChange\" height=\"200\"></WangEditor>\r\n    </el-form-item>\r\n    <el-form-item label=\"\">\r\n      <el-button type=\"primary\" @click=\"save\" :loading=\"btnLoading\">提 交</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport request, {base} from \"../../../utils/http\";\r\nimport WangEditor from \"../../components/WangEditor\";\r\n\r\nexport default {\r\n  name: \"bbs\",\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 15, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n      },\r\n      bbslist: \"\",\r\n\r\n      formData: {}, //表单数据\r\n      rules: {\r\n        btitle: [\r\n          {required: true, message: \"请输入帖子标题\", trigger: \"blur\"},\r\n        ],\r\n        bdetail: [\r\n          {required: true, message: \"请输入帖子内容\", trigger: \"blur\"},\r\n        ],\r\n      },\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.keyword = this.$route.query.keyword;\r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 分页\r\n    handleCurrentChange(val) {\r\n      this.page.currentPage = val;\r\n\r\n      this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {      \r\n\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/bbs/list?currentPage=\" + this.page.currentPage + \"&pageSize=\" + this.page.pageSize;\r\n      request.post(url, para).then((res) => {\r\n        if (res.resdata.length > 0) {\r\n          this.isPage = true;\r\n        } else {\r\n          this.isPage = false;\r\n        }\r\n        this.page.totalCount = res.count;\r\n        this.bbslist = res.resdata;\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n\r\n    //保存\r\n    save() {\r\n\r\n      var lname = sessionStorage.getItem(\"lname\");\r\n      if (lname == null) {\r\n        this.$message({\r\n          message: \"请先登录\",\r\n          type: \"error\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs.formDataRef.validate((valid) => {\r\n        if (valid) {\r\n          let para = {\r\n            bdetail: this.formData.bdetail,\r\n            btitle: this.formData.btitle,\r\n            sno: lname,\r\n            btotal: 0,\r\n          };\r\n          this.btnLoading = true;\r\n          let url = base + \"/bbs/add\";\r\n          request.post(url, para).then((res) => {\r\n            this.btnLoading = false;\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: \"提交成功\",\r\n                type: \"success\",\r\n              });\r\n              this.formData = {};\r\n\r\n              //设置富文本编辑器内容\r\n              this.$nextTick(() => {\r\n                this.$refs[\"wangEditorRef\"].editor.txt.html(\"\");\r\n              });\r\n\r\n              this.getDatas();\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.bdetail = val;\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 200px;\r\n}\r\n</style>\r\n\r\n\r\n"]}]}