{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue?vue&type=template&id=91b6ff58&scoped=true", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue", "mtime": 1741616960745}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9InRvcC1tZW51Ij4NCiAgICA8IS0tIOmhtumDqOS/oeaBr+agjyAtLT4NCiAgICA8ZGl2IGNsYXNzPSJ0b3AtYmFyIj4NCiAgICAgIDxkaXYgY2xhc3M9ImNvbnRhaW5lciI+DQogICAgICAgIDxkaXYgY2xhc3M9InRvcC1iYXItY29udGVudCI+DQogICAgICAgICAgPGRpdiBjbGFzcz0iY29udGFjdC1pbmZvIj4NCiAgICAgICAgICAgIDxhIGhyZWY9Im1haWx0bzphZG1pbkAxNjMuY29tIiBjbGFzcz0iY29udGFjdC1pdGVtIj4NCiAgICAgICAgICAgICAgPGkgY2xhc3M9ImZhcyBmYS1lbnZlbG9wZSI+PC9pPg0KICAgICAgICAgICAgICA8c3Bhbj5hZG1pbkAxNjMuY29tPC9zcGFuPg0KICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgPGEgaHJlZj0idGVsOjAxMDY2NjY2NjY2IiBjbGFzcz0iY29udGFjdC1pdGVtIj4NCiAgICAgICAgICAgICAgPGkgY2xhc3M9ImZhcyBmYS1waG9uZSI+PC9pPg0KICAgICAgICAgICAgICA8c3Bhbj4wMTAgNjY2NiA2NjY2PC9zcGFuPg0KICAgICAgICAgICAgPC9hPg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InVzZXItaW5mbyI+DQogICAgICAgICAgICA8dGVtcGxhdGUgdi1pZj0iaXNsb2dpbiI+DQogICAgICAgICAgICAgIDxhIGhyZWY9Ii9TcmVnIiBjbGFzcz0idXNlci1saW5rIj7msYLogYzogIXms6jlhow8L2E+DQogICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJkaXZpZGVyIj58PC9zcGFuPg0KICAgICAgICAgICAgICA8YSBocmVmPSIvU2xvZ2luIiBjbGFzcz0idXNlci1saW5rIj7msYLogYzogIXnmbvlvZU8L2E+DQogICAgICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgICAgICAgPHRlbXBsYXRlIHYtZWxzZT4NCiAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9IndlbGNvbWUtdGV4dCI+5qyi6L+O5oKo77yaPC9zcGFuPg0KICAgICAgICAgICAgICA8YSBocmVmPSIvc3dlY2xvbWUiIGNsYXNzPSJ1c2VyLW5hbWUiPnt7IGxuYW1lIH19PC9hPg0KICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iZGl2aWRlciI+fDwvc3Bhbj4NCiAgICAgICAgICAgICAgPGEgaHJlZj0iamF2YXNjcmlwdDp2b2lkKDApOyIgQGNsaWNrPSJleGl0IiBjbGFzcz0ibG9nb3V0LWxpbmsiPumAgOWHuueZu+W9lTwvYT4NCiAgICAgICAgICAgIDwvdGVtcGxhdGU+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvZGl2Pg0KICAgICAgPC9kaXY+DQogICAgPC9kaXY+DQoNCiAgICA8IS0tIOS4u+WvvOiIquagjyAtLT4NCiAgICA8ZGl2IGNsYXNzPSJtYWluLW5hdiI+DQogICAgICA8ZGl2IGNsYXNzPSJjb250YWluZXIiPg0KICAgICAgICA8ZGl2IGNsYXNzPSJuYXYtY29udGVudCI+DQogICAgICAgICAgPCEtLSBMb2dvIC0tPg0KICAgICAgICAgIDxhIGhyZWY9Ii9pbmRleCIgY2xhc3M9ImxvZ28iPg0KICAgICAgICAgICAgPGltZyBzcmM9Ii4uL2Fzc2V0cy9pbWFnZXMvbWFpbi1sb2dvLnBuZyIgYWx0PSLmsYLogYzns7vnu58iIC8+DQogICAgICAgICAgPC9hPg0KDQogICAgICAgICAgPCEtLSDnp7vliqjnq6/oj5zljZXmjInpkq4gLS0+DQogICAgICAgICAgPGRpdiBjbGFzcz0ibW9iaWxlLXRvZ2dsZSIgQGNsaWNrPSJ0b2dnbGVNb2JpbGVNZW51Ij4NCiAgICAgICAgICAgIDxzcGFuPjwvc3Bhbj4NCiAgICAgICAgICAgIDxzcGFuPjwvc3Bhbj4NCiAgICAgICAgICAgIDxzcGFuPjwvc3Bhbj4NCiAgICAgICAgICA8L2Rpdj4NCg0KICAgICAgICAgIDwhLS0g5a+86Iiq6I+c5Y2VIC0tPg0KICAgICAgICAgIDxkaXYgY2xhc3M9Im5hdi1tZW51IiA6Y2xhc3M9InsgJ2FjdGl2ZSc6IG1vYmlsZU1lbnVBY3RpdmUgfSI+DQogICAgICAgICAgICA8dWwgY2xhc3M9Im5hdi1saXN0Ij4NCiAgICAgICAgICAgICAgPGxpIGNsYXNzPSJuYXYtaXRlbSIgOmNsYXNzPSJ7ICdhY3RpdmUnOiBhY3RpdmVQYXRoID09PSAnL2luZGV4JyB9Ij4NCiAgICAgICAgICAgICAgICA8YSBocmVmPSIvaW5kZXgiIGNsYXNzPSJuYXYtbGluayI+572R56uZ6aaW6aG1PC9hPg0KICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICA8bGkgY2xhc3M9Im5hdi1pdGVtIiA6Y2xhc3M9InsgJ2FjdGl2ZSc6IGFjdGl2ZVBhdGggPT09ICcvcG9zaXRpb25zTGlzdCcgfSI+DQogICAgICAgICAgICAgICAgPGEgaHJlZj0iL3Bvc2l0aW9uc0xpc3QiIGNsYXNzPSJuYXYtbGluayI+5oub6IGY6IGM5L2NPC9hPg0KICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICA8bGkgY2xhc3M9Im5hdi1pdGVtIiA6Y2xhc3M9InsgJ2FjdGl2ZSc6IGFjdGl2ZVBhdGggPT09ICcvY29tcGFueUxpc3QnIH0iPg0KICAgICAgICAgICAgICAgIDxhIGhyZWY9Ii9jb21wYW55TGlzdCIgY2xhc3M9Im5hdi1saW5rIj7kvIHkuJrlsZXnpLo8L2E+DQogICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgIDxsaSBjbGFzcz0ibmF2LWl0ZW0iIDpjbGFzcz0ieyAnYWN0aXZlJzogYWN0aXZlUGF0aCA9PT0gJy9iYnMnIH0iPg0KICAgICAgICAgICAgICAgIDxhIGhyZWY9Ii9iYnMiIGNsYXNzPSJuYXYtbGluayI+5Lqk5rWB6K665Z2bPC9hPg0KICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICA8bGkgY2xhc3M9Im5hdi1pdGVtIiA6Y2xhc3M9InsgJ2FjdGl2ZSc6IGFjdGl2ZVBhdGggPT09ICcvYWknIH0iPg0KICAgICAgICAgICAgICAgIDxhIGhyZWY9Ii9haSIgY2xhc3M9Im5hdi1saW5rIj5BSemhvumXrjwvYT4NCiAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgPGxpIGNsYXNzPSJuYXYtaXRlbSIgOmNsYXNzPSJ7ICdhY3RpdmUnOiBhY3RpdmVQYXRoID09PSAnL3N3ZWNsb21lJyB9Ij4NCiAgICAgICAgICAgICAgICA8YSBocmVmPSIvc3dlY2xvbWUiIGNsYXNzPSJuYXYtbGluayI+5Liq5Lq65Lit5b+DPC9hPg0KICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgPC91bD4NCiAgICAgICAgICA8L2Rpdj4NCg0KICAgICAgICAgIDwhLS0g5pCc57Si5oyJ6ZKuIC0tPg0KICAgICAgICAgIDxkaXYgY2xhc3M9InNlYXJjaC1idG4iIEBjbGljaz0ic2hvd1NlYXJjaERpYWxvZyI+DQogICAgICAgICAgICA8aSBjbGFzcz0iZmFzIGZhLXNlYXJjaCI+PC9pPg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L2Rpdj4NCiAgICAgIDwvZGl2Pg0KICAgIDwvZGl2Pg0KDQogICAgPCEtLSDmkJzntKLlr7nor53moYYgLS0+DQogICAgPGVsLWRpYWxvZyB2LW1vZGVsPSJzZWFyY2hEaWFsb2dWaXNpYmxlIiB0aXRsZT0i6IGM5L2N5pCc57SiIiB3aWR0aD0iMzAlIiBjbGFzcz0ic2VhcmNoLWRpYWxvZyI+DQogICAgICA8ZWwtZm9ybT4NCiAgICAgICAgPGVsLWZvcm0taXRlbT4NCiAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0ic2VhcmNoS2V5d29yZCIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeiBjOS9jeWFs+mUruivjSIgcHJlZml4LWljb249ImVsLWljb24tc2VhcmNoIg0KICAgICAgICAgICAgQGtleXVwLmVudGVyPSJoYW5kbGVTZWFyY2giPjwvZWwtaW5wdXQ+DQogICAgICAgIDwvZWwtZm9ybS1pdGVtPg0KICAgICAgPC9lbC1mb3JtPg0KICAgICAgPHRlbXBsYXRlICNmb290ZXI+DQogICAgICAgIDxzcGFuIGNsYXNzPSJkaWFsb2ctZm9vdGVyIj4NCiAgICAgICAgICA8ZWwtYnV0dG9uIEBjbGljaz0ic2VhcmNoRGlhbG9nVmlzaWJsZSA9IGZhbHNlIj7lj5Yg5raIPC9lbC1idXR0b24+DQogICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImhhbmRsZVNlYXJjaCI+5pCcIOe0ojwvZWwtYnV0dG9uPg0KICAgICAgICA8L3NwYW4+DQogICAgICA8L3RlbXBsYXRlPg0KICAgIDwvZWwtZGlhbG9nPg0KICA8L2Rpdj4NCg=="}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;UAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;YACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACxD,CAAC,CAAC,CAAC;;UAEH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC3D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACjE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3C,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACzE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnD,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACvE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjD,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC/D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC9D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACpE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9C,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/components/TopMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"top-menu\">\r\n    <!-- 顶部信息栏 -->\r\n    <div class=\"top-bar\">\r\n      <div class=\"container\">\r\n        <div class=\"top-bar-content\">\r\n          <div class=\"contact-info\">\r\n            <a href=\"mailto:<EMAIL>\" class=\"contact-item\">\r\n              <i class=\"fas fa-envelope\"></i>\r\n              <span><EMAIL></span>\r\n            </a>\r\n            <a href=\"tel:01066666666\" class=\"contact-item\">\r\n              <i class=\"fas fa-phone\"></i>\r\n              <span>010 6666 6666</span>\r\n            </a>\r\n          </div>\r\n          <div class=\"user-info\">\r\n            <template v-if=\"islogin\">\r\n              <a href=\"/Sreg\" class=\"user-link\">求职者注册</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"/Slogin\" class=\"user-link\">求职者登录</a>\r\n            </template>\r\n            <template v-else>\r\n              <span class=\"welcome-text\">欢迎您：</span>\r\n              <a href=\"/sweclome\" class=\"user-name\">{{ lname }}</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"javascript:void(0);\" @click=\"exit\" class=\"logout-link\">退出登录</a>\r\n            </template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主导航栏 -->\r\n    <div class=\"main-nav\">\r\n      <div class=\"container\">\r\n        <div class=\"nav-content\">\r\n          <!-- Logo -->\r\n          <a href=\"/index\" class=\"logo\">\r\n            <img src=\"../assets/images/main-logo.png\" alt=\"求职系统\" />\r\n          </a>\r\n\r\n          <!-- 移动端菜单按钮 -->\r\n          <div class=\"mobile-toggle\" @click=\"toggleMobileMenu\">\r\n            <span></span>\r\n            <span></span>\r\n            <span></span>\r\n          </div>\r\n\r\n          <!-- 导航菜单 -->\r\n          <div class=\"nav-menu\" :class=\"{ 'active': mobileMenuActive }\">\r\n            <ul class=\"nav-list\">\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/index' }\">\r\n                <a href=\"/index\" class=\"nav-link\">网站首页</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/positionsList' }\">\r\n                <a href=\"/positionsList\" class=\"nav-link\">招聘职位</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/companyList' }\">\r\n                <a href=\"/companyList\" class=\"nav-link\">企业展示</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/bbs' }\">\r\n                <a href=\"/bbs\" class=\"nav-link\">交流论坛</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/ai' }\">\r\n                <a href=\"/ai\" class=\"nav-link\">AI顾问</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/sweclome' }\">\r\n                <a href=\"/sweclome\" class=\"nav-link\">个人中心</a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- 搜索按钮 -->\r\n          <div class=\"search-btn\" @click=\"showSearchDialog\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索对话框 -->\r\n    <el-dialog v-model=\"searchDialogVisible\" title=\"职位搜索\" width=\"30%\" class=\"search-dialog\">\r\n      <el-form>\r\n        <el-form-item>\r\n          <el-input v-model=\"searchKeyword\" placeholder=\"请输入职位关键词\" prefix-icon=\"el-icon-search\"\r\n            @keyup.enter=\"handleSearch\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"searchDialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">搜 索</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../utils/http';\r\n  import '../assets/css/font-awesome.min.css';\r\n  import '../assets/css/qbootstrap.min.css';\r\n  import '../assets/css/qanimate.css';\r\n  import '../assets/css/owl.carousel.min.css';\r\n  import '../assets/css/owl.theme.default.min.css';\r\n  import '../assets/css/qmain.css';\r\n  import { ElMessage } from 'element-plus';\r\n\r\n  export default {\r\n    name: 'TopMenu',\r\n    data() {\r\n      return {\r\n        islogin: true,\r\n        lname: '',\r\n        ishow: false,\r\n        key: '',\r\n        searchDialogVisible: false,\r\n        searchKeyword: '',\r\n        mobileMenuActive: false,\r\n        activePath: ''\r\n      };\r\n    },\r\n    mounted() {\r\n      this.lname = sessionStorage.getItem('lname');\r\n      if (this.lname) {\r\n        this.islogin = false;\r\n      }\r\n      // 获取当前路径\r\n      this.activePath = window.location.pathname;\r\n    },\r\n    methods: {\r\n      exit: function () {\r\n        var _this = this;\r\n        this.$confirm('确认退出吗?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        })\r\n          .then(() => {\r\n            sessionStorage.removeItem('lname');\r\n            location.href = '/index';\r\n          })\r\n          .catch(() => { });\r\n      },\r\n      showSearchDialog() {\r\n        this.searchDialogVisible = true;\r\n      },\r\n      handleSearch() {\r\n        if (!this.searchKeyword) {\r\n          ElMessage.warning('请输入搜索关键词');\r\n          return;\r\n        }\r\n        location.href = '/positionsList?keyword=' + this.searchKeyword;\r\n      },\r\n      toggleMobileMenu() {\r\n        this.mobileMenuActive = !this.mobileMenuActive;\r\n      }\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 整体容器 */\r\n  .top-menu {\r\n    font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\r\n  }\r\n\r\n  /* 顶部信息栏 */\r\n  .top-bar {\r\n    background-color: #f8f9fa;\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .top-bar-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .contact-info {\r\n    display: flex;\r\n  }\r\n\r\n  .contact-item {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #666;\r\n    margin-right: 20px;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .contact-item:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .contact-item i {\r\n    margin-right: 8px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-info {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .user-link,\r\n  .user-name,\r\n  .logout-link {\r\n    color: #666;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .user-link:hover,\r\n  .user-name:hover,\r\n  .logout-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-name {\r\n    color: #3498db;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .welcome-text {\r\n    color: #666;\r\n  }\r\n\r\n  .divider {\r\n    margin: 0 10px;\r\n    color: #ccc;\r\n  }\r\n\r\n  /* 主导航栏 */\r\n  .main-nav {\r\n    background-color: #fff;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n    position: relative;\r\n    z-index: 100;\r\n  }\r\n\r\n  .nav-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 0;\r\n    position: relative;\r\n  }\r\n\r\n  .logo {\r\n    display: block;\r\n    margin-right: 30px;\r\n  }\r\n\r\n  .logo img {\r\n    height: 50px;\r\n    display: block;\r\n  }\r\n\r\n  .mobile-toggle {\r\n    display: none;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    width: 30px;\r\n    height: 22px;\r\n    cursor: pointer;\r\n    z-index: 101;\r\n  }\r\n\r\n  .mobile-toggle span {\r\n    display: block;\r\n    height: 3px;\r\n    width: 100%;\r\n    background-color: #333;\r\n    border-radius: 3px;\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  .nav-menu {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .nav-list {\r\n    display: flex;\r\n    list-style: none;\r\n    margin: 0;\r\n    padding: 0;\r\n  }\r\n\r\n  .nav-item {\r\n    position: relative;\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .nav-link {\r\n    display: block;\r\n    padding: 10px 15px;\r\n    color: #333;\r\n    font-weight: 500;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .nav-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active .nav-link {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -15px;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 3px;\r\n    background-color: #3498db;\r\n  }\r\n\r\n  .search-btn {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f8f9fa;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.3s;\r\n    margin-left: 15px;\r\n  }\r\n\r\n  .search-btn:hover {\r\n    background-color: #e9ecef;\r\n  }\r\n\r\n  .search-btn i {\r\n    color: #666;\r\n    font-size: 18px;\r\n  }\r\n\r\n  /* 搜索对话框 */\r\n  .search-dialog :deep(.el-dialog__header) {\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__body) {\r\n    padding: 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__footer) {\r\n    border-top: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n  @media (max-width: 992px) {\r\n    .mobile-toggle {\r\n      display: flex;\r\n    }\r\n\r\n    .nav-menu {\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100vh;\r\n      background-color: rgba(255, 255, 255, 0.95);\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      align-items: center;\r\n      transform: translateX(-100%);\r\n      transition: transform 0.3s ease;\r\n      z-index: 100;\r\n    }\r\n\r\n    .nav-menu.active {\r\n      transform: translateX(0);\r\n    }\r\n\r\n    .nav-list {\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n\r\n    .nav-item {\r\n      margin: 10px 0;\r\n    }\r\n\r\n    .nav-link {\r\n      font-size: 18px;\r\n      padding: 10px 20px;\r\n    }\r\n\r\n    .nav-item.active::after {\r\n      display: none;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .top-bar-content {\r\n      flex-direction: column;\r\n    }\r\n\r\n    .contact-info {\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .logo img {\r\n      height: 40px;\r\n    }\r\n  }\r\n</style>"]}]}