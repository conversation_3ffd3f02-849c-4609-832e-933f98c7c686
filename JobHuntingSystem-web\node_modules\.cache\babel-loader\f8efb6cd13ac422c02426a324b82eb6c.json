{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs_Edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs_Edit.vue", "mtime": 1741617385493}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "$refs", "editor", "txt", "html", "bdetail", "save", "validate", "valid", "code", "$message", "message", "type", "offset", "$router", "push", "path", "back", "go", "<PERSON><PERSON><PERSON><PERSON>", "val"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Bbs_Edit.vue"], "sourcesContent": ["<template>\n  \n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\n<el-form-item label=\"标题\" prop=\"btitle\">\n<el-input v-model=\"formData.btitle\" placeholder=\"标题\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"内容\" prop=\"bdetail\">\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.bdetail\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\n</el-form-item>\n\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\"  icon=\"el-icon-upload\" >提 交</el-button>\n<el-button  type=\"info\" size=\"small\" @click=\"back\"  icon=\"el-icon-back\" >返 回</el-button>\n</el-form-item>\n</el-form>\n\n\n\n</template>\n<script>\nimport request, { base } from \"../../../utils/http\";\nimport WangEditor from \"../../components/WangEditor\";\nexport default {\n  name: 'Bbs_Edit',\n  components: {\n    WangEditor,\n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        \n      };\n    },\n    created() {\n    this.id = this.$route.query.id;\n    this.getDatas();\n    },\n\n \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/bbs/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            this.$refs[\"wangEditorRef\"].editor.txt.html(this.formData.bdetail);\n            \n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/bbs/update\";\n              this.btnLoading = true;\n              \n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });\n                  this.$router.push({\n                    path: \"/Bbs_Manage\",\n                  });\n                } else {\n                  this.$message({\n                    message: \"服务器错误\",\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n              });\n            }\n    \n          });\n        },\n        \n        // 返回\n        back() {\n          //返回上一页\n          this.$router.go(-1);\n        },\n       \n              \n          \n           \n            // 富文本编辑器\n    editorChange(val) {\n      this.formData.bdetail = val;\n    },\n   \n      },\n}\n\n</script>\n\n<style></style>\n\n\n\n"], "mappings": ";AAoBA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,OAAOC,UAAS,MAAO,6BAA6B;AACpD,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IACVF;EACF,CAAC;EACCG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC,CAAE;IAEhB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACV,IAAI,CAACL,EAAC,GAAI,IAAI,CAACM,MAAM,CAACC,KAAK,CAACP,EAAE;IAC9B,IAAI,CAACQ,QAAQ,CAAC,CAAC;EACf,CAAC;EAGDC,OAAO,EAAE;IAEb;IACQD,QAAQA,CAAA,EAAG;MACT,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIjB,IAAG,GAAI,cAAa,GAAI,IAAI,CAACK,EAAE;MACzCN,OAAO,CAACmB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACX,QAAO,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;QACxB,IAAI,CAACS,KAAK,CAAC,eAAe,CAAC,CAACC,MAAM,CAACC,GAAG,CAACC,IAAI,CAAC,IAAI,CAACnB,QAAQ,CAACoB,OAAO,CAAC;MAEpE,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACL,KAAK,CAAC,aAAa,CAAC,CAACM,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIf,GAAE,GAAIjB,IAAG,GAAI,aAAa;UAC9B,IAAI,CAACQ,UAAS,GAAI,IAAI;UAEtBT,OAAO,CAACmB,IAAI,CAACD,GAAG,EAAE,IAAI,CAACR,QAAQ,CAAC,CAACU,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACa,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZC,OAAO,EAAE,MAAM;gBACfC,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACN,QAAQ,CAAC;gBACZC,OAAO,EAAE,OAAO;gBAChBC,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACJ,CAAC;IAED;IACAI,IAAIA,CAAA,EAAG;MACL;MACA,IAAI,CAACH,OAAO,CAACI,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IAKG;IACRC,YAAYA,CAACC,GAAG,EAAE;MAChB,IAAI,CAACnC,QAAQ,CAACoB,OAAM,GAAIe,GAAG;IAC7B;EAEE;AACN", "ignoreList": []}]}