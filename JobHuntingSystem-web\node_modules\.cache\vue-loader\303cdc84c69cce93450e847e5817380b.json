{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue", "mtime": 1741616960745}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue"], "names": [], "mappings": ";EAoGE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACf,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;UACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;MACrB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD;IACF,CAAC;EACH,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/components/TopMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"top-menu\">\r\n    <!-- 顶部信息栏 -->\r\n    <div class=\"top-bar\">\r\n      <div class=\"container\">\r\n        <div class=\"top-bar-content\">\r\n          <div class=\"contact-info\">\r\n            <a href=\"mailto:<EMAIL>\" class=\"contact-item\">\r\n              <i class=\"fas fa-envelope\"></i>\r\n              <span><EMAIL></span>\r\n            </a>\r\n            <a href=\"tel:01066666666\" class=\"contact-item\">\r\n              <i class=\"fas fa-phone\"></i>\r\n              <span>010 6666 6666</span>\r\n            </a>\r\n          </div>\r\n          <div class=\"user-info\">\r\n            <template v-if=\"islogin\">\r\n              <a href=\"/Sreg\" class=\"user-link\">求职者注册</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"/Slogin\" class=\"user-link\">求职者登录</a>\r\n            </template>\r\n            <template v-else>\r\n              <span class=\"welcome-text\">欢迎您：</span>\r\n              <a href=\"/sweclome\" class=\"user-name\">{{ lname }}</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"javascript:void(0);\" @click=\"exit\" class=\"logout-link\">退出登录</a>\r\n            </template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主导航栏 -->\r\n    <div class=\"main-nav\">\r\n      <div class=\"container\">\r\n        <div class=\"nav-content\">\r\n          <!-- Logo -->\r\n          <a href=\"/index\" class=\"logo\">\r\n            <img src=\"../assets/images/main-logo.png\" alt=\"求职系统\" />\r\n          </a>\r\n\r\n          <!-- 移动端菜单按钮 -->\r\n          <div class=\"mobile-toggle\" @click=\"toggleMobileMenu\">\r\n            <span></span>\r\n            <span></span>\r\n            <span></span>\r\n          </div>\r\n\r\n          <!-- 导航菜单 -->\r\n          <div class=\"nav-menu\" :class=\"{ 'active': mobileMenuActive }\">\r\n            <ul class=\"nav-list\">\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/index' }\">\r\n                <a href=\"/index\" class=\"nav-link\">网站首页</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/positionsList' }\">\r\n                <a href=\"/positionsList\" class=\"nav-link\">招聘职位</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/companyList' }\">\r\n                <a href=\"/companyList\" class=\"nav-link\">企业展示</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/bbs' }\">\r\n                <a href=\"/bbs\" class=\"nav-link\">交流论坛</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/ai' }\">\r\n                <a href=\"/ai\" class=\"nav-link\">AI顾问</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/sweclome' }\">\r\n                <a href=\"/sweclome\" class=\"nav-link\">个人中心</a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- 搜索按钮 -->\r\n          <div class=\"search-btn\" @click=\"showSearchDialog\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索对话框 -->\r\n    <el-dialog v-model=\"searchDialogVisible\" title=\"职位搜索\" width=\"30%\" class=\"search-dialog\">\r\n      <el-form>\r\n        <el-form-item>\r\n          <el-input v-model=\"searchKeyword\" placeholder=\"请输入职位关键词\" prefix-icon=\"el-icon-search\"\r\n            @keyup.enter=\"handleSearch\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"searchDialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">搜 索</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../utils/http';\r\n  import '../assets/css/font-awesome.min.css';\r\n  import '../assets/css/qbootstrap.min.css';\r\n  import '../assets/css/qanimate.css';\r\n  import '../assets/css/owl.carousel.min.css';\r\n  import '../assets/css/owl.theme.default.min.css';\r\n  import '../assets/css/qmain.css';\r\n  import { ElMessage } from 'element-plus';\r\n\r\n  export default {\r\n    name: 'TopMenu',\r\n    data() {\r\n      return {\r\n        islogin: true,\r\n        lname: '',\r\n        ishow: false,\r\n        key: '',\r\n        searchDialogVisible: false,\r\n        searchKeyword: '',\r\n        mobileMenuActive: false,\r\n        activePath: ''\r\n      };\r\n    },\r\n    mounted() {\r\n      this.lname = sessionStorage.getItem('lname');\r\n      if (this.lname) {\r\n        this.islogin = false;\r\n      }\r\n      // 获取当前路径\r\n      this.activePath = window.location.pathname;\r\n    },\r\n    methods: {\r\n      exit: function () {\r\n        var _this = this;\r\n        this.$confirm('确认退出吗?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        })\r\n          .then(() => {\r\n            sessionStorage.removeItem('lname');\r\n            location.href = '/index';\r\n          })\r\n          .catch(() => { });\r\n      },\r\n      showSearchDialog() {\r\n        this.searchDialogVisible = true;\r\n      },\r\n      handleSearch() {\r\n        if (!this.searchKeyword) {\r\n          ElMessage.warning('请输入搜索关键词');\r\n          return;\r\n        }\r\n        location.href = '/positionsList?keyword=' + this.searchKeyword;\r\n      },\r\n      toggleMobileMenu() {\r\n        this.mobileMenuActive = !this.mobileMenuActive;\r\n      }\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 整体容器 */\r\n  .top-menu {\r\n    font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\r\n  }\r\n\r\n  /* 顶部信息栏 */\r\n  .top-bar {\r\n    background-color: #f8f9fa;\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .top-bar-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .contact-info {\r\n    display: flex;\r\n  }\r\n\r\n  .contact-item {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #666;\r\n    margin-right: 20px;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .contact-item:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .contact-item i {\r\n    margin-right: 8px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-info {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .user-link,\r\n  .user-name,\r\n  .logout-link {\r\n    color: #666;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .user-link:hover,\r\n  .user-name:hover,\r\n  .logout-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-name {\r\n    color: #3498db;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .welcome-text {\r\n    color: #666;\r\n  }\r\n\r\n  .divider {\r\n    margin: 0 10px;\r\n    color: #ccc;\r\n  }\r\n\r\n  /* 主导航栏 */\r\n  .main-nav {\r\n    background-color: #fff;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n    position: relative;\r\n    z-index: 100;\r\n  }\r\n\r\n  .nav-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 0;\r\n    position: relative;\r\n  }\r\n\r\n  .logo {\r\n    display: block;\r\n    margin-right: 30px;\r\n  }\r\n\r\n  .logo img {\r\n    height: 50px;\r\n    display: block;\r\n  }\r\n\r\n  .mobile-toggle {\r\n    display: none;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    width: 30px;\r\n    height: 22px;\r\n    cursor: pointer;\r\n    z-index: 101;\r\n  }\r\n\r\n  .mobile-toggle span {\r\n    display: block;\r\n    height: 3px;\r\n    width: 100%;\r\n    background-color: #333;\r\n    border-radius: 3px;\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  .nav-menu {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .nav-list {\r\n    display: flex;\r\n    list-style: none;\r\n    margin: 0;\r\n    padding: 0;\r\n  }\r\n\r\n  .nav-item {\r\n    position: relative;\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .nav-link {\r\n    display: block;\r\n    padding: 10px 15px;\r\n    color: #333;\r\n    font-weight: 500;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .nav-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active .nav-link {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -15px;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 3px;\r\n    background-color: #3498db;\r\n  }\r\n\r\n  .search-btn {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f8f9fa;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.3s;\r\n    margin-left: 15px;\r\n  }\r\n\r\n  .search-btn:hover {\r\n    background-color: #e9ecef;\r\n  }\r\n\r\n  .search-btn i {\r\n    color: #666;\r\n    font-size: 18px;\r\n  }\r\n\r\n  /* 搜索对话框 */\r\n  .search-dialog :deep(.el-dialog__header) {\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__body) {\r\n    padding: 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__footer) {\r\n    border-top: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n  @media (max-width: 992px) {\r\n    .mobile-toggle {\r\n      display: flex;\r\n    }\r\n\r\n    .nav-menu {\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100vh;\r\n      background-color: rgba(255, 255, 255, 0.95);\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      align-items: center;\r\n      transform: translateX(-100%);\r\n      transition: transform 0.3s ease;\r\n      z-index: 100;\r\n    }\r\n\r\n    .nav-menu.active {\r\n      transform: translateX(0);\r\n    }\r\n\r\n    .nav-list {\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n\r\n    .nav-item {\r\n      margin: 10px 0;\r\n    }\r\n\r\n    .nav-link {\r\n      font-size: 18px;\r\n      padding: 10px 20px;\r\n    }\r\n\r\n    .nav-item.active::after {\r\n      display: none;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .top-bar-content {\r\n      flex-direction: column;\r\n    }\r\n\r\n    .contact-info {\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .logo img {\r\n      height: 40px;\r\n    }\r\n  }\r\n</style>"]}]}