{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sinfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sinfo.vue", "mtime": 1741615257012}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "formData", "professionalsList", "rules", "sno", "required", "message", "trigger", "sname", "gender", "phone", "pattern", "proid", "spic", "sflag", "btnLoading", "uploadVisible", "created", "getinfo", "getProfessionals", "methods", "lname", "sessionStorage", "getItem", "url", "post", "then", "res", "code", "resdata", "$message", "type", "offset", "save", "$refs", "validate", "valid", "showUpload", "hideUpload", "handleRemove", "file", "fileList", "handlePreview", "console", "log", "handleExceed", "files", "duration", "fileListChange", "extendFileName", "extendFileNames", "split", "regExpRules", "i", "length", "push", "RegExp", "fileNames", "that", "for<PERSON>ach", "key", "val", "ret", "test", "indexOf", "multiFiles", "handleConfirm", "filePath", "FormData", "append", "raw", "furl", "params", "currentPage", "pageSize"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Sinfo.vue"], "sourcesContent": ["<template>\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\n    <el-form-item label=\"姓名\" prop=\"sname\">\n      <el-input v-model=\"formData.sname\" placeholder=\"姓名\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"性别\" prop=\"gender\">\n      <el-radio-group v-model=\"formData.gender\">\n        <el-radio label=\"男\"> 男 </el-radio>\n        <el-radio label=\"女\"> 女 </el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item label=\"年龄\" prop=\"age\">\n      <el-input v-model=\"formData.age\" placeholder=\"年龄\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"手机号码\" prop=\"phone\">\n      <el-input v-model=\"formData.phone\" placeholder=\"手机号码\" style=\"width: 50%\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"专业\" prop=\"proid\">\n      <el-select v-model=\"formData.proid\" placeholder=\"请选择\" size=\"small\">\n        <el-option v-for=\"item in professionalsList\" :key=\"item.proid\" :label=\"item.proname\"\n          :value=\"item.proid\"></el-option>\n      </el-select>\n    </el-form-item>\n    <el-form-item prop=\"spic\" label=\"照片\" min-width=\"20%\">\n      <el-avatar :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" shape=\"square\" :size=\"100\"\n        :fit=\"fit\"></el-avatar>\n      <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n    </el-form-item>\n\n    <el-form-item>\n      <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">保 存</el-button>\n    </el-form-item>\n  </el-form>\n  <el-dialog v-model=\"uploadVisible\" title=\"附件上传\" custom-class=\"el-dialog-widthSmall\" @close=\"closeDialog\">\n    <div>\n      <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n    </div>\n    <el-upload action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n      style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\" drag :limit=\"1\"\n      :on-preview=\"handlePreview\" :on-remove=\"handleRemove\" :file-list=\"fileList\" :on-exceed=\"handleExceed\"\n      :auto-upload=\"false\" name=\"file\" :on-change=\"fileListChange\">\n      <i class=\"el-icon-upload\"></i>\n      <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n      <div class=\"el-upload__tip\">\n        <div style=\"display: inline; color: #d70000; font-size: 14px\" class=\"uploadFileWarning\" id=\"uploadFileWarning\">\n        </div>\n      </div>\n    </el-upload>\n    <span class=\"dialog-footer\">\n      <el-button @click=\"hideUpload\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n    </span>\n  </el-dialog>\n</template>\n<script>\n  import request, { base } from '../../../utils/http';\n  export default {\n    name: 'Sinfo',\n    data() {\n      return {\n        formData: {},\n        professionalsList: [], // 专业列表\n\n        rules: {\n          sno: [{ required: true, message: '请输入账号', trigger: 'blur' }],\n          sname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\n          gender: [{ required: true, message: '请输入性别', trigger: 'blur' }],\n          phone: [\n            { required: true, message: '请输入手机号码', trigger: 'blur' },\n            { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\n          ],\n          proid: [{ required: true, message: '请选择专业', trigger: 'onchange' }],\n          spic: [{ required: true, message: '请上传照片', trigger: 'blur' }],\n          sflag: [{ required: true, message: '请输入就业状态', trigger: 'blur' }],\n        },\n\n        btnLoading: false, //按钮是否在加载中\n        uploadVisible: false, //上传弹出框\n      };\n    },\n    created() {\n      this.getinfo();\n      this.getProfessionals();\n    },\n    methods: {\n      //得到用户信息\n      getinfo() {\n        var lname = sessionStorage.getItem('lname');\n        let url = base + '/students/get?id=' + lname; //请求地址\n        request.post(url).then((res) => {\n          //请求接口\n          if (res.code == 200) {\n            this.formData = res.resdata;\n          } else {\n            this.$message({\n              message: '服务器错误',\n              type: 'error',\n              offset: 320,\n            });\n          }\n        });\n      },\n\n      //注册\n      save() {\n        //表单验证\n        this.$refs['formDataRef'].validate((valid) => {\n          if (valid) {\n            var lname = sessionStorage.getItem('lname');\n            let url = base + '/students/update?sno=' + lname; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => {\n              //请求接口\n              if (res.code == 200) {\n                this.$message({\n                  message: '操作成功！',\n                  type: 'success',\n                  offset: 320,\n                });\n              } else {\n                this.$message({\n                  message: '服务器错误',\n                  type: 'error',\n                  offset: 320,\n                });\n              }\n            });\n          }\n        });\n      },\n      //显示上传框\n      showUpload() {\n        this.uploadVisible = true;\n      },\n\n      //隐藏上传框\n      hideUpload() {\n        this.uploadVisible = false;\n      },\n      //上传\n      handleRemove(file, fileList) {\n        this.fileList = fileList;\n      },\n      handlePreview(file) {\n        console.log(file);\n      },\n      handleExceed(files, fileList) {\n        this.$message({\n          duration: 1000,\n          message: '只能上传一个文件',\n          type: 'error',\n          offset: 320,\n        });\n      },\n      // 判断上传文件后缀\n      fileListChange(file, fileList) {\n        let extendFileName = 'png,jpg';\n        let extendFileNames = extendFileName.split(',');\n        let regExpRules = [];\n        for (let i = 0; i < extendFileNames.length; i++) {\n          regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n        }\n        let fileNames = [];\n        let files = [];\n        let that = this;\n        fileList.forEach(function (key, val) {\n          let ret = false;\n          for (let i = 0; i < regExpRules.length; i++) {\n            ret = ret || regExpRules[i].test(key['name']);\n          }\n          if (!ret) {\n            console.log(key['name'] + ':' + ret);\n            that.$message({\n              duration: 1000,\n              message: '上传的文件后缀必须为' + extendFileName + '格式！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          if (fileNames.indexOf(key['name']) != -1) {\n            that.$message({\n              duration: 1000,\n              message: '上传的文件重复！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          //只能上传一个文件，用最后上传的覆盖\n          if (!that.multiFiles) {\n            files = [];\n            fileNames = [];\n          }\n          files.push(key);\n          fileNames.push(key['name']);\n          if (fileNames !== '') {\n            // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n          }\n          //$(\".uploadFileWarning\").text(\"\");\n        });\n        this.files = fileNames;\n        this.fileList = files;\n      },\n      /**\n       * 确认按钮\n       */\n      handleConfirm() {\n        let filePath = this.fileList;\n        if (filePath.length === 0) {\n          this.$message({\n            duration: 1000,\n            message: '请选择文件！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        let formData = new FormData();\n        this.fileList.forEach((file) => {\n          formData.append('file', file.raw, file.raw.name);\n        });\n        let url = base + '/common/uploadFile';\n        console.log('url=' + url);\n        request.post(url, formData).then((res) => {\n          console.log(res);\n          let furl = res.resdata.filePath;\n          this.formData.spic = furl; // 上传文件的路径\n          this.hideUpload();\n          console.log(res);\n        });\n      },\n\n      // 获取专业列表\n      getProfessionals() {\n        let url = base + '/professionals/list';\n        request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\n          if (res.code == 200) {\n            this.professionalsList = res.resdata;\n          }\n        });\n      },\n    },\n  };\n</script>\n\n<style></style>"], "mappings": ";;;AAuDE,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,CAAC,CAAC;MACZC,iBAAiB,EAAE,EAAE;MAAE;;MAEvBC,KAAK,EAAE;QACLC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC5DC,KAAK,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC9DE,MAAM,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC/DG,KAAK,EAAE,CACL;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACvD;UAAEI,OAAO,EAAE,mBAAmB;UAAEL,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CACxE;QACDK,KAAK,EAAE,CAAC;UAAEP,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAW,CAAC,CAAC;QAClEM,IAAI,EAAE,CAAC;UAAER,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC7DO,KAAK,EAAE,CAAC;UAAET,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MACjE,CAAC;MAEDQ,UAAU,EAAE,KAAK;MAAE;MACnBC,aAAa,EAAE,KAAK,CAAE;IACxB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,OAAO,CAAC,CAAC;IACd,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EACDC,OAAO,EAAE;IACP;IACAF,OAAOA,CAAA,EAAG;MACR,IAAIG,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIC,GAAE,GAAI1B,IAAG,GAAI,mBAAkB,GAAIuB,KAAK,EAAE;MAC9CxB,OAAO,CAAC4B,IAAI,CAACD,GAAG,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAC9B;QACA,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAAC3B,QAAO,GAAI0B,GAAG,CAACE,OAAO;QAC7B,OAAO;UACL,IAAI,CAACC,QAAQ,CAAC;YACZxB,OAAO,EAAE,OAAO;YAChByB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL;MACA,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAC5C,IAAIA,KAAK,EAAE;UACT,IAAIf,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAIC,GAAE,GAAI1B,IAAG,GAAI,uBAAsB,GAAIuB,KAAK,EAAE;UAClD,IAAI,CAACN,UAAS,GAAI,IAAI,EAAE;UACxBlB,OAAO,CAAC4B,IAAI,CAACD,GAAG,EAAE,IAAI,CAACvB,QAAQ,CAAC,CAACyB,IAAI,CAAEC,GAAG,IAAK;YAC7C;YACA,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACE,QAAQ,CAAC;gBACZxB,OAAO,EAAE,OAAO;gBAChByB,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACF,QAAQ,CAAC;gBACZxB,OAAO,EAAE,OAAO;gBAChByB,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IACD;IACAK,UAAUA,CAAA,EAAG;MACX,IAAI,CAACrB,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACAsB,UAAUA,CAAA,EAAG;MACX,IAAI,CAACtB,aAAY,GAAI,KAAK;IAC5B,CAAC;IACD;IACAuB,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAO,GAAIA,QAAQ;IAC1B,CAAC;IACDC,aAAaA,CAACF,IAAI,EAAE;MAClBG,OAAO,CAACC,GAAG,CAACJ,IAAI,CAAC;IACnB,CAAC;IACDK,YAAYA,CAACC,KAAK,EAAEL,QAAQ,EAAE;MAC5B,IAAI,CAACX,QAAQ,CAAC;QACZiB,QAAQ,EAAE,IAAI;QACdzC,OAAO,EAAE,UAAU;QACnByB,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD;IACAgB,cAAcA,CAACR,IAAI,EAAEC,QAAQ,EAAE;MAC7B,IAAIQ,cAAa,GAAI,SAAS;MAC9B,IAAIC,eAAc,GAAID,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC;MAC/C,IAAIC,WAAU,GAAI,EAAE;MACpB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,eAAe,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/CD,WAAW,CAACG,IAAI,CAAC,IAAIC,MAAM,CAAC,QAAO,GAAIN,eAAe,CAACG,CAAC,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC;MAC3E;MACA,IAAII,SAAQ,GAAI,EAAE;MAClB,IAAIX,KAAI,GAAI,EAAE;MACd,IAAIY,IAAG,GAAI,IAAI;MACfjB,QAAQ,CAACkB,OAAO,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnC,IAAIC,GAAE,GAAI,KAAK;QACf,KAAK,IAAIT,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3CS,GAAE,GAAIA,GAAE,IAAKV,WAAW,CAACC,CAAC,CAAC,CAACU,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C;QACA,IAAI,CAACE,GAAG,EAAE;UACRnB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAC,MAAM,IAAI,GAAE,GAAIE,GAAG,CAAC;UACpCJ,IAAI,CAAC5B,QAAQ,CAAC;YACZiB,QAAQ,EAAE,IAAI;YACdzC,OAAO,EAAE,YAAW,GAAI2C,cAAa,GAAI,KAAK;YAC9ClB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA,IAAIyB,SAAS,CAACO,OAAO,CAACJ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;UACxCF,IAAI,CAAC5B,QAAQ,CAAC;YACZiB,QAAQ,EAAE,IAAI;YACdzC,OAAO,EAAE,UAAU;YACnByB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA;QACA,IAAI,CAAC0B,IAAI,CAACO,UAAU,EAAE;UACpBnB,KAAI,GAAI,EAAE;UACVW,SAAQ,GAAI,EAAE;QAChB;QACAX,KAAK,CAACS,IAAI,CAACK,GAAG,CAAC;QACfH,SAAS,CAACF,IAAI,CAACK,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAIH,SAAQ,KAAM,EAAE,EAAE;UACpB;QAAA;QAEF;MACF,CAAC,CAAC;MACF,IAAI,CAACX,KAAI,GAAIW,SAAS;MACtB,IAAI,CAAChB,QAAO,GAAIK,KAAK;IACvB,CAAC;IACD;;;IAGAoB,aAAaA,CAAA,EAAG;MACd,IAAIC,QAAO,GAAI,IAAI,CAAC1B,QAAQ;MAC5B,IAAI0B,QAAQ,CAACb,MAAK,KAAM,CAAC,EAAE;QACzB,IAAI,CAACxB,QAAQ,CAAC;UACZiB,QAAQ,EAAE,IAAI;UACdzC,OAAO,EAAE,QAAQ;UACjByB,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MACA,IAAI/B,QAAO,GAAI,IAAImE,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC3B,QAAQ,CAACkB,OAAO,CAAEnB,IAAI,IAAK;QAC9BvC,QAAQ,CAACoE,MAAM,CAAC,MAAM,EAAE7B,IAAI,CAAC8B,GAAG,EAAE9B,IAAI,CAAC8B,GAAG,CAACvE,IAAI,CAAC;MAClD,CAAC,CAAC;MACF,IAAIyB,GAAE,GAAI1B,IAAG,GAAI,oBAAoB;MACrC6C,OAAO,CAACC,GAAG,CAAC,MAAK,GAAIpB,GAAG,CAAC;MACzB3B,OAAO,CAAC4B,IAAI,CAACD,GAAG,EAAEvB,QAAQ,CAAC,CAACyB,IAAI,CAAEC,GAAG,IAAK;QACxCgB,OAAO,CAACC,GAAG,CAACjB,GAAG,CAAC;QAChB,IAAI4C,IAAG,GAAI5C,GAAG,CAACE,OAAO,CAACsC,QAAQ;QAC/B,IAAI,CAAClE,QAAQ,CAACY,IAAG,GAAI0D,IAAI,EAAE;QAC3B,IAAI,CAACjC,UAAU,CAAC,CAAC;QACjBK,OAAO,CAACC,GAAG,CAACjB,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC;IAED;IACAR,gBAAgBA,CAAA,EAAG;MACjB,IAAIK,GAAE,GAAI1B,IAAG,GAAI,qBAAqB;MACtCD,OAAO,CAAC4B,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,EAAE;QAAEgD,MAAM,EAAE;UAAEC,WAAW,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAI;MAAE,CAAC,CAAC,CAAChD,IAAI,CAAEC,GAAG,IAAK;QACjF,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAAC1B,iBAAgB,GAAIyB,GAAG,CAACE,OAAO;QACtC;MACF,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}