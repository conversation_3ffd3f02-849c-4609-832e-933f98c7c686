{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1741615349295}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\Home.vue"], "names": [], "mappings": ";EAyHE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEtF,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;YACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC;UACH;;UAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;UAEnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,EAAE,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C;UACF,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D;MACF,CAAC;IACH,CAAC;EACH,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">后台首页</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">欢迎页面</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">欢迎页面</h4>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n      <!-- 管理员显示4个统计数据 -->\r\n      <template v-if=\"role === '管理员'\">\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-pink\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-user widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Students\">求职者总数</h6>\r\n              <h2 class=\"my-2\">{{ studentCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">注册求职者总人数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-purple\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-office-building widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Companies\">企业总数</h6>\r\n              <h2 class=\"my-2\">{{ companyCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">企业总数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-info\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-suitcase widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Jobs\">职位总数</h6>\r\n              <h2 class=\"my-2\">{{ jobCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">发布职位总数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-primary\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-document widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Resumes\">简历投递数</h6>\r\n              <h2 class=\"my-2\">{{ resumeCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">简历投递总数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 企业显示2个统计数据 -->\r\n      <template v-if=\"role === '企业'\">\r\n        <div class=\"col-xxl-6 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-info\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-suitcase widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Jobs\">发布职位</h6>\r\n              <h2 class=\"my-2\">{{ companyJobCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">已发布职位数量</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-6 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-primary\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-document widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Resumes\">收到简历</h6>\r\n              <h2 class=\"my-2\">{{ companyResumeCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">收到简历投递数量</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div style=\"width: 100%; line-height: 30px; text-align: center; padding: 100px\">\r\n        账号：<b style=\"color: red\">{{ userLname }}</b>， 身份：<b style=\"color: red\">{{ role }}</b><br />\r\n        您好，欢迎使用求职系统！<br />\r\n        请在左侧菜单中选择您要进行的操作！\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import axios from 'axios';\r\n  import { ElMessage } from 'element-plus';\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    data() {\r\n      return {\r\n        userLname: '',\r\n        role: '',\r\n        // 管理员统计数据\r\n        studentCount: 0,\r\n        companyCount: 0,\r\n        jobCount: 0,\r\n        resumeCount: 0,\r\n        // 企业统计数据\r\n        companyJobCount: 0,\r\n        companyResumeCount: 0,\r\n      };\r\n    },\r\n    mounted() {\r\n      this.userLname = sessionStorage.getItem('userLname');\r\n      this.role = sessionStorage.getItem('role');\r\n      this.getStatistics();\r\n    },\r\n    methods: {\r\n      async getStatistics() {\r\n        try {\r\n          const url = base + (this.role === '管理员' ? '/statistics/admin' : '/statistics/company');\r\n\r\n          let params = {};\r\n          if (this.role === '企业') {\r\n            var user = JSON.parse(sessionStorage.getItem('user'));\r\n            params = {\r\n              cid: user.cid,\r\n            };\r\n          }\r\n\r\n          const response = await request.get(url, { params });\r\n\r\n          if (response.code === 200) {\r\n            const data = response.data;\r\n            if (this.role === '管理员') {\r\n              this.studentCount = data.studentCount;\r\n              this.companyCount = data.companyCount;\r\n              this.jobCount = data.jobCount;\r\n              this.resumeCount = data.resumeCount;\r\n            } else {\r\n              this.companyJobCount = data.jobCount;\r\n              this.companyResumeCount = data.resumeCount;\r\n            }\r\n          } else {\r\n            ElMessage.error(response.data.msg || '获取统计数据失败');\r\n          }\r\n        } catch (error) {\r\n          console.error('获取统计数据失败:', error);\r\n          ElMessage.error(error.response?.data?.msg || '获取统计数据失败');\r\n        }\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .widget-icon {\r\n    font-size: 24px;\r\n    background: rgba(255, 255, 255, 0.1);\r\n    height: 40px;\r\n    width: 40px;\r\n    text-align: center;\r\n    line-height: 40px;\r\n    border-radius: 3px;\r\n    display: inline-block;\r\n  }\r\n</style>"]}]}