{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsAdd.vue", "mtime": 1741615869026}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\students\\StudentsAdd.vue"], "names": [], "mappings": ";EA0EE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACvD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACzE,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;QAE/D,CAAC;MACH,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC;cACJ,EAAE,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC;cACJ;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC;MACD,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACb,CAAC,CAAC;MACJ,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E;QACA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACnC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAC3C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C;UACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACb,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACd;UACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACb,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACd;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UAChB;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC;MACD,CAAC,CAAC;OACD,EAAE,CAAC,CAAC,CAAC;OACL,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACb,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd;QACA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC;QACF,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC;IACH,CAAC;EACH,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/students/StudentsAdd.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">求职者管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">添加求职者</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">添加求职者</h4>\n      </div>\n    </div>\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n      <el-form-item label=\"账号\" prop=\"sno\">\n        <el-input v-model=\"formData.sno\" placeholder=\"账号\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"密码\" prop=\"password\">\n        <el-input v-model=\"formData.password\" placeholder=\"密码\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"sname\">\n        <el-input v-model=\"formData.sname\" placeholder=\"姓名\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"formData.gender\">\n          <el-radio label=\"男\"> 男 </el-radio>\n          <el-radio label=\"女\"> 女 </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"年龄\" prop=\"age\">\n        <el-input v-model=\"formData.age\" placeholder=\"年龄\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"手机号码\" prop=\"phone\">\n        <el-input v-model=\"formData.phone\" placeholder=\"手机号码\" style=\"width: 50%\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"专业\" prop=\"proid\">\n        <el-select v-model=\"formData.proid\" placeholder=\"请选择\" size=\"small\">\n          <el-option v-for=\"item in professionalsList\" :key=\"item.proid\" :label=\"item.proname\"\n            :value=\"item.proid\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item prop=\"spic\" label=\"照片\" min-width=\"20%\">\n        <el-input v-model=\"formData.spic\" placeholder=\"照片\" readonly=\"true\" style=\"width: 50%\"></el-input>\n        <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\n        <el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\n      </el-form-item>\n    </el-form>\n    <el-dialog v-model=\"uploadVisible\" title=\"附件上传\" custom-class=\"el-dialog-widthSmall\" @close=\"closeDialog\">\n      <div>\n        <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n      </div>\n      <el-upload action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n        style=\"margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px\" drag :limit=\"1\"\n        :on-preview=\"handlePreview\" :on-remove=\"handleRemove\" :file-list=\"fileList\" :on-exceed=\"handleExceed\"\n        :auto-upload=\"false\" name=\"file\" :on-change=\"fileListChange\">\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将数据文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\">\n          <div style=\"display: inline; color: #d70000; font-size: 14px\" class=\"uploadFileWarning\"\n            id=\"uploadFileWarning\"></div>\n        </div>\n      </el-upload>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"hideUpload\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n<script>\n  import request, { base } from '../../../../utils/http';\n\n  export default {\n    name: 'StudentsAdd',\n    components: {},\n    data() {\n      return {\n        uploadVisible: false,\n        btnLoading: false, //保存按钮加载状态\n        formData: {}, //表单数据\n        addrules: {\n          sno: [{ required: true, message: '请输入账号', trigger: 'blur' }],\n          password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\n          sname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\n          gender: [{ required: true, message: '请输入性别', trigger: 'blur' }],\n          phone: [\n            { required: true, message: '请输入手机号码', trigger: 'blur' },\n            { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\n          ],\n          proid: [{ required: true, message: '请选择专业', trigger: 'onchange' }],\n          spic: [{ required: true, message: '请输入照片', trigger: 'blur' }],\n         \n        },\n      };\n    },\n    mounted() {\n      this.getprofessionalsList();\n    },\n\n    methods: {\n      // 添加\n      save() {\n        this.$refs['formDataRef'].validate((valid) => {\n          //验证表单\n          if (valid) {\n            let url = base + '/students/add';\n            this.btnLoading = true;\n \n            request.post(url, this.formData).then((res) => {\n              //发送请求\n              if (res.code == 200) {\n                this.$message({\n                  message: '操作成功',\n                  type: 'success',\n                  offset: 320,\n                });\n                this.$router.push({\n                  path: '/StudentsManage',\n                });\n              } else {\n                this.$message({\n                  message: res.msg,\n                  type: 'error',\n                  offset: 320,\n                });\n              }\n              this.btnLoading = false;\n            });\n          }\n        });\n      },\n\n      // 返回\n      goBack() {\n        this.$router.push({\n          path: '/StudentsManage',\n        });\n      },\n\n      getprofessionalsList() {\n        let para = {};\n        this.listLoading = true;\n        let url = base + '/professionals/list?currentPage=1&pageSize=1000';\n        request.post(url, para).then((res) => {\n          this.professionalsList = res.resdata;\n        });\n      },\n\n      //显示上传框\n      showUpload() {\n        this.uploadVisible = true;\n      },\n\n      //隐藏上传框\n      hideUpload() {\n        this.uploadVisible = false;\n      },\n      //上传\n      handleRemove(file, fileList) {\n        this.fileList = fileList;\n      },\n      handlePreview(file) {\n        console.log(file);\n      },\n      handleExceed(files, fileList) {\n        this.$message({\n          duration: 1000,\n          message: '只能上传一个文件',\n          type: 'error',\n          offset: 320,\n        });\n      },\n      // 判断上传文件后缀\n      fileListChange(file, fileList) {\n        let extendFileName = 'png,jpg';\n        let extendFileNames = extendFileName.split(',');\n        let regExpRules = [];\n        for (let i = 0; i < extendFileNames.length; i++) {\n          regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));\n        }\n        let fileNames = [];\n        let files = [];\n        let that = this;\n        fileList.forEach(function (key, val) {\n          let ret = false;\n          for (let i = 0; i < regExpRules.length; i++) {\n            ret = ret || regExpRules[i].test(key['name']);\n          }\n          if (!ret) {\n            console.log(key['name'] + ':' + ret);\n            that.$message({\n              duration: 1000,\n              message: '上传的文件后缀必须为' + extendFileName + '格式！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          if (fileNames.indexOf(key['name']) != -1) {\n            that.$message({\n              duration: 1000,\n              message: '上传的文件重复！',\n              type: 'error',\n              offset: 320,\n            });\n            return false;\n          }\n          //只能上传一个文件，用最后上传的覆盖\n          if (!that.multiFiles) {\n            files = [];\n            fileNames = [];\n          }\n          files.push(key);\n          fileNames.push(key['name']);\n          if (fileNames !== '') {\n            // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n          }\n          //$(\".uploadFileWarning\").text(\"\");\n        });\n        this.files = fileNames;\n        this.fileList = files;\n      },\n      /**\n       * 确认按钮\n       */\n      handleConfirm() {\n        let filePath = this.fileList;\n        if (filePath.length === 0) {\n          this.$message({\n            duration: 1000,\n            message: '请选择文件！',\n            type: 'error',\n            offset: 320,\n          });\n          return false;\n        }\n        let formData = new FormData();\n        this.fileList.forEach((file) => {\n          formData.append('file', file.raw, file.raw.name);\n        });\n        let url = base + '/common/uploadFile';\n        console.log('url=' + url);\n        request.post(url, formData).then((res) => {\n          console.log(res);\n          let furl = res.resdata.filePath;\n          this.formData.spic = furl; // 上传文件的路径\n          this.hideUpload();\n          console.log(res);\n        });\n      },\n    },\n  };\n</script>\n<style scoped></style>"]}]}