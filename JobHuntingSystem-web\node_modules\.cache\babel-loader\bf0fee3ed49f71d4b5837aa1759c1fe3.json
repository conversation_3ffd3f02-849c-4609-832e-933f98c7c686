{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\bbs\\BbsDetail.vue?vue&type=template&id=38f88a7e", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\bbs\\BbsDetail.vue", "mtime": 1741614414000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "align", "_component_el_form_item", "label", "bid", "btitle", "prop", "_createElementVNode", "innerHTML", "bdetail", "btotal", "sno", "addtime", "_component_el_button", "type", "size", "onClick", "$options", "back", "icon", "_cache"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\bbs\\BbsDetail.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">帖子管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">帖子详情</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">帖子详情</h4>\r\n      </div>\r\n    </div>\r\n     <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n<el-form-item label=\"帖子ID\">\r\n{{formData.bid}}</el-form-item>\r\n<el-form-item label=\"标题\">\r\n{{formData.btitle}}</el-form-item>\r\n<el-form-item label=\"内容\" prop=\"bdetail\">\r\n<div v-html=\"formData.bdetail\"></div>\r\n</el-form-item>\r\n<el-form-item label=\"浏览量\">\r\n{{formData.btotal}}</el-form-item>\r\n<el-form-item label=\"发布人\">\r\n{{formData.sno}}</el-form-item>\r\n<el-form-item label=\"发布时间\">\r\n{{formData.addtime}}</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\n        \n        import request, { base } from \"../../../../utils/http\";\n        export default {\n            name: 'BbsDetail',\n            components: {\n            },\n            data() {\n                return {\n                    id: '',\n                    formData: {}, //表单数据         \n        \n                };\n            },\n            created() {\n                this.id = this.$route.query.id; //获取参数\n                this.getDatas();\n            },\n        \n        \n            methods: {\n        \n                //获取列表数据\n                getDatas() {\n                    let para = {\n                    };\n                    this.listLoading = true;\n                    let url = base + \"/bbs/get?id=\" + this.id;\n                    request.post(url, para).then((res) => {\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\n                        this.listLoading = false;\n                    });\n                },\n        \n                // 返回\n                back() {\n                    //返回上一页\n                    this.$router.go(-1);\n                },\n        \n            },\n        }\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EAEOA,KAAK,EAAC;AAAK;;;;;;uBAAhBC,mBAAA,CAgCM,OAhCNC,UAgCM,G,sWApBHC,YAAA,CAiBKC,kBAAA;IAjBKC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,KAAK,EAAC;;sBACxD,MAC+B,CAD/BL,YAAA,CAC+BM,uBAAA;MADjBC,KAAK,EAAC;IAAM;wBAC1B,MAAgB,C,kCAAdJ,KAAA,CAAAC,QAAQ,CAACI,GAAG,iB;;QACdR,YAAA,CACkCM,uBAAA;MADpBC,KAAK,EAAC;IAAI;wBACxB,MAAmB,C,kCAAjBJ,KAAA,CAAAC,QAAQ,CAACK,MAAM,iB;;QACjBT,YAAA,CAEeM,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACG,IAAI,EAAC;;wBAC9B,MAAqC,CAArCC,mBAAA,CAAqC;QAAhCC,SAAyB,EAAjBT,KAAA,CAAAC,QAAQ,CAACS;;;QAEtBb,YAAA,CACkCM,uBAAA;MADpBC,KAAK,EAAC;IAAK;wBACzB,MAAmB,C,kCAAjBJ,KAAA,CAAAC,QAAQ,CAACU,MAAM,iB;;QACjBd,YAAA,CAC+BM,uBAAA;MADjBC,KAAK,EAAC;IAAK;wBACzB,MAAgB,C,kCAAdJ,KAAA,CAAAC,QAAQ,CAACW,GAAG,iB;;QACdf,YAAA,CACmCM,uBAAA;MADrBC,KAAK,EAAC;IAAM;wBAC1B,MAAoB,C,kCAAlBJ,KAAA,CAAAC,QAAQ,CAACY,OAAO,iB;;QAClBhB,YAAA,CAEeM,uBAAA;wBADf,MAAqF,CAArFN,YAAA,CAAqFiB,oBAAA;QAA1EC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC,IAAI;QAAEC,IAAI,EAAC;;0BAAe,MAAGC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E", "ignoreList": []}]}