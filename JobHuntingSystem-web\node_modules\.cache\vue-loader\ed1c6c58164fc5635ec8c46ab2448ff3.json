{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Index.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Index.vue", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgVG9wTWVudSBmcm9tICIuLi9jb21wb25lbnRzL1RvcE1lbnUiOw0KaW1wb3J0IEZvb3QgZnJvbSAiLi4vY29tcG9uZW50cy9Gb290IjsNCmltcG9ydCB7IEVsQ29uZmlnUHJvdmlkZXIgfSBmcm9tICJlbGVtZW50LXBsdXMiOw0KDQoNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiSW5kZXhMYXlvdXQiLA0KICBjb21wb25lbnRzOiB7DQogICAgVG9wTWVudSwNCiAgICBGb290LA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQoNCiAgICB9Ow0KICB9LA0KDQogIHNldHVwKCkgew0KDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Index.vue"], "names": [], "mappings": ";AASA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;AAI/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC;EACN,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAER,CAAC;AACH,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/Index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <TopMenu />\r\n  </div>\r\n  <router-view />\r\n  <Foot />\r\n</template>\r\n\r\n<script>\r\nimport TopMenu from \"../components/TopMenu\";\r\nimport Foot from \"../components/Foot\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\n\r\n\r\n\r\nexport default {\r\n  name: \"IndexLayout\",\r\n  components: {\r\n    TopMenu,\r\n    Foot,\r\n  },\r\n  data() {\r\n    return {\r\n\r\n    };\r\n  },\r\n\r\n  setup() {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n"]}]}