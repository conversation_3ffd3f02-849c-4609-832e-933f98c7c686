{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue?vue&type=template&id=26cae6d4&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue", "mtime": 1741617188960}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "src", "$data", "formData", "spic", "_hoisted_6", "_hoisted_7", "_toDisplayString", "sname", "_hoisted_8", "_hoisted_9", "gender", "_hoisted_10", "age", "_hoisted_11", "phone", "_hoisted_12", "professionalName", "_hoisted_13", "_hoisted_14", "resumeData", "_hoisted_15", "_hoisted_16", "innerHTML", "education", "_hoisted_18", "parttimejob", "_hoisted_20", "introduction", "_hoisted_22", "_createVNode", "_component_el_empty", "description", "_component_el_button", "type", "onClick", "$options", "goToAddResume", "_cache"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue"], "sourcesContent": ["<template>\r\n  <div class=\"resume-preview\">\r\n    <div class=\"resume-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"avatar-section\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" class=\"avatar\" />\r\n        </div>\r\n        <div class=\"basic-info\">\r\n          <h1 class=\"name\">{{ formData.sname }}</h1>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>性别：{{ formData.gender }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-date\"></i>\r\n              <span>年龄：{{ formData.age }}岁</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-phone\"></i>\r\n              <span>电话：{{ formData.phone }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-school\"></i>\r\n              <span>专业：{{ professionalName }}</span>\r\n            </div>\r\n          \r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"resume-body\">\r\n      <div class=\"resume-section\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          简历信息\r\n        </h2>\r\n        <div class=\"resume-content\" v-if=\"resumeData\">\r\n          <div class=\"info-row\">\r\n            <label>教育背景：</label>\r\n            <div class=\"content\" v-html=\"resumeData.education\"></div>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <label>实习经历：</label>\r\n            <div class=\"content\" v-html=\"resumeData.parttimejob\"></div>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <label>个人介绍：</label>\r\n            <div class=\"content\" v-html=\"resumeData.introduction\"></div>\r\n          </div>\r\n        </div>\r\n        <div class=\"no-resume\" v-else>\r\n          <el-empty description=\"暂无简历信息\">\r\n            <el-button type=\"primary\" @click=\"goToAddResume\">创建简历</el-button>\r\n          </el-empty>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    name: 'ResumePreview',\r\n    data() {\r\n      return {\r\n        formData: {},\r\n        resumeData: null,\r\n        professionalsList: [],\r\n        professionalName: '',\r\n      };\r\n    },\r\n    created() {\r\n      this.getUserInfo();\r\n      this.getProfessionals();\r\n      this.getResumeInfo();\r\n    },\r\n    methods: {\r\n      // 获取用户信息\r\n      getUserInfo() {\r\n        let lname = sessionStorage.getItem('lname');\r\n        let url = base + '/students/get?id=' + lname;\r\n        request.post(url).then((res) => {\r\n          if (res.code == 200) {\r\n            this.formData = res.resdata;\r\n            this.getProfessionalName();\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取专业列表\r\n      getProfessionals() {\r\n        let url = base + '/professionals/list';\r\n        request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n          if (res.code == 200) {\r\n            this.professionalsList = res.resdata;\r\n            this.getProfessionalName();\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取专业名称\r\n      getProfessionalName() {\r\n        if (this.formData.proid && this.professionalsList.length > 0) {\r\n          const professional = this.professionalsList.find((p) => p.proid === this.formData.proid);\r\n          this.professionalName = professional ? professional.proname : '';\r\n        }\r\n      },\r\n\r\n      // 获取简历信息\r\n      getResumeInfo() {\r\n        let lname = sessionStorage.getItem('lname');\r\n        let url = base + '/resume/list';\r\n        request.post(url, { sno: lname }, { params: { currentPage: 1, pageSize: 1 } }).then((res) => {\r\n          if (res.code == 200 && res.resdata.length > 0) {\r\n            this.resumeData = res.resdata[0];\r\n          }\r\n        });\r\n      },\r\n\r\n      // 跳转到创建简历页面\r\n      goToAddResume() {\r\n        this.$router.push('/Resume_Add');\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .resume-preview {\r\n    max-width: 1000px;\r\n    margin: 20px auto;\r\n    background: #fff;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    border-radius: 8px;\r\n  }\r\n\r\n  .resume-header {\r\n    background: linear-gradient(135deg, #3bc0c3 0%, #42d3d7 100%);\r\n    padding: 40px;\r\n    border-radius: 8px 8px 0 0;\r\n    color: #fff;\r\n  }\r\n\r\n  .header-content {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 40px;\r\n  }\r\n\r\n  .avatar-section {\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .avatar {\r\n    width: 150px;\r\n    height: 150px;\r\n    border-radius: 75px;\r\n    border: 4px solid rgba(255, 255, 255, 0.3);\r\n  }\r\n\r\n  .basic-info {\r\n    flex-grow: 1;\r\n  }\r\n\r\n  .name {\r\n    font-size: 28px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n    gap: 15px;\r\n  }\r\n\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .info-item i {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .resume-body {\r\n    padding: 40px;\r\n  }\r\n\r\n  .resume-section {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 20px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .section-title i {\r\n    color: #3bc0c3;\r\n  }\r\n\r\n  .info-row {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-row label {\r\n    font-weight: bold;\r\n    color: #666;\r\n    display: block;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .content {\r\n    line-height: 1.6;\r\n    color: #333;\r\n  }\r\n\r\n  .status-employed {\r\n    color: #67c23a;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .status-unemployed {\r\n    color: #f56c6c;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .no-resume {\r\n    text-align: center;\r\n    padding: 40px 0;\r\n  }\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAgB;;;EAGtBA,KAAK,EAAC;AAAY;;EACjBA,KAAK,EAAC;AAAM;;EACXA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAUzBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAgB;;;EAKpBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAU;;;EAIhBA,KAAK,EAAC;AAAU;;;EAIhBA,KAAK,EAAC;AAAU;;;;EAKlBA,KAAK,EAAC;;;;;uBAnDjBC,mBAAA,CA0DM,OA1DNC,UA0DM,GAzDJC,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJD,mBAAA,CA0BM,OA1BNE,UA0BM,GAzBJF,mBAAA,CAEM,OAFNG,UAEM,GADJH,mBAAA,CAAuF;IAAjFI,GAAG,8CAA8CC,KAAA,CAAAC,QAAQ,CAACC,IAAI;IAAEV,KAAK,EAAC;yCAE9EG,mBAAA,CAqBM,OArBNQ,UAqBM,GApBJR,mBAAA,CAA0C,MAA1CS,UAA0C,EAAAC,gBAAA,CAAtBL,KAAA,CAAAC,QAAQ,CAACK,KAAK,kBAClCX,mBAAA,CAkBM,OAlBNY,UAkBM,GAjBJZ,mBAAA,CAGM,OAHNa,UAGM,G,0BAFJb,mBAAA,CAA4B;IAAzBH,KAAK,EAAC;EAAc,6BACvBG,mBAAA,CAAqC,cAA/B,KAAG,GAAAU,gBAAA,CAAGL,KAAA,CAAAC,QAAQ,CAACQ,MAAM,iB,GAE7Bd,mBAAA,CAGM,OAHNe,WAGM,G,0BAFJf,mBAAA,CAA4B;IAAzBH,KAAK,EAAC;EAAc,6BACvBG,mBAAA,CAAmC,cAA7B,KAAG,GAAAU,gBAAA,CAAGL,KAAA,CAAAC,QAAQ,CAACU,GAAG,IAAG,GAAC,gB,GAE9BhB,mBAAA,CAGM,OAHNiB,WAGM,G,0BAFJjB,mBAAA,CAA6B;IAA1BH,KAAK,EAAC;EAAe,6BACxBG,mBAAA,CAAoC,cAA9B,KAAG,GAAAU,gBAAA,CAAGL,KAAA,CAAAC,QAAQ,CAACY,KAAK,iB,GAE5BlB,mBAAA,CAGM,OAHNmB,WAGM,G,0BAFJnB,mBAAA,CAA8B;IAA3BH,KAAK,EAAC;EAAgB,6BACzBG,mBAAA,CAAsC,cAAhC,KAAG,GAAAU,gBAAA,CAAGL,KAAA,CAAAe,gBAAgB,iB,WAQtCpB,mBAAA,CA0BM,OA1BNqB,WA0BM,GAzBJrB,mBAAA,CAwBM,OAxBNsB,WAwBM,G,0BAvBJtB,mBAAA,CAGK;IAHDH,KAAK,EAAC;EAAe,IACvBG,mBAAA,CAAgC;IAA7BH,KAAK,EAAC;EAAkB,I,iBAAK,QAElC,E,sBACkCQ,KAAA,CAAAkB,UAAU,I,cAA5CzB,mBAAA,CAaM,OAbN0B,WAaM,GAZJxB,mBAAA,CAGM,OAHNyB,WAGM,G,0BAFJzB,mBAAA,CAAoB,eAAb,OAAK,sBACZA,mBAAA,CAAyD;IAApDH,KAAK,EAAC,SAAS;IAAC6B,SAA6B,EAArBrB,KAAA,CAAAkB,UAAU,CAACI;0CAE1C3B,mBAAA,CAGM,OAHN4B,WAGM,G,0BAFJ5B,mBAAA,CAAoB,eAAb,OAAK,sBACZA,mBAAA,CAA2D;IAAtDH,KAAK,EAAC,SAAS;IAAC6B,SAA+B,EAAvBrB,KAAA,CAAAkB,UAAU,CAACM;0CAE1C7B,mBAAA,CAGM,OAHN8B,WAGM,G,0BAFJ9B,mBAAA,CAAoB,eAAb,OAAK,sBACZA,mBAAA,CAA4D;IAAvDH,KAAK,EAAC,SAAS;IAAC6B,SAAgC,EAAxBrB,KAAA,CAAAkB,UAAU,CAACQ;6DAG5CjC,mBAAA,CAIM,OAJNkC,WAIM,GAHJC,YAAA,CAEWC,mBAAA;IAFDC,WAAW,EAAC;EAAQ;sBAC5B,MAAiE,CAAjEF,YAAA,CAAiEG,oBAAA;MAAtDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEC,QAAA,CAAAC;;wBAAe,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E", "ignoreList": []}]}