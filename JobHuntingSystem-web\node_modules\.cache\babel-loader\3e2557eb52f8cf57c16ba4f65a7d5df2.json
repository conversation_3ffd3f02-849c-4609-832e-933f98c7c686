{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Menunav.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Menunav.vue", "mtime": 1741536483000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uL3V0aWxzL2h0dHAiOwppbXBvcnQgTWVudSBmcm9tICIuLi8uLi9jb21wb25lbnRzL01lbnUiOwppbXBvcnQgVG9wTWVudSBmcm9tICIuLi8uLi9jb21wb25lbnRzL1RvcE1lbnUiOwppbXBvcnQgRm9vdCBmcm9tICIuLi8uLi9jb21wb25lbnRzL0Zvb3QiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIk1lbnVuYXYiLAogIGNvbXBvbmVudHM6IHsKICAgIFRvcE1lbnUsCiAgICBGb290LAogICAgTWVudQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9LAogIG1vdW50ZWQoKSB7fSwKICBjcmVhdGVkKCkge30sCiAgbWV0aG9kczoge30KfTs="}, {"version": 3, "names": ["request", "base", "<PERSON><PERSON>", "TopMenu", "Foot", "name", "components", "data", "mounted", "created", "methods"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Menunav.vue"], "sourcesContent": ["<template>\r\n\t<TopMenu />\r\n\r\n\t<div class=\"custom-breadcrumb custom-breadcrumb--bg\"\r\n\t\t:style=\"{ backgroundImage: 'url(' + require('@/assets/images/breadcrumb-bg.jpg') + ')' }\">\r\n\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"row\">\r\n\t\t\t\t<!-- page title -->\r\n\t\t\t\t<div class=\"col-md-6\">\r\n\t\t\t\t\t<h1 class=\"page-title\">\r\n\t\t\t\t\t\t{{ $route.meta.title }}\r\n\t\t\t\t\t</h1>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- breadcrumb -->\r\n\t\t\t\t<div class=\"col-md-6 breadcrumb-menu\">\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\r\n\t<div class=\"main-content py-120\" style=\"padding-top:30px;\">\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"row\">\r\n\r\n\t\t\t\t<Menu />\r\n\r\n\t\t\t\t<div class=\"col-lg-9 sidebar\">\r\n\t\t\t\t\t<div class=\"widget widget_archive\">\r\n\t\t\t\t\t\t<h3 class=\"widget-title\">\r\n\t\t\t\t\t\t\t{{ $route.meta.title }}\r\n\t\t\t\t\t\t</h3>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<!-- blog posts -->\r\n\t\t\t\t\t<div class=\"row\">\r\n\t\t\t\t\t\t<!-- blog post item -->\r\n\t\t\t\t\t\t<div class=\"col-12\">\r\n\r\n\t\t\t\t\t\t\t<div class=\"blog-post blog-post--list\">\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"blog-post__body\" style=\"padding-top:0px;\">\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t<div class=\"blog-post__body--content\">\r\n\t\t\t\t\t\t\t\t\t\t<router-view />\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<!-- end blog contents -->\r\n\t\t</div>\r\n\t</div>\r\n\r\n\r\n\r\n\t<Foot />\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport Menu from \"../../components/Menu\";\r\nimport TopMenu from \"../../components/TopMenu\";\r\nimport Foot from \"../../components/Foot\";\r\n\r\nexport default {\r\n\tname: \"Menunav\",\r\n\tcomponents: {\r\n\t\tTopMenu,\r\n\t\tFoot,\r\n\t\tMenu\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\r\n\t\t};\r\n\t},\r\n\tmounted() {\r\n\r\n\t},\r\n\tcreated() {\r\n\r\n\t},\r\n\tmethods: {\r\n\r\n\t},\r\n};\r\n</script>\r\n\r\n\r\n<style></style>\r\n"], "mappings": "AA+DA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,OAAOC,IAAG,MAAO,uBAAuB;AACxC,OAAOC,OAAM,MAAO,0BAA0B;AAC9C,OAAOC,IAAG,MAAO,uBAAuB;AAExC,eAAe;EACdC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE;IACXH,OAAO;IACPC,IAAI;IACJF;EACD,CAAC;EACDK,IAAIA,CAAA,EAAG;IACN,OAAO,CAEP,CAAC;EACF,CAAC;EACDC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAO,EAAE,CAET;AACD,CAAC", "ignoreList": []}]}