{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryManage.vue?vue&type=template&id=2d0e08ca&scoped=true", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryManage.vue", "mtime": 1741615349398}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_col", "span", "style", "_component_el_form", "inline", "model", "$data", "filters", "_component_el_form_item", "_component_el_input", "sno", "$event", "placeholder", "size", "_component_el_button", "type", "onClick", "$options", "query", "icon", "_cache", "_createBlock", "_component_el_table", "data", "datalist", "border", "stripe", "_component_el_table_column", "prop", "label", "align", "default", "_withCtx", "scope", "row", "auditreply", "_hoisted_2", "_toDisplayString", "substring", "handleShow", "$index", "previewResume", "rid", "handleDelete", "listLoading", "_component_el_pagination", "onCurrentChange", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount", "_createCommentVNode", "_component_el_dialog", "title", "resumeDialogVisible", "width", "resumeData", "_hoisted_3", "_createElementVNode", "_hoisted_4", "_hoisted_5", "_hoisted_6", "src", "studentInfo", "spic", "_hoisted_8", "_hoisted_9", "sname", "_hoisted_10", "_hoisted_11", "gender", "_hoisted_12", "age", "_hoisted_13", "phone", "_hoisted_14", "professionalName", "_hoisted_15", "_normalizeClass", "sflag", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "innerHTML", "education", "_hoisted_21", "parttimejob", "_hoisted_23", "introduction", "_hoisted_25", "_component_el_empty", "description"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryManage.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">简历投递管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">简历投递列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">简历投递列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.sno\" placeholder=\"账号\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"by1\" label=\"职位\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"sno\" label=\"账号\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"by2\" label=\"简历\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"submittime\" label=\"投递时间\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"auditstatus\" label=\"审核状态\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"auditreply\" label=\"审核回复\" align=\"center\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.auditreply != null\">{{\n            scope.row.auditreply.substring(0, 10)\n            }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\n          <el-button type=\"info\" size=\"mini\" @click=\"previewResume(scope.row.rid)\" icon=\"el-icon-document\"\n            style=\"padding: 3px 6px 3px 6px\">预览简历</el-button>\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n    <!-- 简历预览对话框 -->\n    <el-dialog title=\"简历预览\" v-model=\"resumeDialogVisible\" width=\"800px\">\n      <div v-if=\"resumeData\" class=\"resume-preview\">\n        <div class=\"resume-header\">\n          <div class=\"header-content\">\n            <div class=\"avatar-section\">\n              <img :src=\"'http://localhost:8088/JobHuntingSystem/' + studentInfo.spic\" class=\"avatar\" />\n            </div>\n            <div class=\"basic-info\">\n              <h1 class=\"name\">{{ studentInfo.sname }}</h1>\n              <div class=\"info-grid\">\n                <div class=\"info-item\">\n                  <i class=\"el-icon-user\"></i>\n                  <span>性别：{{ studentInfo.gender }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-date\"></i>\n                  <span>年龄：{{ studentInfo.age }}岁</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-phone\"></i>\n                  <span>电话：{{ studentInfo.phone }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-school\"></i>\n                  <span>专业：{{ professionalName }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-s-flag\"></i>\n                  <span>就业状态：\n                    <span :class=\"{\n                      'status-employed': studentInfo.sflag === '已就业',\n                      'status-unemployed': studentInfo.sflag === '未就业',\n                    }\">\n                      {{ studentInfo.sflag }}\n                    </span>\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"resume-body\">\n          <div class=\"resume-section\">\n            <h2 class=\"section-title\">\n              <i class=\"el-icon-document\"></i>\n              简历信息\n            </h2>\n            <div class=\"resume-content\">\n              <div class=\"info-row\">\n                <label>教育背景：</label>\n                <div class=\"content\" v-html=\"resumeData.education\"></div>\n              </div>\n              <div class=\"info-row\">\n                <label>实习经历：</label>\n                <div class=\"content\" v-html=\"resumeData.parttimejob\"></div>\n              </div>\n              <div class=\"info-row\">\n                <label>个人介绍：</label>\n                <div class=\"content\" v-html=\"resumeData.introduction\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div v-else class=\"resume-empty\">\n        <el-empty description=\"暂无简历信息\"></el-empty>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\n  import request, { base } from '../../../../utils/http';\n  export default {\n    name: 'resumedelivery',\n    components: {},\n    data() {\n      return {\n        filters: {\n          //列表查询参数\n          sno: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n        resumeDialogVisible: false,\n        resumeData: null,\n        studentInfo: {},\n        professionalsList: [],\n        professionalName: '',\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n    methods: {\n      // 删除简历投递\n      handleDelete(index, row) {\n        this.$confirm('确认删除该记录吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            this.listLoading = true;\n            let url = base + '/resumedelivery/del?id=' + row.delid;\n            request.post(url).then((res) => {\n              this.listLoading = false;\n\n              this.$message({\n                message: '删除成功',\n                type: 'success',\n                offset: 320,\n              });\n              this.getDatas();\n            });\n          })\n          .catch(() => { });\n      },\n\n      // 分页\n      handleCurrentChange(val) {\n        this.page.currentPage = val;\n        this.getDatas();\n      },\n\n      //获取列表数据\n      getDatas() {\n        let para = {\n          sno: this.filters.sno,\n        };\n        this.listLoading = true;\n        let url =\n          base +\n          '/resumedelivery/list?currentPage=' +\n          this.page.currentPage +\n          '&pageSize=' +\n          this.page.pageSize;\n        request.post(url, para).then((res) => {\n          if (res.resdata.length > 0) {\n            this.isPage = true;\n          } else {\n            this.isPage = false;\n          }\n          this.page.totalCount = res.count;\n          this.datalist = res.resdata;\n          this.listLoading = false;\n        });\n      },\n      //查询\n      query() {\n        this.getDatas();\n      },\n\n      // 查看\n      handleShow(index, row) {\n        this.$router.push({\n          path: '/ResumedeliveryDetail',\n          query: {\n            id: row.delid,\n          },\n        });\n      },\n\n      // 编辑\n      handleEdit(index, row) {\n        this.$router.push({\n          path: '/ResumedeliveryEdit',\n          query: {\n            id: row.delid,\n          },\n        });\n      },\n\n      // 预览简历\n      async previewResume(rid) {\n        try {\n          // 获取简历信息\n          const resumeRes = await request.post(base + '/resume/get?id=' + rid);\n          if (resumeRes.code === 200) {\n            this.resumeData = resumeRes.resdata;\n            // 获取求职者信息\n            const studentRes = await request.post(base + '/students/get?id=' + this.resumeData.sno);\n            if (studentRes.code === 200) {\n              this.studentInfo = studentRes.resdata;\n              // 获取专业信息\n              await this.getProfessionals();\n            }\n            this.resumeDialogVisible = true;\n          }\n        } catch (error) {\n          console.error('获取简历信息失败:', error);\n          this.$message({\n            message: '获取简历信息失败',\n            type: 'error',\n            offset: 320,\n          });\n        }\n      },\n\n      // 获取专业列表\n      async getProfessionals() {\n        try {\n          const res = await request.post(\n            base + '/professionals/list',\n            {},\n            { params: { currentPage: 1, pageSize: 100 } }\n          );\n          if (res.code === 200) {\n            this.professionalsList = res.resdata;\n            this.getProfessionalName();\n          }\n        } catch (error) {\n          console.error('获取专业列表失败:', error);\n        }\n      },\n\n      // 获取专业名称\n      getProfessionalName() {\n        if (this.studentInfo.proid && this.professionalsList.length > 0) {\n          const professional = this.professionalsList.find((p) => p.proid === this.studentInfo.proid);\n          this.professionalName = professional ? professional.proname : '';\n        }\n      },\n    },\n  };\n</script>\n<style scoped>\n  .resume-preview {\n    max-width: 800px;\n    margin: 0 auto;\n  }\n\n  .resume-header {\n    background: linear-gradient(135deg, #3bc0c3 0%, #42d3d7 100%);\n    padding: 40px;\n    border-radius: 8px 8px 0 0;\n    color: #fff;\n  }\n\n  .header-content {\n    display: flex;\n    gap: 30px;\n  }\n\n  .avatar-section {\n    width: 120px;\n    height: 120px;\n  }\n\n  .avatar {\n    width: 100%;\n    height: 100%;\n    border-radius: 60px;\n    object-fit: cover;\n    border: 4px solid rgba(255, 255, 255, 0.3);\n  }\n\n  .basic-info {\n    flex: 1;\n  }\n\n  .name {\n    font-size: 24px;\n    margin-bottom: 20px;\n  }\n\n  .info-grid {\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: 15px;\n  }\n\n  .info-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n\n  .resume-body {\n    background: #fff;\n    padding: 30px;\n    border-radius: 0 0 8px 8px;\n  }\n\n  .section-title {\n    font-size: 18px;\n    color: #333;\n    margin-bottom: 20px;\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n\n  .info-row {\n    margin-bottom: 20px;\n  }\n\n  .info-row label {\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 8px;\n    display: block;\n  }\n\n  .content {\n    color: #666;\n    line-height: 1.8;\n  }\n\n  .status-employed {\n    color: #67c23a;\n    font-weight: bold;\n  }\n\n  .status-unemployed {\n    color: #f56c6c;\n    font-weight: bold;\n  }\n\n  .resume-empty {\n    padding: 40px 0;\n    text-align: center;\n  }\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAK;;;;;;EAqDWA,KAAK,EAAC;;;EACtBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAgB;;;EAGtBA,KAAK,EAAC;AAAY;;EACjBA,KAAK,EAAC;AAAM;;EACXA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAgBzBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAgB;;EAKpBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAU;;;EAIhBA,KAAK,EAAC;AAAU;;;EAIhBA,KAAK,EAAC;AAAU;;;;EAQjBA,KAAK,EAAC;;;;;;;;;;;;;;uBArHtBC,mBAAA,CAyHM,OAzHNC,UAyHM,G,8eA7GJC,YAAA,CASSC,iBAAA;IATAC,IAAI,EAAE,EAAE;IAAEC,KAA8C,EAA9C;MAAA;MAAA;IAAA;;sBACjB,MAOU,CAPVH,YAAA,CAOUI,kBAAA;MAPAC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,KAAA,CAAAC;;wBAC9B,MAEe,CAFfR,YAAA,CAEeS,uBAAA;0BADb,MAAyE,CAAzET,YAAA,CAAyEU,mBAAA;sBAAtDH,KAAA,CAAAC,OAAO,CAACG,GAAG;qEAAXJ,KAAA,CAAAC,OAAO,CAACG,GAAG,GAAAC,MAAA;UAAEC,WAAW,EAAC,IAAI;UAACC,IAAI,EAAC;;;UAExDd,YAAA,CAEeS,uBAAA;0BADb,MAA0F,CAA1FT,YAAA,CAA0Fe,oBAAA;UAA/EC,IAAI,EAAC,SAAS;UAACF,IAAI,EAAC,OAAO;UAAEG,OAAK,EAAEC,QAAA,CAAAC,KAAK;UAAEC,IAAI,EAAC;;4BAAiB,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;;qCAKpFC,YAAA,CAwBWC,mBAAA;IAxBAC,IAAI,EAAEjB,KAAA,CAAAkB,QAAQ;IAAEC,MAAM,EAAN,EAAM;IAACC,MAAM,EAAN,EAAM;IAACxB,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAAyB,uBAAqB,EAArB,EAAqB;IACxG,YAAU,EAAC,KAAK;IAACW,IAAI,EAAC;;sBACtB,MAAwE,CAAxEd,YAAA,CAAwE4B,0BAAA;MAAvDC,IAAI,EAAC,KAAK;MAACC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAC7C/B,YAAA,CAAwE4B,0BAAA;MAAvDC,IAAI,EAAC,KAAK;MAACC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAC7C/B,YAAA,CAAwE4B,0BAAA;MAAvDC,IAAI,EAAC,KAAK;MAACC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAC7C/B,YAAA,CAAiF4B,0BAAA;MAAhEC,IAAI,EAAC,YAAY;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACtD/B,YAAA,CAAkF4B,0BAAA;MAAjEC,IAAI,EAAC,aAAa;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QACvD/B,YAAA,CAMkB4B,0BAAA;MANDC,IAAI,EAAC,YAAY;MAACC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;;MACzCC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACXA,KAAK,CAACC,GAAG,CAACC,UAAU,Y,cAAhCtC,mBAAA,CAEW,QAAAuC,UAAA,EAAAC,gBAAA,CADTJ,KAAK,CAACC,GAAG,CAACC,UAAU,CAACG,SAAS,2B;;QAIpCvC,YAAA,CASkB4B,0BAAA;MATDE,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC,KAAK;MAACC,KAAK,EAAC;;MACrCC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBlC,YAAA,CACiDe,oBAAA;QADtCC,IAAI,EAAC,SAAS;QAACF,IAAI,EAAC,MAAM;QAAEG,OAAK,EAAAL,MAAA,IAAEM,QAAA,CAAAsB,UAAU,CAACN,KAAK,CAACO,MAAM,EAAEP,KAAK,CAACC,GAAG;QAAGf,IAAI,EAAC,iBAAiB;QACvGjB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAEkB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;wDACrCrB,YAAA,CACmDe,oBAAA;QADxCC,IAAI,EAAC,MAAM;QAACF,IAAI,EAAC,MAAM;QAAEG,OAAK,EAAAL,MAAA,IAAEM,QAAA,CAAAwB,aAAa,CAACR,KAAK,CAACC,GAAG,CAACQ,GAAG;QAAGvB,IAAI,EAAC,kBAAkB;QAC9FjB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAIkB,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;wDACvCrB,YAAA,CACiDe,oBAAA;QADtCC,IAAI,EAAC,QAAQ;QAACF,IAAI,EAAC,MAAM;QAAEG,OAAK,EAAAL,MAAA,IAAEM,QAAA,CAAA0B,YAAY,CAACV,KAAK,CAACO,MAAM,EAAEP,KAAK,CAACC,GAAG;QAAGf,IAAI,EAAC,gBAAgB;QACvGjB,KAAgC,EAAhC;UAAA;QAAA;;0BAAiC,MAAEkB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;sDArB6Bd,KAAA,CAAAsC,WAAW,E,GAyBnF7C,YAAA,CAE8D8C,wBAAA;IAF9CC,eAAc,EAAE7B,QAAA,CAAA8B,mBAAmB;IAAG,cAAY,EAAEzC,KAAA,CAAA0C,IAAI,CAACC,WAAW;IAAG,WAAS,EAAE3C,KAAA,CAAA0C,IAAI,CAACE,QAAQ;IAC7GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAE/C,KAAA,CAAA0C,IAAI,CAACM,UAAU;IAC5EpD,KAA2C,EAA3C;MAAA;MAAA;IAAA;sFACFqD,mBAAA,aAAgB,EAChBxD,YAAA,CAoEYyD,oBAAA;IApEDC,KAAK,EAAC,MAAM;gBAAUnD,KAAA,CAAAoD,mBAAmB;+DAAnBpD,KAAA,CAAAoD,mBAAmB,GAAA/C,MAAA;IAAEgD,KAAK,EAAC;;sBAC1D,MA+DM,CA/DKrD,KAAA,CAAAsD,UAAU,I,cAArB/D,mBAAA,CA+DM,OA/DNgE,UA+DM,GA9DJC,mBAAA,CAsCM,OAtCNC,UAsCM,GArCJD,mBAAA,CAoCM,OApCNE,UAoCM,GAnCJF,mBAAA,CAEM,OAFNG,UAEM,GADJH,mBAAA,CAA0F;MAApFI,GAAG,8CAA8C5D,KAAA,CAAA6D,WAAW,CAACC,IAAI;MAAExE,KAAK,EAAC;2CAEjFkE,mBAAA,CA+BM,OA/BNO,UA+BM,GA9BJP,mBAAA,CAA6C,MAA7CQ,UAA6C,EAAAjC,gBAAA,CAAzB/B,KAAA,CAAA6D,WAAW,CAACI,KAAK,kBACrCT,mBAAA,CA4BM,OA5BNU,WA4BM,GA3BJV,mBAAA,CAGM,OAHNW,WAGM,G,0BAFJX,mBAAA,CAA4B;MAAzBlE,KAAK,EAAC;IAAc,6BACvBkE,mBAAA,CAAwC,cAAlC,KAAG,GAAAzB,gBAAA,CAAG/B,KAAA,CAAA6D,WAAW,CAACO,MAAM,iB,GAEhCZ,mBAAA,CAGM,OAHNa,WAGM,G,0BAFJb,mBAAA,CAA4B;MAAzBlE,KAAK,EAAC;IAAc,6BACvBkE,mBAAA,CAAsC,cAAhC,KAAG,GAAAzB,gBAAA,CAAG/B,KAAA,CAAA6D,WAAW,CAACS,GAAG,IAAG,GAAC,gB,GAEjCd,mBAAA,CAGM,OAHNe,WAGM,G,0BAFJf,mBAAA,CAA6B;MAA1BlE,KAAK,EAAC;IAAe,6BACxBkE,mBAAA,CAAuC,cAAjC,KAAG,GAAAzB,gBAAA,CAAG/B,KAAA,CAAA6D,WAAW,CAACW,KAAK,iB,GAE/BhB,mBAAA,CAGM,OAHNiB,WAGM,G,0BAFJjB,mBAAA,CAA8B;MAA3BlE,KAAK,EAAC;IAAgB,6BACzBkE,mBAAA,CAAsC,cAAhC,KAAG,GAAAzB,gBAAA,CAAG/B,KAAA,CAAA0E,gBAAgB,iB,GAE9BlB,mBAAA,CAUM,OAVNmB,WAUM,G,4BATJnB,mBAAA,CAA8B;MAA3BlE,KAAK,EAAC;IAAgB,6BACzBkE,mBAAA,CAOO,e,6CAPD,QACJ,IAAAA,mBAAA,CAKO;MALAlE,KAAK,EAAAsF,eAAA;2BAA6C5E,KAAA,CAAA6D,WAAW,CAACgB,KAAK;6BAAuD7E,KAAA,CAAA6D,WAAW,CAACgB,KAAK;;wBAI7I7E,KAAA,CAAA6D,WAAW,CAACgB,KAAK,wB,aASlCrB,mBAAA,CAqBM,OArBNsB,WAqBM,GApBJtB,mBAAA,CAmBM,OAnBNuB,WAmBM,G,4BAlBJvB,mBAAA,CAGK;MAHDlE,KAAK,EAAC;IAAe,IACvBkE,mBAAA,CAAgC;MAA7BlE,KAAK,EAAC;IAAkB,I,iBAAK,QAElC,E,sBACAkE,mBAAA,CAaM,OAbNwB,WAaM,GAZJxB,mBAAA,CAGM,OAHNyB,WAGM,G,4BAFJzB,mBAAA,CAAoB,eAAb,OAAK,sBACZA,mBAAA,CAAyD;MAApDlE,KAAK,EAAC,SAAS;MAAC4F,SAA6B,EAArBlF,KAAA,CAAAsD,UAAU,CAAC6B;4CAE1C3B,mBAAA,CAGM,OAHN4B,WAGM,G,4BAFJ5B,mBAAA,CAAoB,eAAb,OAAK,sBACZA,mBAAA,CAA2D;MAAtDlE,KAAK,EAAC,SAAS;MAAC4F,SAA+B,EAAvBlF,KAAA,CAAAsD,UAAU,CAAC+B;4CAE1C7B,mBAAA,CAGM,OAHN8B,WAGM,G,4BAFJ9B,mBAAA,CAAoB,eAAb,OAAK,sBACZA,mBAAA,CAA4D;MAAvDlE,KAAK,EAAC,SAAS;MAAC4F,SAAgC,EAAxBlF,KAAA,CAAAsD,UAAU,CAACiC;qEAMlDhG,mBAAA,CAEM,OAFNiG,WAEM,GADJ/F,YAAA,CAA0CgG,mBAAA;MAAhCC,WAAW,EAAC;IAAQ,G", "ignoreList": []}]}