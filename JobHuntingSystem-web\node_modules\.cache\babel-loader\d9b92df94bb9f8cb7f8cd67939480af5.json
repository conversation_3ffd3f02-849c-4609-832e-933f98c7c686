{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resume\\ResumeManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resume\\ResumeManage.vue", "mtime": 1741615349399}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZmluZC5qcyI7CmltcG9ydCByZXF1ZXN0LCB7IGJhc2UgfSBmcm9tICcuLi8uLi8uLi8uLi91dGlscy9odHRwJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdyZXN1bWUnLAogIGNvbXBvbmVudHM6IHt9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmaWx0ZXJzOiB7CiAgICAgICAgLy/liJfooajmn6Xor6Llj4LmlbAKICAgICAgICByZXN1bWVuYW1lOiAnJywKICAgICAgICBzbm86ICcnLAogICAgICAgIHNhbGFyeVJhbmdlOiAnJyAvLyDlt6XotYTljLrpl7QKICAgICAgfSwKICAgICAgcGFnZTogewogICAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICAgIC8vIOW9k+W<PERSON>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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "resumename", "sno", "salaryRange", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "resumeDialogVisible", "resumeData", "studentInfo", "professionalsList", "professionalName", "created", "getDatas", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "rid", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "para", "min", "max", "split", "minSalary", "max<PERSON><PERSON><PERSON>", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "id", "handleEdit", "previewResume", "resumeRes", "code", "studentRes", "getProfessionals", "error", "console", "params", "getProfessionalName", "proid", "professional", "find", "p", "proname", "formatSalary", "salary", "num", "parseInt", "toFixed"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resume\\ResumeManage.vue"], "sourcesContent": ["<template>\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"page-title-box\">\n        <div class=\"page-title-right\">\n          <ol class=\"breadcrumb m-0\">\n            <li class=\"breadcrumb-item\"><a id=\"title2\">简历管理</a></li>\n            <li class=\"breadcrumb-item active\" id=\"title3\">简历列表</li>\n          </ol>\n        </div>\n        <h4 class=\"page-title\" id=\"title1\">简历列表</h4>\n      </div>\n    </div>\n    <el-col :span=\"24\" style=\"padding-bottom: 0px; margin-left: 10px\">\n      <el-form :inline=\"true\" :model=\"filters\">\n        <el-form-item>\n          <el-input v-model=\"filters.resumename\" placeholder=\"简历名称\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-input v-model=\"filters.sno\" placeholder=\"账号\" size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"filters.salaryRange\" placeholder=\"工资区间\" size=\"small\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"3k以下\" value=\"0-3000\"></el-option>\n            <el-option label=\"3k-5k\" value=\"3000-5000\"></el-option>\n            <el-option label=\"5k-8k\" value=\"5000-8000\"></el-option>\n            <el-option label=\"8k-12k\" value=\"8000-12000\"></el-option>\n            <el-option label=\"12k-15k\" value=\"12000-15000\"></el-option>\n            <el-option label=\"15k-20k\" value=\"15000-20000\"></el-option>\n            <el-option label=\"20k以上\" value=\"20000-999999\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </el-col>\n\n    <el-table :data=\"datalist\" border stripe style=\"width: 100%\" v-loading=\"listLoading\" highlight-current-row\n      max-height=\"600\" size=\"small\">\n      <el-table-column prop=\"resumename\" label=\"简历名称\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"education\" label=\"教育经历\" align=\"center\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.education != null\">{{ scope.row.education.substring(0, 20) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"expectedSalary\" label=\"期望薪资\" align=\"center\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.expectedSalary\">{{ formatSalary(scope.row.expectedSalary) }}</span>\n          <span v-else>未设置</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"parttimejob\" label=\"工作经历\" align=\"center\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.parttimejob != null\">{{\n            scope.row.parttimejob.substring(0, 20)\n            }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"introduction\" label=\"个人简介\" align=\"center\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.introduction != null\">{{\n            scope.row.introduction.substring(0, 20)\n            }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"sno\" label=\"账号\" align=\"center\"></el-table-column>\n      <el-table-column prop=\"createdat\" label=\"创建时间\" align=\"center\"></el-table-column>\n      <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\"\n            style=\"padding: 3px 6px 3px 6px\">详情</el-button>\n          <el-button type=\"info\" size=\"mini\" @click=\"previewResume(scope.row.rid)\" icon=\"el-icon-document\"\n            style=\"padding: 3px 6px 3px 6px\">预览简历</el-button>\n          <el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\"\n            style=\"padding: 3px 6px 3px 6px\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n      style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n    <!-- 简历预览对话框 -->\n    <el-dialog title=\"简历预览\" v-model=\"resumeDialogVisible\" width=\"800px\">\n      <div v-if=\"resumeData\" class=\"resume-preview\">\n        <div class=\"resume-header\">\n          <div class=\"header-content\">\n            <div class=\"avatar-section\">\n              <img :src=\"'http://localhost:8088/JobHuntingSystem/' + studentInfo.spic\" class=\"avatar\" />\n            </div>\n            <div class=\"basic-info\">\n              <h1 class=\"name\">{{ studentInfo.sname }}</h1>\n              <div class=\"info-grid\">\n                <div class=\"info-item\">\n                  <i class=\"el-icon-user\"></i>\n                  <span>性别：{{ studentInfo.gender }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-date\"></i>\n                  <span>年龄：{{ studentInfo.age }}岁</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-phone\"></i>\n                  <span>电话：{{ studentInfo.phone }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-school\"></i>\n                  <span>专业：{{ professionalName }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-s-flag\"></i>\n                  <span>就业状态：\n                    <span :class=\"{\n                      'status-employed': studentInfo.sflag === '已就业',\n                      'status-unemployed': studentInfo.sflag === '未就业',\n                    }\">\n                      {{ studentInfo.sflag }}\n                    </span>\n                  </span>\n                </div>\n                <div class=\"info-item\">\n                  <i class=\"el-icon-money\"></i>\n                  <span>期望薪资：\n                    <span class=\"salary-text\">{{\n                      resumeData.expectedSalary ? formatSalary(resumeData.expectedSalary) : '未设置'\n                      }}</span>\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"resume-body\">\n          <div class=\"resume-section\">\n            <h2 class=\"section-title\">\n              <i class=\"el-icon-document\"></i>\n              简历信息\n            </h2>\n            <div class=\"resume-content\">\n              <div class=\"info-row\">\n                <label>教育背景：</label>\n                <div class=\"content\" v-html=\"resumeData.education\"></div>\n              </div>\n              <div class=\"info-row\">\n                <label>实习经历：</label>\n                <div class=\"content\" v-html=\"resumeData.parttimejob\"></div>\n              </div>\n              <div class=\"info-row\">\n                <label>个人介绍：</label>\n                <div class=\"content\" v-html=\"resumeData.introduction\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div v-else class=\"resume-empty\">\n        <el-empty description=\"暂无简历信息\"></el-empty>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\n  import request, { base } from '../../../../utils/http';\n  export default {\n    name: 'resume',\n    components: {},\n    data() {\n      return {\n        filters: {\n          //列表查询参数\n          resumename: '',\n          sno: '',\n          salaryRange: '', // 工资区间\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n        resumeDialogVisible: false,\n        resumeData: null,\n        studentInfo: {},\n        professionalsList: [],\n        professionalName: '',\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n    methods: {\n      // 删除简历\n      handleDelete(index, row) {\n        this.$confirm('确认删除该记录吗?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        })\n          .then(() => {\n            this.listLoading = true;\n            let url = base + '/resume/del?id=' + row.rid;\n            request.post(url).then((res) => {\n              this.listLoading = false;\n\n              this.$message({\n                message: '删除成功',\n                type: 'success',\n                offset: 320,\n              });\n              this.getDatas();\n            });\n          })\n          .catch(() => { });\n      },\n\n      // 分页\n      handleCurrentChange(val) {\n        this.page.currentPage = val;\n        this.getDatas();\n      },\n\n      //获取列表数据\n      getDatas() {\n        let para = {\n          resumename: this.filters.resumename,\n          sno: this.filters.sno,\n        };\n\n        // 如果选择了工资区间，添加到查询参数\n        if (this.filters.salaryRange) {\n          const [min, max] = this.filters.salaryRange.split('-');\n          para.minSalary = min;\n          para.maxSalary = max;\n        }\n\n        this.listLoading = true;\n        let url =\n          base +\n          '/resume/list?currentPage=' +\n          this.page.currentPage +\n          '&pageSize=' +\n          this.page.pageSize;\n        request.post(url, para).then((res) => {\n          if (res.resdata.length > 0) {\n            this.isPage = true;\n          } else {\n            this.isPage = false;\n          }\n          this.page.totalCount = res.count;\n          this.datalist = res.resdata;\n          this.listLoading = false;\n        });\n      },\n      //查询\n      query() {\n        this.getDatas();\n      },\n\n      // 查看\n      handleShow(index, row) {\n        this.$router.push({\n          path: '/ResumeDetail',\n          query: {\n            id: row.rid,\n          },\n        });\n      },\n\n      // 编辑\n      handleEdit(index, row) {\n        this.$router.push({\n          path: '/ResumeEdit',\n          query: {\n            id: row.rid,\n          },\n        });\n      },\n\n      // 预览简历\n      async previewResume(rid) {\n        try {\n          // 获取简历信息\n          const resumeRes = await request.post(base + '/resume/get?id=' + rid);\n          if (resumeRes.code === 200) {\n            this.resumeData = resumeRes.resdata;\n            // 获取求职者信息\n            const studentRes = await request.post(base + '/students/get?id=' + this.resumeData.sno);\n            if (studentRes.code === 200) {\n              this.studentInfo = studentRes.resdata;\n              // 获取专业信息\n              await this.getProfessionals();\n            }\n            this.resumeDialogVisible = true;\n          }\n        } catch (error) {\n          console.error('获取简历信息失败:', error);\n          this.$message({\n            message: '获取简历信息失败',\n            type: 'error',\n            offset: 320,\n          });\n        }\n      },\n\n      // 获取专业列表\n      async getProfessionals() {\n        try {\n          const res = await request.post(\n            base + '/professionals/list',\n            {},\n            { params: { currentPage: 1, pageSize: 100 } }\n          );\n          if (res.code === 200) {\n            this.professionalsList = res.resdata;\n            this.getProfessionalName();\n          }\n        } catch (error) {\n          console.error('获取专业列表失败:', error);\n        }\n      },\n\n      // 获取专业名称\n      getProfessionalName() {\n        if (this.studentInfo.proid && this.professionalsList.length > 0) {\n          const professional = this.professionalsList.find((p) => p.proid === this.studentInfo.proid);\n          this.professionalName = professional ? professional.proname : '';\n        }\n      },\n\n      // 格式化薪资显示\n      formatSalary(salary) {\n        const num = parseInt(salary);\n        if (num >= 1000) {\n          return (num / 1000).toFixed(1) + 'k';\n        }\n        return salary;\n      },\n    },\n  };\n</script>\n<style scoped>\n  .resume-preview {\n    max-width: 800px;\n    margin: 0 auto;\n  }\n\n  .resume-header {\n    background: linear-gradient(135deg, #3bc0c3 0%, #42d3d7 100%);\n    padding: 40px;\n    border-radius: 8px 8px 0 0;\n    color: #fff;\n  }\n\n  .header-content {\n    display: flex;\n    gap: 30px;\n  }\n\n  .avatar-section {\n    width: 120px;\n    height: 120px;\n  }\n\n  .avatar {\n    width: 100%;\n    height: 100%;\n    border-radius: 60px;\n    object-fit: cover;\n    border: 4px solid rgba(255, 255, 255, 0.3);\n  }\n\n  .basic-info {\n    flex: 1;\n  }\n\n  .name {\n    font-size: 24px;\n    margin-bottom: 20px;\n  }\n\n  .info-grid {\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: 15px;\n  }\n\n  .info-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n\n  .resume-body {\n    background: #fff;\n    padding: 30px;\n    border-radius: 0 0 8px 8px;\n  }\n\n  .section-title {\n    font-size: 18px;\n    color: #333;\n    margin-bottom: 20px;\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n\n  .info-row {\n    margin-bottom: 20px;\n  }\n\n  .info-row label {\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 8px;\n    display: block;\n  }\n\n  .content {\n    color: #666;\n    line-height: 1.8;\n  }\n\n  .status-employed {\n    color: #67c23a;\n    font-weight: bold;\n  }\n\n  .status-unemployed {\n    color: #f56c6c;\n    font-weight: bold;\n  }\n\n  .resume-empty {\n    padding: 40px 0;\n    text-align: center;\n  }\n\n  .salary-text {\n    color: #ff4d4f;\n    font-weight: bold;\n  }\n</style>"], "mappings": ";;;AAqKE,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,QAAQ;EACdC,UAAU,EAAE,CAAC,CAAC;EACdC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;QACP;QACAC,UAAU,EAAE,EAAE;QACdC,GAAG,EAAE,EAAE;QACPC,WAAW,EAAE,EAAE,CAAE;MACnB,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE;MAAE;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC,CAAC;MACfC,iBAAiB,EAAE,EAAE;MACrBC,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAAClB,WAAU,GAAI,IAAI;QACvB,IAAImB,GAAE,GAAIhC,IAAG,GAAI,iBAAgB,GAAI0B,GAAG,CAACO,GAAG;QAC5ClC,OAAO,CAACmC,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACtB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACuB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAChB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAiB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAACjC,IAAI,CAACC,WAAU,GAAIgC,GAAG;MAC3B,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAIoB,IAAG,GAAI;QACTrC,UAAU,EAAE,IAAI,CAACD,OAAO,CAACC,UAAU;QACnCC,GAAG,EAAE,IAAI,CAACF,OAAO,CAACE;MACpB,CAAC;;MAED;MACA,IAAI,IAAI,CAACF,OAAO,CAACG,WAAW,EAAE;QAC5B,MAAM,CAACoC,GAAG,EAAEC,GAAG,IAAI,IAAI,CAACxC,OAAO,CAACG,WAAW,CAACsC,KAAK,CAAC,GAAG,CAAC;QACtDH,IAAI,CAACI,SAAQ,GAAIH,GAAG;QACpBD,IAAI,CAACK,SAAQ,GAAIH,GAAG;MACtB;MAEA,IAAI,CAAC/B,WAAU,GAAI,IAAI;MACvB,IAAImB,GAAE,GACJhC,IAAG,GACH,2BAA0B,GAC1B,IAAI,CAACQ,IAAI,CAACC,WAAU,GACpB,YAAW,GACX,IAAI,CAACD,IAAI,CAACE,QAAQ;MACpBX,OAAO,CAACmC,IAAI,CAACF,GAAG,EAAEU,IAAI,CAAC,CAACX,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACa,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAAC1C,IAAI,CAACG,UAAS,GAAIwB,GAAG,CAACgB,KAAK;QAChC,IAAI,CAACpC,QAAO,GAAIoB,GAAG,CAACa,OAAO;QAC3B,IAAI,CAACnC,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACD;IACAuC,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC9B,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACA+B,UAAUA,CAAC5B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC4B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,eAAe;QACrBJ,KAAK,EAAE;UACLK,EAAE,EAAE/B,GAAG,CAACO;QACV;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAyB,UAAUA,CAACjC,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC4B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,aAAa;QACnBJ,KAAK,EAAE;UACLK,EAAE,EAAE/B,GAAG,CAACO;QACV;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACA,MAAM0B,aAAaA,CAAC1B,GAAG,EAAE;MACvB,IAAI;QACF;QACA,MAAM2B,SAAQ,GAAI,MAAM7D,OAAO,CAACmC,IAAI,CAAClC,IAAG,GAAI,iBAAgB,GAAIiC,GAAG,CAAC;QACpE,IAAI2B,SAAS,CAACC,IAAG,KAAM,GAAG,EAAE;UAC1B,IAAI,CAAC5C,UAAS,GAAI2C,SAAS,CAACZ,OAAO;UACnC;UACA,MAAMc,UAAS,GAAI,MAAM/D,OAAO,CAACmC,IAAI,CAAClC,IAAG,GAAI,mBAAkB,GAAI,IAAI,CAACiB,UAAU,CAACX,GAAG,CAAC;UACvF,IAAIwD,UAAU,CAACD,IAAG,KAAM,GAAG,EAAE;YAC3B,IAAI,CAAC3C,WAAU,GAAI4C,UAAU,CAACd,OAAO;YACrC;YACA,MAAM,IAAI,CAACe,gBAAgB,CAAC,CAAC;UAC/B;UACA,IAAI,CAAC/C,mBAAkB,GAAI,IAAI;QACjC;MACF,EAAE,OAAOgD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAAC5B,QAAQ,CAAC;UACZC,OAAO,EAAE,UAAU;UACnBP,IAAI,EAAE,OAAO;UACbQ,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC;IAED;IACA,MAAMyB,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAM5B,GAAE,GAAI,MAAMpC,OAAO,CAACmC,IAAI,CAC5BlC,IAAG,GAAI,qBAAqB,EAC5B,CAAC,CAAC,EACF;UAAEkE,MAAM,EAAE;YAAEzD,WAAW,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAI;QAAE,CAC9C,CAAC;QACD,IAAIyB,GAAG,CAAC0B,IAAG,KAAM,GAAG,EAAE;UACpB,IAAI,CAAC1C,iBAAgB,GAAIgB,GAAG,CAACa,OAAO;UACpC,IAAI,CAACmB,mBAAmB,CAAC,CAAC;QAC5B;MACF,EAAE,OAAOH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;IAED;IACAG,mBAAmBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAACjD,WAAW,CAACkD,KAAI,IAAK,IAAI,CAACjD,iBAAiB,CAAC8B,MAAK,GAAI,CAAC,EAAE;QAC/D,MAAMoB,YAAW,GAAI,IAAI,CAAClD,iBAAiB,CAACmD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,KAAI,KAAM,IAAI,CAAClD,WAAW,CAACkD,KAAK,CAAC;QAC3F,IAAI,CAAChD,gBAAe,GAAIiD,YAAW,GAAIA,YAAY,CAACG,OAAM,GAAI,EAAE;MAClE;IACF,CAAC;IAED;IACAC,YAAYA,CAACC,MAAM,EAAE;MACnB,MAAMC,GAAE,GAAIC,QAAQ,CAACF,MAAM,CAAC;MAC5B,IAAIC,GAAE,IAAK,IAAI,EAAE;QACf,OAAO,CAACA,GAAE,GAAI,IAAI,EAAEE,OAAO,CAAC,CAAC,IAAI,GAAG;MACtC;MACA,OAAOH,MAAM;IACf;EACF;AACF,CAAC", "ignoreList": []}]}