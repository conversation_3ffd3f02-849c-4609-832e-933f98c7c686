{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\jobcategories\\JobcategoriesManage.vue?vue&type=template&id=1ad826de", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\jobcategories\\JobcategoriesManage.vue", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createBlock", "_component_el_table", "data", "$data", "datalist", "border", "stripe", "style", "size", "_createVNode", "_component_el_table_column", "prop", "label", "align", "default", "_withCtx", "scope", "_component_el_button", "type", "onClick", "$event", "$options", "handleEdit", "$index", "row", "icon", "_cache", "handleDelete", "listLoading", "_component_el_pagination", "onCurrentChange", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\jobcategories\\JobcategoriesManage.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">职位分类管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">职位分类列表</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">职位分类列表</h4>\r\n      </div>\r\n    </div>\r\n    \r\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\r\n<el-table-column prop=\"catname\" label=\"分类名称\"  align=\"center\"></el-table-column>\r\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\r\n<template #default=\"scope\">\r\n<el-button type=\"success\" size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\" icon=\"el-icon-edit\" style=\" padding: 3px 6px 3px 6px;\">编辑</el-button>\r\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \r\n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" \r\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'jobcategories',\n  components: {\n    \n  },  \n    data() {\n      return {\n       \n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        \n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据  \n    \n      };\n    },\n    created() {\r\n      this.getDatas();\r\n    },\r\n\r\n \n    methods: {    \n\n              \n       // 删除职位分类\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/jobcategories/del?id=\" + row.catid;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n        getDatas() {      \n          let para = {\n            \n          };\n          this.listLoading = true;\n          let url = base + \"/jobcategories/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;        \n          request.post(url, para).then((res) => {   \n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },    \n        \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/JobcategoriesDetail\",\n             query: {\n                id: row.catid,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/JobcategoriesEdit\",\n             query: {\n                id: row.catid,\n              },\n          });\n        },\n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EAEOA,KAAK,EAAC;AAAK;;;;;;;uBAAhBC,mBAAA,CA0BM,OA1BNC,UA0BM,G,2YAbRC,YAAA,CAQWC,mBAAA;IARAC,IAAI,EAAEC,KAAA,CAAAC,QAAQ;IAAEC,MAAM,EAAN,EAAM;IAACC,MAAM,EAAN,EAAM;IAACC,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAA4B,uBAAqB,EAArB,EAAqB;IAAG,YAAU,EAAC,KAAK;IAAKC,IAAI,EAAC;;sBAC1I,MAA+E,CAA/EC,YAAA,CAA+EC,0BAAA;MAA9DC,IAAI,EAAC,SAAS;MAACC,KAAK,EAAC,MAAM;MAAEC,KAAK,EAAC;QACpDJ,YAAA,CAKkBC,0BAAA;MALDE,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC,KAAK;MAACC,KAAK,EAAC;;MACvCC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACzBP,YAAA,CAAwJQ,oBAAA;QAA7IC,IAAI,EAAC,SAAS;QAACV,IAAI,EAAC,MAAM;QAAEW,OAAK,EAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU,CAACN,KAAK,CAACO,MAAM,EAAEP,KAAK,CAACQ,GAAG;QAAGC,IAAI,EAAC,cAAc;QAAClB,KAAkC,EAAlC;UAAA;QAAA;;0BAAmC,MAAEmB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;wDAC5IjB,YAAA,CAA2JQ,oBAAA;QAAhJC,IAAI,EAAC,QAAQ;QAACV,IAAI,EAAC,MAAM;QAAEW,OAAK,EAAAC,MAAA,IAAEC,QAAA,CAAAM,YAAY,CAACX,KAAK,CAACO,MAAM,EAAEP,KAAK,CAACQ,GAAG;QAAGC,IAAI,EAAC,gBAAgB;QAAClB,KAAkC,EAAlC;UAAA;QAAA;;0BAAmC,MAAEmB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;sDALtEvB,KAAA,CAAAyB,WAAW,E,GASpFnB,YAAA,CAE6DoB,wBAAA;IAF5CC,eAAc,EAAET,QAAA,CAAAU,mBAAmB;IAAG,cAAY,EAAE5B,KAAA,CAAA6B,IAAI,CAACC,WAAW;IAAG,WAAS,EAAE9B,KAAA,CAAA6B,IAAI,CAACE,QAAQ;IAC/GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAElC,KAAA,CAAA6B,IAAI,CAACM,UAAU;IAC5E/B,KAA2C,EAA3C;MAAA;MAAA;IAAA", "ignoreList": []}]}