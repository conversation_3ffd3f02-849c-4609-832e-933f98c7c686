{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue", "mtime": 1741614414000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue"], "names": [], "mappings": ";AAkGA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzD,CAAC;MACH,CAAC;;;IAGH,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;MACX,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC;UACD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;cAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjD,CAAC,CAAC;;;YAGJ,EAAE,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd;MACF,CAAC,CAAC;IACJ,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC;AACH,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/BbsView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <table style=\"width:100%;line-height:27px; font-size:12px;border: 1px solid #C2D5E3;  margin-bottom:10px;\">\r\n    <tr>\r\n\r\n      <td> <span style=\"font-size:14px; font-weight:bold\">\r\n          {{ bbsmore.btitle }}\r\n        </span>\r\n\r\n      </td>\r\n      <td width=\"200\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\">\r\n        查看：<b style=\"color:red;\">\r\n          {{ bbsmore.btotal }}\r\n        </b> |\r\n        回复：<b style=\"color:red;\">\r\n          {{ bbsmore.by1 }}\r\n        </b> |\r\n        <a href=\"/bbs\">返回列表</a>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/JobHuntingSystem/' + bbsmore.by2\" />\r\n        </div>\r\n\r\n        <span class=\"cu-tag bg-gradual-green\" style=\" height:28px;\">楼主</span>\r\n        <span class=\"text-red\">\r\n          {{ bbsmore.sno }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"bbsmore.bdetail\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ bbsmore.addtime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \" v-for=\"item in bbsmore.bbsmore\">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/JobHuntingSystem/' + item.by1\" />\r\n        </div>\r\n\r\n        <span class=\"text-red\">\r\n          {{ item.sno }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"item.mdetail\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ item.antime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\r\n\r\n    <el-form-item label=\"回复内容\" prop=\"mdetail\">\r\n      <WangEditor ref=\"wangEditorRef\" v-model=\"formData.mdetail\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n        @change=\"editorChange\" height=\"200\"></WangEditor>\r\n    </el-form-item>\r\n    <el-form-item label=\"\">\r\n      <el-button type=\"primary\" @click=\"save\" :loading=\"btnLoading\">提 交</el-button>\r\n    </el-form-item>\r\n\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport WangEditor from \"../../components/WangEditor\";\r\n\r\nexport default {\r\n  name: \"bbsView\",\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      bbsmore: \"\",\r\n\r\n      formData: {\r\n        mdetail: \"\",\r\n      },\r\n\r\n      rules: {\r\n        mdetail: [\r\n          { required: true, message: \"请输入回复内容\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n\r\n\r\n    };\r\n  },\r\n  created() {\r\n\r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let id = this.$route.query.id;\r\n      let para = {\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/bbs/get?id=\" + id;\r\n      request.post(url, para).then((res) => {\r\n        this.bbsmore = res.resdata;\r\n      });\r\n    },\r\n\r\n    //回复\r\n    save() {\r\n      var lname = sessionStorage.getItem(\"lname\");\r\n      if (lname == null) {\r\n        this.$message({\r\n          message: \"请先登录\",\r\n          type: \"error\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs.formDataRef.validate((valid) => {\r\n        if (valid) {\r\n          this.btnLoading = true;\r\n          let para = {\r\n            bid: this.bbsmore.bid,\r\n            mdetail: this.formData.mdetail,\r\n            sno: lname,\r\n          };\r\n          let url = base + \"/bbsmore/add\";\r\n          request.post(url, para).then((res) => {\r\n            this.btnLoading = false;\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: \"回复成功\",\r\n                type: \"success\",\r\n              });\r\n              this.getDatas();\r\n              this.formData.mdetail = \"\";\r\n\r\n              //设置富文本编辑器内容\r\n              this.$nextTick(() => {\r\n                this.$refs[\"wangEditorRef\"].editor.txt.html(\"\");\r\n              });\r\n\r\n\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.mdetail = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 180px;\r\n}\r\n</style>\r\n\r\n"]}]}