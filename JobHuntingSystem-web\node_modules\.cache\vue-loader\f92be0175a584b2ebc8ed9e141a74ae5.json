{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Leftnav.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Leftnav.vue", "mtime": 1741536471000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vLi4vdXRpbHMvaHR0cCI7DQppbXBvcnQgTGVmdCBmcm9tICIuLi8uLi9jb21wb25lbnRzL0xlZnQiOw0KaW1wb3J0IFRvcE1lbnUgZnJvbSAiLi4vLi4vY29tcG9uZW50cy9Ub3BNZW51IjsNCmltcG9ydCBGb290IGZyb20gIi4uLy4uL2NvbXBvbmVudHMvRm9vdCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCgluYW1lOiAiTGVmdG5hdiIsDQoJY29tcG9uZW50czogew0KCQlMZWZ0LA0KCQlUb3BNZW51LA0KCQlGb290LA0KCX0sDQoJZGF0YSgpIHsNCgkJcmV0dXJuIHsNCg0KCQl9Ow0KCX0sDQoJbW91bnRlZCgpIHsNCg0KCX0sDQoJY3JlYXRlZCgpIHsNCg0KCX0sDQoJbWV0aG9kczogew0KDQoJfSwNCn07DQo="}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Leftnav.vue"], "names": [], "mappings": ";AAoEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAExC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;CACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC;CACL,CAAC;CACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEP,CAAC;CACF,CAAC;CACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;CAEV,CAAC;CACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;CAEV,CAAC;CACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;CAET,CAAC;AACF,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/Leftnav.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\t<TopMenu />\r\n\r\n\r\n\t<div class=\"custom-breadcrumb custom-breadcrumb--bg\"\r\n\t\t:style=\"{ backgroundImage: 'url(' + require('@/assets/images/breadcrumb-bg.jpg') + ')' }\">\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"row\">\r\n\t\t\t\t<!-- page title -->\r\n\r\n\t\t\t\t<div class=\"col-md-6\">\r\n\t\t\t\t\t<h1 class=\"page-title\">\r\n\t\t\t\t\t\t{{ $route.meta.title }}\r\n\t\t\t\t\t</h1>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- breadcrumb -->\r\n\t\t\t\t<div class=\"col-md-6 breadcrumb-menu\">\r\n\t\t\t\t\t<nav aria-label=\"breadcrumb\">\r\n\t\t\t\t\t\t<ol class=\"breadcrumb\">\r\n\t\t\t\t\t\t\t<li class=\"breadcrumb-item\">\r\n\t\t\t\t\t\t\t\t<a href=\"/index\">\r\n\t\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"fas fa-home\">\r\n\t\t\t\t\t\t\t\t\t\t</i>\r\n\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t网站首页\r\n\t\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t<li class=\"breadcrumb-item active\" aria-current=\"page\">\r\n\t\t\t\t\t\t\t\t‌{{ $route.meta.title }}\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t</ol>\r\n\t\t\t\t\t</nav>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\r\n\r\n\t<div class=\"main-content pt-120\">\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"pb-120\">\r\n\t\t\t\t<!-- our team list -->\r\n\t\t\t\t<div class=\"row\">\r\n\r\n\r\n\t\t\t\t\t<div class=\"col-xxl-8 col-xl-8 col-lg-8\">\r\n\t\t\t\t\t\t<div style=\"line-height: 30px;\">\r\n\t\t\t\t\t\t\t<router-view />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<Left />\r\n\r\n\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<!-- call to action banner -->\r\n\t\t<!-- end call to action banner -->\r\n\t</div>\r\n\r\n\r\n\r\n\r\n\t<Foot />\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport Left from \"../../components/Left\";\r\nimport TopMenu from \"../../components/TopMenu\";\r\nimport Foot from \"../../components/Foot\";\r\n\r\nexport default {\r\n\tname: \"Leftnav\",\r\n\tcomponents: {\r\n\t\tLeft,\r\n\t\tTopMenu,\r\n\t\tFoot,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\r\n\t\t};\r\n\t},\r\n\tmounted() {\r\n\r\n\t},\r\n\tcreated() {\r\n\r\n\t},\r\n\tmethods: {\r\n\r\n\t},\r\n};\r\n</script>\r\n\r\n\r\n<style></style>\r\n"]}]}