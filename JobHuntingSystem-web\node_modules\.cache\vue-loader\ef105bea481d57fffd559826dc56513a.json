{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyEdit.vue?vue&type=template&id=68989f8b", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyEdit.vue", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\company\\CompanyEdit.vue"], "names": [], "mappings": ";;EAEE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;KACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;QACE,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACpB;UACA,CAAC,CAAC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAGf,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/company/CompanyEdit.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">企业管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">编辑企业</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">编辑企业</h4>\r\n      </div>\r\n    </div>\r\n     <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"企业账号\" prop=\"clname\">\r\n<el-input v-model=\"formData.clname\" placeholder=\"企业账号\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"登录密码\" prop=\"password\">\r\n<el-input v-model=\"formData.password\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"企业名称\" prop=\"comname\">\r\n<el-input v-model=\"formData.comname\" placeholder=\"企业名称\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"企业规模\" prop=\"scale\">\r\n<el-input v-model=\"formData.scale\" placeholder=\"企业规模\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"企业性质\" prop=\"nature\">\r\n<el-input v-model=\"formData.nature\" placeholder=\"企业性质\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系方式\" prop=\"contact\">\r\n<el-input v-model=\"formData.contact\" placeholder=\"联系方式\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系地址\" prop=\"address\">\r\n<el-input v-model=\"formData.address\" placeholder=\"联系地址\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item prop=\"logo\" label=\"企业logo\"  min-width=\"20%\">\r\n<el-input  v-model=\"formData.logo\" placeholder=\"企业logo\"  readonly=\"true\" style=\"width:50%;\" ></el-input>\r\n<el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\r\n</el-form-item>\r\n<el-form-item label=\"企业介绍\" prop=\"introduction\">\r\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.introduction\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n     <el-dialog\n        v-model=\"uploadVisible\"\n        title=\"附件上传\"\n        custom-class=\"el-dialog-widthSmall\"\n        @close=\"closeDialog\"\n      >\n        <div>\n          <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n        </div>\n        <el-upload\n          action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n          style=\"\n            margin: auto;\n            margin-top: 10px;\n            border: 1px solid #dcdfe6;\n            border-radius: 4px;\n          \"\n          drag\n          :limit=\"1\"\n          :on-preview=\"handlePreview\"\n          :on-remove=\"handleRemove\"\n          :file-list=\"fileList\"\n          :on-exceed=\"handleExceed\"\n          :auto-upload=\"false\"\n          name=\"file\"\n          :on-change=\"fileListChange\"\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">\n            将数据文件拖到此处，或<em>点击上传</em>\n          </div>\n          <div class=\"el-upload__tip\">\n            <div\n              style=\"display: inline; color: #d70000; font-size: 14px\"\n              class=\"uploadFileWarning\"\n              id=\"uploadFileWarning\"\n            ></div>\n          </div>\n        </el-upload>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"hideUpload\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n        </span>\n      </el-dialog>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\nimport WangEditor from \"../../../components/WangEditor\";\nexport default {\n  name: 'CompanyEdit',\n  components: {\n    WangEditor,\n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          clname: [{ required: true, message: '请输入企业账号', trigger: 'blur' },\r\n],          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' },\r\n],          comname: [{ required: true, message: '请输入企业名称', trigger: 'blur' },\r\n],          scale: [{ required: true, message: '请输入企业规模', trigger: 'blur' },\r\n],          nature: [{ required: true, message: '请输入企业性质', trigger: 'blur' },\r\n],          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },\r\n],          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },\r\n],          logo: [{ required: true, message: '请输入企业logo', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n    },\r\n\r\n \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/company/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            this.$refs[\"wangEditorRef\"].editor.txt.html(this.formData.introduction);\n            \n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/company/update\";\n              this.btnLoading = true;\n              \n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });\n                  this.$router.push({\n                    path: \"/CompanyManage\",\n                  });\n                } else {\n                  this.$message({\n                    message:res.msg,\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n                this.btnLoading = false;\n              });\n            }\n    \n          });\n        },\n        \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/CompanyManage\",\n          });\n        },       \n              \n          \n            //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: \"只能上传一个文件\",\n        type: \"error\",\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = \"png,jpg\";\n      let extendFileNames = extendFileName.split(\",\");\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(\n          new RegExp(\"(.*).(\" + extendFileNames[i] + \")$\", \"gim\")\n        );\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key[\"name\"]);\n        }\n        if (!ret) {\n          console.log(key[\"name\"] + \":\" + ret);\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件后缀必须为\" + extendFileName + \"格式！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key[\"name\"]) != -1) {\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件重复！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key[\"name\"]);\n        if (fileNames !== \"\") {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: \"请选择文件！\",\n          type: \"error\",\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append(\"file\", file.raw, file.raw.name);\n      });\n      let url = base + \"/common/uploadFile\";\n      console.log(\"url=\" + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.logo = furl;  // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n\r\n   \n            // 富文本编辑器\n    editorChange(val) {\n      this.formData.introduction = val;\n    },\r\n   \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}