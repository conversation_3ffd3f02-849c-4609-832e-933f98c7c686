{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resume\\ResumeDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resume\\ResumeDetail.vue", "mtime": 1741614896084}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1Jlc3VtZURldGFpbCcsCiAgY29tcG9uZW50czoge30sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlkOiAnJywKICAgICAgZm9ybURhdGE6IHt9IC8v6KGo5Y2V5pWw5o2uICAgICAgICAgCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsgLy/ojrflj5blj4LmlbAKICAgIHRoaXMuZ2V0RGF0YXMoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICBnZXREYXRhcygpIHsKICAgICAgbGV0IHBhcmEgPSB7fTsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9yZXN1bWUvZ2V0P2lkPSIgKyB0aGlzLmlkOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5mb3JtRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocmVzLnJlc2RhdGEpKTsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOi/lOWbngogICAgYmFjaygpIHsKICAgICAgLy/ov5Tlm57kuIrkuIDpobUKICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "formData", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "back", "$router", "go"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resume\\ResumeDetail.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">简历管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">简历详情</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">简历详情</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n      <el-form-item label=\"简历编号\">\r\n        {{formData.rid}}</el-form-item>\r\n      <el-form-item label=\"简历名称\">\r\n        {{formData.resumename}}</el-form-item>\r\n      <el-form-item label=\"教育经历\">\r\n        {{formData.education}}</el-form-item>\r\n      <el-form-item label=\"工作经历\">\r\n        {{formData.parttimejob}}</el-form-item>\r\n      <el-form-item label=\"个人简介\">\r\n        {{formData.introduction}}</el-form-item>\r\n      <el-form-item label=\"账号\">\r\n        {{formData.sno}}</el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        {{formData.createdat}}</el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\n\r\n  import request, { base } from \"../../../../utils/http\";\r\n  export default {\r\n    name: 'ResumeDetail',\r\n    components: {\r\n    },\r\n    data() {\r\n      return {\r\n        id: '',\r\n        formData: {}, //表单数据         \r\n\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id; //获取参数\r\n      this.getDatas();\r\n    },\r\n\r\n\r\n    methods: {\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/resume/get?id=\" + this.id;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 返回\r\n      back() {\r\n        //返回上一页\r\n        this.$router.go(-1);\r\n      },\r\n\r\n    },\r\n  }\r\n\r\n</script>\r\n<style scoped>\r\n</style>"], "mappings": "AAwCE,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE,CACZ,CAAC;EACDC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,QAAQ,EAAE,CAAC,CAAC,CAAE;IAEhB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACF,EAAC,GAAI,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,EAAE,EAAE;IAChC,IAAI,CAACK,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAEP;IACAD,QAAQA,CAAA,EAAG;MACT,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIb,IAAG,GAAI,iBAAgB,GAAI,IAAI,CAACI,EAAE;MAC5CL,OAAO,CAACe,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACX,QAAO,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IAED;IACAS,IAAIA,CAAA,EAAG;MACL;MACA,IAAI,CAACC,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB;EAEF;AACF", "ignoreList": []}]}