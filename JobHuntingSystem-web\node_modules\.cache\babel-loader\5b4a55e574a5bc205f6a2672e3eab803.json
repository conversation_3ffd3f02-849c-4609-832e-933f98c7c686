{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Browsingrecords_Manage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Browsingrecords_Manage.vue", "mtime": 1741608933000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "created", "getDatas", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "brid", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "lname", "sessionStorage", "getItem", "para", "sno", "resdata", "length", "isPage", "count", "handleShow", "$router", "push", "path", "query", "id", "pid", "handleEdit"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\Browsingrecords_Manage.vue"], "sourcesContent": ["<template>\n  <el-table\n    :data=\"datalist\"\n    border\n    stripe\n    style=\"width: 100%\"\n    v-loading=\"listLoading\"\n    highlight-current-row\n    max-height=\"600\"\n    size=\"small\"\n  >\n    <el-table-column prop=\"by1\" label=\"职位名称\" align=\"center\"></el-table-column>\n    <el-table-column prop=\"browsetime\" label=\"浏览时间\" align=\"center\"></el-table-column>\n    <el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n      <template #default=\"scope\">\n        <el-button\n          type=\"primary\"\n          size=\"mini\"\n          @click=\"handleShow(scope.$index, scope.row)\"\n          icon=\"el-icon-view\"\n          style=\"padding: 3px 6px 3px 6px\"\n          >详情</el-button\n        >\n        <el-button\n          type=\"danger\"\n          size=\"mini\"\n          @click=\"handleDelete(scope.$index, scope.row)\"\n          icon=\"el-icon-delete\"\n          style=\"padding: 3px 6px 3px 6px\"\n          >删除</el-button\n        >\n      </template>\n    </el-table-column>\n  </el-table>\n  <el-pagination\n    @current-change=\"handleCurrentChange\"\n    :current-page=\"page.currentPage\"\n    :page-size=\"page.pageSize\"\n    background\n    layout=\"total, prev, pager, next, jumper\"\n    :total=\"page.totalCount\"\n    style=\"float: right; margin: 10px 20px 0 0\"\n  ></el-pagination>\n</template>\n<script>\nimport request, { base } from '../../../utils/http';\nexport default {\n  name: 'browsingrecords',\n  components: {},\n  data() {\n    return {\n      page: {\n        currentPage: 1, // 当前页\n        pageSize: 10, // 每页显示条目个数\n        totalCount: 0, // 总条目数\n      },\n      isClear: false,\n\n      listLoading: false, //列表加载状态\n      btnLoading: false, //保存按钮加载状态\n      datalist: [], //表格数据\n    };\n  },\n  created() {\n    this.getDatas();\n  },\n\n  methods: {\n    // 删除浏览记录\n    handleDelete(index, row) {\n      this.$confirm('确认删除该记录吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      })\n        .then(() => {\n          this.listLoading = true;\n          let url = base + '/browsingrecords/del?id=' + row.brid;\n          request.post(url).then((res) => {\n            this.listLoading = false;\n\n            this.$message({\n              message: '删除成功',\n              type: 'success',\n              offset: 320,\n            });\n            this.getDatas();\n          });\n        })\n        .catch(() => {});\n    },\n\n    // 分页\n    handleCurrentChange(val) {\n      this.page.currentPage = val;\n      this.getDatas();\n    },\n\n    //获取列表数据\n    getDatas() {\n      let lname = sessionStorage.getItem('lname');\n      let para = {\n        sno: lname,\n      };\n      this.listLoading = true;\n      let url =\n        base +\n        '/browsingrecords/list?currentPage=' +\n        this.page.currentPage +\n        '&pageSize=' +\n        this.page.pageSize;\n      request.post(url, para).then((res) => {\n        if (res.resdata.length > 0) {\n          this.isPage = true;\n        } else {\n          this.isPage = false;\n        }\n        this.page.totalCount = res.count;\n        this.datalist = res.resdata;\n        this.listLoading = false;\n      });\n    },\n\n    // 查看\n    handleShow(index, row) {\n      this.$router.push({\n        path: '/PositionsView',\n        query: {\n          id: row.pid,\n        },\n      });\n    },\n\n    // 编辑\n    handleEdit(index, row) {\n      this.$router.push({\n        path: '/Browsingrecords_Edit',\n        query: {\n          id: row.brid,\n        },\n      });\n    },\n  },\n};\n</script>\n\n<style></style>\n"], "mappings": ";AA6CA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE,CAAC,CAAC;EACdC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE,CAAE;IAChB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACb,WAAU,GAAI,IAAI;QACvB,IAAIc,GAAE,GAAIvB,IAAG,GAAI,0BAAyB,GAAIiB,GAAG,CAACO,IAAI;QACtDzB,OAAO,CAAC0B,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACjB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACkB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAChB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAiB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAAC5B,IAAI,CAACC,WAAU,GAAI2B,GAAG;MAC3B,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAIoB,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIC,IAAG,GAAI;QACTC,GAAG,EAAEJ;MACP,CAAC;MACD,IAAI,CAACxB,WAAU,GAAI,IAAI;MACvB,IAAIc,GAAE,GACJvB,IAAG,GACH,oCAAmC,GACnC,IAAI,CAACI,IAAI,CAACC,WAAU,GACpB,YAAW,GACX,IAAI,CAACD,IAAI,CAACE,QAAQ;MACpBP,OAAO,CAAC0B,IAAI,CAACF,GAAG,EAAEa,IAAI,CAAC,CAACd,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACY,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAACpC,IAAI,CAACG,UAAS,GAAImB,GAAG,CAACe,KAAK;QAChC,IAAI,CAAC9B,QAAO,GAAIe,GAAG,CAACY,OAAO;QAC3B,IAAI,CAAC7B,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IAED;IACAiC,UAAUA,CAAC1B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC0B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,KAAK,EAAE;UACLC,EAAE,EAAE9B,GAAG,CAAC+B;QACV;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,UAAUA,CAACjC,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC0B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,uBAAuB;QAC7BC,KAAK,EAAE;UACLC,EAAE,EAAE9B,GAAG,CAACO;QACV;MACF,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}