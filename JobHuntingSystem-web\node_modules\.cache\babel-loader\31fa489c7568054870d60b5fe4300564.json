{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Main.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Main.vue", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEhlYWRlciBmcm9tICIuLi9jb21wb25lbnRzL0hlYWRlciI7CmltcG9ydCBMZWZ0TWVudSBmcm9tICIuLi9jb21wb25lbnRzL0xlZnRNZW51IjsKaW1wb3J0IHsgRWxDb25maWdQcm92aWRlciB9IGZyb20gImVsZW1lbnQtcGx1cyI7CmltcG9ydCB6aENuIGZyb20gImVsZW1lbnQtcGx1cy9saWIvbG9jYWxlL2xhbmcvemgtY24iOwppbXBvcnQgIi4uL2Fzc2V0cy9qcy92ZW5kb3IubWluLmpzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJNYWluTGF5b3V0IiwKICBjb21wb25lbnRzOiB7CiAgICBIZWFkZXIsCiAgICBMZWZ0TWVudSwKICAgIFtFbENvbmZpZ1Byb3ZpZGVyLm5hbWVdOiBFbENvbmZpZ1Byb3ZpZGVyCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9jYWxlOiB6aENuCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHt9Cn07"}, {"version": 3, "names": ["Header", "LeftMenu", "ElConfigProvider", "zhCn", "name", "components", "data", "locale", "mounted", "methods"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Main.vue"], "sourcesContent": ["<template>\r\n  <html data-bs-theme=\"light\" data-layout-mode=\"fluid\" data-menu-color=\"dark\" data-topbar-color=\"light\"\r\n        data-layout-position=\"fixed\" data-sidenav-size=\"default\" class=\"menuitem-active\">\r\n  <ElConfigProvider :locale=\"locale\">\r\n\r\n    <div class=\"wrapper\">\r\n      <Header />\r\n\r\n      <LeftMenu />\r\n\r\n      <div class=\"content-page\">\r\n        <div class=\"content\">\r\n          <div class=\"container-fluid\">\r\n            <router-view />\r\n          </div>\r\n        </div>  \r\n      </div>\r\n    </div>\r\n  </ElConfigProvider>\r\n\r\n  </html>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"../components/Header\";\r\nimport LeftMenu from \"../components/LeftMenu\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\nimport zhCn from \"element-plus/lib/locale/lang/zh-cn\";\r\n\r\nimport \"../assets/js/vendor.min.js\";\r\n\r\nexport default {\r\n  name: \"MainLayout\",\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n@import url(../assets/css/app.min.css);\r\n@import url(../assets/css/icons.min.css);\r\n\r\nbody{\r\n  background-image: url(../assets/img/auth-bg.jpg);\r\n  background-size: cover;\r\n}\r\n.form-control{\r\n  border: 1px solid #3bc0c3;\r\n}\r\n.form-control:focus{\r\n  border: 2px solid #c01750;\r\n  box-shadow: none;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": "AAwBA,OAAOA,MAAK,MAAO,sBAAsB;AACzC,OAAOC,QAAO,MAAO,wBAAwB;AAC7C,SAASC,gBAAe,QAAS,cAAc;AAC/C,OAAOC,IAAG,MAAO,oCAAoC;AAErD,OAAO,4BAA4B;AAEnC,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVL,MAAM;IACNC,QAAQ;IACR,CAACC,gBAAgB,CAACE,IAAI,GAAGF;EAC3B,CAAC;EACDI,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAEJ;IACV,CAAC;EACH,CAAC;EACDK,OAAOA,CAAA,EAAG,CAGV,CAAC;EAEDC,OAAO,EAAE,CAET;AACF,CAAC", "ignoreList": []}]}