{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Foot.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Foot.vue", "mtime": 1741618811922}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgZXhwb3J0IGRlZmF1bHQgewogICAgbmFtZTogJ0Zvb3QnLAogICAgZGF0YSgpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBjdXJyZW50WWVhcjogbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpCiAgICAgIH07CiAgICB9LAogICAgbW91bnRlZCgpIHsgfSwKICAgIG1ldGhvZHM6IHt9LAogIH07Cg=="}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Foot.vue"], "names": [], "mappings": ";EAqEE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACb,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/components/Foot.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <footer class=\"footer\">\n    <!-- 主页脚 -->\n    <div class=\"footer__main\">\n      <div class=\"container\">\n        <div class=\"row\">\n          <!-- 关于我们 -->\n          <div class=\"col-md-4 footer-column\">\n            <h4 class=\"footer-title\">关于我们</h4>\n            <p class=\"footer-description\">\n              求职系统致力于为求职者和企业提供高效、便捷的招聘服务平台，帮助求职者找到理想工作，助力企业招揽优秀人才。\n            </p>\n        \n          </div>\n\n          <!-- 快速链接 -->\n          <div class=\"col-md-4 footer-column\">\n            <h4 class=\"footer-title\">快速链接</h4>\n            <ul class=\"footer-links list-no-style\">\n              <li><a href=\"/index\">网站首页</a></li>\n              <li><a href=\"/positionsList\">招聘职位</a></li>\n              <li><a href=\"/companyList\">企业展示</a></li>\n              <li><a href=\"/bbs\">交流论坛</a></li>\n              <li><a href=\"/ai\">AI顾问</a></li>\n            </ul>\n          </div>\n\n          <!-- 联系我们 -->\n          <div class=\"col-md-4 footer-column\">\n            <h4 class=\"footer-title\">联系我们</h4>\n            <ul class=\"footer-contact list-no-style\">\n              <li><i class=\"fas fa-map-marker-alt\"></i> 北京市海淀区中关村大街1号</li>\n              <li><i class=\"fas fa-phone\"></i> 010-6666-6666</li>\n              <li><i class=\"fas fa-envelope\"></i> <EMAIL></li>\n              <li><i class=\"fas fa-clock\"></i> 周一至周五 9:00-18:00</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 版权信息 -->\n    <div class=\"footer__bottom\">\n      <div class=\"container\">\n        <div class=\"row\">\n          <!-- 版权 -->\n          <div class=\"col-md-6\">\n            <p class=\"copyright\">&#9400; {{ currentYear }} 求职系统 - 版权所有</p>\n          </div>\n          <!-- 后台入口 -->\n          <div class=\"col-md-6\">\n            <ul class=\"footer__bottom--links list-no-style\">\n              <li><a href=\"/login\">后台入口</a></li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  </footer>\n\n  <!-- 返回顶部按钮 -->\n  <el-backtop :right=\"100\" :bottom=\"100\">\n    <div class=\"back-to-top\">\n      <i class=\"fas fa-arrow-up\"></i>\n    </div>\n  </el-backtop>\n</template>\n\n<script>\n  export default {\n    name: 'Foot',\n    data() {\n      return {\n        currentYear: new Date().getFullYear()\n      };\n    },\n    mounted() { },\n    methods: {},\n  };\n</script>\n\n<style scoped>\n  /* 主页脚样式 */\n  .footer {\n    background-color: #2c3e50;\n    color: #ecf0f1;\n  }\n\n  .footer__main {\n    padding: 60px 0 40px;\n  }\n\n  .footer-column {\n    margin-bottom: 30px;\n  }\n\n  .footer-title {\n    color: #fff;\n    font-size: 20px;\n    font-weight: 600;\n    margin-bottom: 20px;\n    position: relative;\n    padding-bottom: 10px;\n  }\n\n  .footer-title::after {\n    content: '';\n    position: absolute;\n    left: 0;\n    bottom: 0;\n    width: 50px;\n    height: 3px;\n    background-color: #3498db;\n    border-radius: 1.5px;\n  }\n\n  .footer-description {\n    color: #bdc3c7;\n    line-height: 1.6;\n    margin-bottom: 20px;\n  }\n\n  .footer-social {\n    display: flex;\n    gap: 15px;\n  }\n\n  .social-icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 36px;\n    height: 36px;\n    background-color: rgba(255, 255, 255, 0.1);\n    border-radius: 50%;\n    color: #ecf0f1;\n    transition: all 0.3s ease;\n  }\n\n  .social-icon:hover {\n    background-color: #3498db;\n    color: #fff;\n    transform: translateY(-3px);\n  }\n\n  .footer-links li {\n    margin-bottom: 10px;\n  }\n\n  .footer-links a {\n    color: #bdc3c7;\n    transition: all 0.3s ease;\n    display: inline-block;\n    position: relative;\n  }\n\n  .footer-links a::before {\n    content: '›';\n    margin-right: 8px;\n    color: #3498db;\n  }\n\n  .footer-links a:hover {\n    color: #3498db;\n    text-decoration: none;\n    transform: translateX(5px);\n  }\n\n  .footer-contact li {\n    color: #bdc3c7;\n    margin-bottom: 15px;\n    display: flex;\n    align-items: flex-start;\n  }\n\n  .footer-contact li i {\n    color: #3498db;\n    margin-right: 10px;\n    margin-top: 5px;\n  }\n\n  /* 版权信息样式 */\n  .footer__bottom {\n    background-color: #1a252f;\n    padding: 20px 0;\n    font-size: 14px;\n  }\n\n  .copyright {\n    margin: 0;\n  }\n\n  .footer__bottom--links {\n    display: flex;\n    justify-content: flex-end;\n    margin: 0;\n  }\n\n  .footer__bottom--links li a {\n    color: #bdc3c7;\n    transition: color 0.3s;\n  }\n\n  .footer__bottom--links li a:hover {\n    color: #3498db;\n    text-decoration: none;\n  }\n\n  /* 返回顶部按钮样式 */\n  .back-to-top {\n    height: 100%;\n    width: 100%;\n    background-color: #3498db;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #fff;\n    border-radius: 4px;\n    transition: all 0.3s ease;\n  }\n\n  .back-to-top:hover {\n    background-color: #2980b9;\n    transform: translateY(-2px);\n  }\n\n  .back-to-top i {\n    font-size: 20px;\n  }\n\n  /* 响应式设计 */\n  @media (max-width: 768px) {\n    .footer__bottom--links {\n      justify-content: center;\n      margin-top: 10px;\n    }\n\n    .copyright {\n      text-align: center;\n    }\n  }\n</style>"]}]}