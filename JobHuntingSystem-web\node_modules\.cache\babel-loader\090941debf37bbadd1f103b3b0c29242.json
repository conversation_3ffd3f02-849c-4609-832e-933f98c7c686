{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\Home.vue?vue&type=template&id=a44c444e&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1741615349295}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "$data", "role", "_Fragment", "key", "_hoisted_3", "_hoisted_4", "_hoisted_5", "title", "_hoisted_6", "_toDisplayString", "studentCount", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "companyCount", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "jobCount", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "resumeCount", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "companyJobCount", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "companyResumeCount", "_hoisted_27", "_hoisted_28", "userLname", "_hoisted_29"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">后台首页</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">欢迎页面</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">欢迎页面</h4>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n      <!-- 管理员显示4个统计数据 -->\r\n      <template v-if=\"role === '管理员'\">\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-pink\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-user widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Students\">求职者总数</h6>\r\n              <h2 class=\"my-2\">{{ studentCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">注册求职者总人数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-purple\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-office-building widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Companies\">企业总数</h6>\r\n              <h2 class=\"my-2\">{{ companyCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">企业总数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-info\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-suitcase widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Jobs\">职位总数</h6>\r\n              <h2 class=\"my-2\">{{ jobCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">发布职位总数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-3 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-primary\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-document widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Resumes\">简历投递数</h6>\r\n              <h2 class=\"my-2\">{{ resumeCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">简历投递总数</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 企业显示2个统计数据 -->\r\n      <template v-if=\"role === '企业'\">\r\n        <div class=\"col-xxl-6 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-info\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-suitcase widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Jobs\">发布职位</h6>\r\n              <h2 class=\"my-2\">{{ companyJobCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">已发布职位数量</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-xxl-6 col-sm-6\">\r\n          <div class=\"card widget-flat text-bg-primary\">\r\n            <div class=\"card-body\">\r\n              <div class=\"float-end\">\r\n                <i class=\"el-icon-document widget-icon\"></i>\r\n              </div>\r\n              <h6 class=\"text-uppercase mt-0\" title=\"Resumes\">收到简历</h6>\r\n              <h2 class=\"my-2\">{{ companyResumeCount }}</h2>\r\n              <p class=\"mb-0\">\r\n                <span class=\"text-nowrap\">收到简历投递数量</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div style=\"width: 100%; line-height: 30px; text-align: center; padding: 100px\">\r\n        账号：<b style=\"color: red\">{{ userLname }}</b>， 身份：<b style=\"color: red\">{{ role }}</b><br />\r\n        您好，欢迎使用求职系统！<br />\r\n        请在左侧菜单中选择您要进行的操作！\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import axios from 'axios';\r\n  import { ElMessage } from 'element-plus';\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    data() {\r\n      return {\r\n        userLname: '',\r\n        role: '',\r\n        // 管理员统计数据\r\n        studentCount: 0,\r\n        companyCount: 0,\r\n        jobCount: 0,\r\n        resumeCount: 0,\r\n        // 企业统计数据\r\n        companyJobCount: 0,\r\n        companyResumeCount: 0,\r\n      };\r\n    },\r\n    mounted() {\r\n      this.userLname = sessionStorage.getItem('userLname');\r\n      this.role = sessionStorage.getItem('role');\r\n      this.getStatistics();\r\n    },\r\n    methods: {\r\n      async getStatistics() {\r\n        try {\r\n          const url = base + (this.role === '管理员' ? '/statistics/admin' : '/statistics/company');\r\n\r\n          let params = {};\r\n          if (this.role === '企业') {\r\n            var user = JSON.parse(sessionStorage.getItem('user'));\r\n            params = {\r\n              cid: user.cid,\r\n            };\r\n          }\r\n\r\n          const response = await request.get(url, { params });\r\n\r\n          if (response.code === 200) {\r\n            const data = response.data;\r\n            if (this.role === '管理员') {\r\n              this.studentCount = data.studentCount;\r\n              this.companyCount = data.companyCount;\r\n              this.jobCount = data.jobCount;\r\n              this.resumeCount = data.resumeCount;\r\n            } else {\r\n              this.companyJobCount = data.jobCount;\r\n              this.companyResumeCount = data.resumeCount;\r\n            }\r\n          } else {\r\n            ElMessage.error(response.data.msg || '获取统计数据失败');\r\n          }\r\n        } catch (error) {\r\n          console.error('获取统计数据失败:', error);\r\n          ElMessage.error(error.response?.data?.msg || '获取统计数据失败');\r\n        }\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .widget-icon {\r\n    font-size: 24px;\r\n    background: rgba(255, 255, 255, 0.1);\r\n    height: 40px;\r\n    width: 40px;\r\n    text-align: center;\r\n    line-height: 40px;\r\n    border-radius: 3px;\r\n    display: inline-block;\r\n  }\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAK;;EAaTA,KAAK,EAAC;AAAK;;EAGPA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAW;;EAKhBA,KAAK,EAAC;AAAM;;EAQjBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiC;;EACrCA,KAAK,EAAC;AAAW;;EAKhBA,KAAK,EAAC;AAAM;;EAQjBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAW;;EAKhBA,KAAK,EAAC;AAAM;;EAQjBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAkC;;EACtCA,KAAK,EAAC;AAAW;;EAKhBA,KAAK,EAAC;AAAM;;EAWjBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAW;;EAKhBA,KAAK,EAAC;AAAM;;EAQjBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAkC;;EACtCA,KAAK,EAAC;AAAW;;EAKhBA,KAAK,EAAC;AAAM;;EASnBC,KAA0E,EAA1E;IAAA;IAAA;IAAA;IAAA;EAAA;AAA0E;;EACvEA,KAAkB,EAAlB;IAAA;EAAA;AAAkB;;EAA4BA,KAAkB,EAAlB;IAAA;EAAA;AAAkB;;uBA/G5EC,mBAAA,CAoHM,OApHNC,UAoHM,G,weAvGJC,mBAAA,CAsGM,OAtGNC,UAsGM,GArGJC,mBAAA,iBAAoB,EACJC,KAAA,CAAAC,IAAI,c,cAApBN,mBAAA,CA4DWO,SAAA;IAAAC,GAAA;EAAA,IA3DTN,mBAAA,CAaM,OAbNO,UAaM,GAZJP,mBAAA,CAWM,OAXNQ,UAWM,GAVJR,mBAAA,CASM,OATNS,UASM,G,0BARJT,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAAwC;IAArCJ,KAAK,EAAC;EAA0B,G,gDAErCI,mBAAA,CAA2D;IAAvDJ,KAAK,EAAC,qBAAqB;IAACc,KAAK,EAAC;KAAW,OAAK,sBACtDV,mBAAA,CAAwC,MAAxCW,UAAwC,EAAAC,gBAAA,CAApBT,KAAA,CAAAU,YAAY,kB,0BAChCb,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAM,IACbI,mBAAA,CAAyC;IAAnCJ,KAAK,EAAC;EAAa,GAAC,UAAQ,E,4BAM1CI,mBAAA,CAaM,OAbNc,UAaM,GAZJd,mBAAA,CAWM,OAXNe,UAWM,GAVJf,mBAAA,CASM,OATNgB,UASM,G,0BARJhB,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAAmD;IAAhDJ,KAAK,EAAC;EAAqC,G,gDAEhDI,mBAAA,CAA2D;IAAvDJ,KAAK,EAAC,qBAAqB;IAACc,KAAK,EAAC;KAAY,MAAI,sBACtDV,mBAAA,CAAwC,MAAxCiB,WAAwC,EAAAL,gBAAA,CAApBT,KAAA,CAAAe,YAAY,kB,0BAChClB,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAM,IACbI,mBAAA,CAAqC;IAA/BJ,KAAK,EAAC;EAAa,GAAC,MAAI,E,4BAMtCI,mBAAA,CAaM,OAbNmB,WAaM,GAZJnB,mBAAA,CAWM,OAXNoB,WAWM,GAVJpB,mBAAA,CASM,OATNqB,WASM,G,0BARJrB,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAA4C;IAAzCJ,KAAK,EAAC;EAA8B,G,gDAEzCI,mBAAA,CAAsD;IAAlDJ,KAAK,EAAC,qBAAqB;IAACc,KAAK,EAAC;KAAO,MAAI,sBACjDV,mBAAA,CAAoC,MAApCsB,WAAoC,EAAAV,gBAAA,CAAhBT,KAAA,CAAAoB,QAAQ,kB,0BAC5BvB,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAM,IACbI,mBAAA,CAAuC;IAAjCJ,KAAK,EAAC;EAAa,GAAC,QAAM,E,4BAMxCI,mBAAA,CAaM,OAbNwB,WAaM,GAZJxB,mBAAA,CAWM,OAXNyB,WAWM,GAVJzB,mBAAA,CASM,OATN0B,WASM,G,0BARJ1B,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAA4C;IAAzCJ,KAAK,EAAC;EAA8B,G,kDAEzCI,mBAAA,CAA0D;IAAtDJ,KAAK,EAAC,qBAAqB;IAACc,KAAK,EAAC;KAAU,OAAK,sBACrDV,mBAAA,CAAuC,MAAvC2B,WAAuC,EAAAf,gBAAA,CAAnBT,KAAA,CAAAyB,WAAW,kB,4BAC/B5B,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAM,IACbI,mBAAA,CAAuC;IAAjCJ,KAAK,EAAC;EAAa,GAAC,QAAM,E,6FAO1CM,mBAAA,gBAAmB,EACHC,KAAA,CAAAC,IAAI,a,cAApBN,mBAAA,CA8BWO,SAAA;IAAAC,GAAA;EAAA,IA7BTN,mBAAA,CAaM,OAbN6B,WAaM,GAZJ7B,mBAAA,CAWM,OAXN8B,WAWM,GAVJ9B,mBAAA,CASM,OATN+B,WASM,G,4BARJ/B,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAA4C;IAAzCJ,KAAK,EAAC;EAA8B,G,kDAEzCI,mBAAA,CAAsD;IAAlDJ,KAAK,EAAC,qBAAqB;IAACc,KAAK,EAAC;KAAO,MAAI,sBACjDV,mBAAA,CAA2C,MAA3CgC,WAA2C,EAAApB,gBAAA,CAAvBT,KAAA,CAAA8B,eAAe,kB,4BACnCjC,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAM,IACbI,mBAAA,CAAwC;IAAlCJ,KAAK,EAAC;EAAa,GAAC,SAAO,E,4BAMzCI,mBAAA,CAaM,OAbNkC,WAaM,GAZJlC,mBAAA,CAWM,OAXNmC,WAWM,GAVJnC,mBAAA,CASM,OATNoC,WASM,G,4BARJpC,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAA4C;IAAzCJ,KAAK,EAAC;EAA8B,G,kDAEzCI,mBAAA,CAAyD;IAArDJ,KAAK,EAAC,qBAAqB;IAACc,KAAK,EAAC;KAAU,MAAI,sBACpDV,mBAAA,CAA8C,MAA9CqC,WAA8C,EAAAzB,gBAAA,CAA1BT,KAAA,CAAAmC,kBAAkB,kB,4BACtCtC,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAM,IACbI,mBAAA,CAAyC;IAAnCJ,KAAK,EAAC;EAAa,GAAC,UAAQ,E,6FAO5CI,mBAAA,CAIM,OAJNuC,WAIM,G,6CAJ0E,MAC3E,IAAAvC,mBAAA,CAAyC,KAAzCwC,WAAyC,EAAA5B,gBAAA,CAAhBT,KAAA,CAAAsC,SAAS,kB,6CAAO,OAAK,IAAAzC,mBAAA,CAAoC,KAApC0C,WAAoC,EAAA9B,gBAAA,CAAXT,KAAA,CAAAC,IAAI,kB,4BAAOJ,mBAAA,CAAM,sC,6CAAA,eAC/E,I,4BAAAA,mBAAA,CAAM,sC,6CAAA,qBAEpB,G", "ignoreList": []}]}