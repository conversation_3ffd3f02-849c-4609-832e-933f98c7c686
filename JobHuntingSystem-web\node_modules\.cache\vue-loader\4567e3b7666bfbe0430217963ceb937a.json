{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue?vue&type=style&index=0&id=effc193a&scoped=true&lang=css", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue", "mtime": 1741536460000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucG9zaXRpb25zLWxpc3Qgew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQoucm93IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBtYXJnaW46IC0xNXB4Ow0KfQ0KDQouY29sLWxnLTYgew0KICBwYWRkaW5nOiAxNXB4Ow0KICB3aWR0aDogNTAlOw0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHsNCiAgLmNvbC1sZy02IHsNCiAgICB3aWR0aDogNTAlOw0KICB9DQp9DQoNCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAuY29sLWxnLTYgew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQp9DQoNCi5qb2ItaW5mbyB7DQogIHRleHQtYWxpZ246IGxlZnQ7DQogIHBhZGRpbmc6IDEwcHggMDsNCn0NCg0KLmpvYi1pbmZvIHAgew0KICBtYXJnaW46IDVweCAwOw0KICBjb2xvcjogIzY2NjsNCn0NCg0KLmpvYi1pbmZvIGkgew0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgY29sb3I6ICMzYmMwYzM7DQp9DQoNCi5pY29uLWJveCB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgaGVpZ2h0OiAxMDAlOw0KfQ0KDQouaWNvbi1ib3g6aG92ZXIgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7DQogIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjIpOw0KfQ0KDQouaGVhZGluZy10aXRsZSB7DQogIGNvbG9yOiAjMzMzOw0KICBmb250LXNpemU6IDE4cHg7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KfQ0KDQouaWNvbi1ib3hfX2J0biB7DQogIG1hcmdpbi10b3A6IDE1cHg7DQogIHRleHQtYWxpZ246IHJpZ2h0Ow0KfQ0KDQouaWNvbi1ib3hfX2J0biBhIHsNCiAgY29sb3I6ICMzYmMwYzM7DQogIHRleHQtZGVjb3JhdGlvbjogbm9uZTsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmljb24tYm94X19idG4gYTpob3ZlciB7DQogIGNvbG9yOiAjMmE4ZjkxOw0KfQ0KDQouc2FsYXJ5IHsNCiAgY29sb3I6ICNmZjRkNGY7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQoucGFnaW5hdGlvbiB7DQogIG1hcmdpbi10b3A6IDMwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg=="}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue"], "names": [], "mappings": ";AAuFA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACZ;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/PositionsList.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"positions-list\">\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-6 col-md-6 wow fadeIn\" data-wow-delay=\"300ms\" v-for=\"item in polist\" :key=\"item.pid\">\r\n        <div class=\"icon-box icon-box--border\">\r\n          <div class=\"icon-box__heading\">\r\n            <h3 class=\"heading-title\">{{ item.pname }}</h3>\r\n          </div>\r\n          <div class=\"icon-box__content\">\r\n            <div class=\"job-info\">\r\n              <p><i class=\"el-icon-location\"></i> 工作地点：{{ item.wlocation }}</p>\r\n              <p><i class=\"el-icon-user\"></i> 招聘人数：{{ item.rnumber }}</p>\r\n              <p>\r\n                <i class=\"el-icon-money\"></i> 薪资待遇：<span class=\"salary\">{{\r\n                  item.streatment\r\n                }}</span>\r\n              </p>\r\n              <p><i class=\"el-icon-time\"></i> 发布时间：{{ item.ptime }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon-box__btn\">\r\n            <a :href=\"'positionsView?id=' + item.pid\">\r\n              查看详情<span><i class=\"fa fa-chevron-right\"></i></span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" class=\"pagination\">\r\n    </el-pagination>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from '../../../utils/http';\r\nexport default {\r\n  name: 'PositionsList',\r\n  data() {\r\n    return {\r\n      page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 8, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n      },\r\n      polist: [],\r\n      catid: '',\r\n      keyword: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.catid = this.$route.query.catid == null ? '' : this.$route.query.catid;\r\n    this.keyword = this.$route.query.keyword == null ? '' : this.$route.query.keyword;\r\n    this.getDatas();\r\n  },\r\n  methods: {\r\n    // 分页\r\n    handleCurrentChange(val) {\r\n      this.page.currentPage = val;\r\n      this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {\r\n        pflag: '开放', // 只显示开放状态的职位\r\n        pflag2: '审核通过', // 只显示审核通过的职位\r\n        catid: this.catid,\r\n        condition: ' and pname like \"%' + this.keyword + '%\" ',\r\n      };\r\n      let url =\r\n        base +\r\n        '/positions/list?currentPage=' +\r\n        this.page.currentPage +\r\n        '&pageSize=' +\r\n        this.page.pageSize;\r\n      request.post(url, para).then((res) => {\r\n        this.polist = res.resdata;\r\n        this.page.totalCount = res.count;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.positions-list {\r\n  padding: 20px;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -15px;\r\n}\r\n\r\n.col-lg-6 {\r\n  padding: 15px;\r\n  width: 50%;\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .col-lg-6 {\r\n    width: 50%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .col-lg-6 {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.job-info {\r\n  text-align: left;\r\n  padding: 10px 0;\r\n}\r\n\r\n.job-info p {\r\n  margin: 5px 0;\r\n  color: #666;\r\n}\r\n\r\n.job-info i {\r\n  margin-right: 5px;\r\n  color: #3bc0c3;\r\n}\r\n\r\n.icon-box {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  height: 100%;\r\n}\r\n\r\n.icon-box:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.heading-title {\r\n  color: #333;\r\n  font-size: 18px;\r\n  margin-bottom: 15px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.icon-box__btn {\r\n  margin-top: 15px;\r\n  text-align: right;\r\n}\r\n\r\n.icon-box__btn a {\r\n  color: #3bc0c3;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.icon-box__btn a:hover {\r\n  color: #2a8f91;\r\n}\r\n\r\n.salary {\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n}\r\n\r\n.pagination {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}