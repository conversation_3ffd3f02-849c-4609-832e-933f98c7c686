{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\jobcategories\\JobcategoriesEdit.vue?vue&type=template&id=3b110143", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\jobcategories\\JobcategoriesEdit.vue", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCiAgPGRpdiBjbGFzcz0icm93Ij4NCiAgICA8ZGl2IGNsYXNzPSJjb2wtMTIiPg0KICAgICAgPGRpdiBjbGFzcz0icGFnZS10aXRsZS1ib3giPg0KICAgICAgICA8ZGl2IGNsYXNzPSJwYWdlLXRpdGxlLXJpZ2h0Ij4NCiAgICAgICAgICA8b2wgY2xhc3M9ImJyZWFkY3J1bWIgbS0wIj4NCiAgICAgICAgICAgIDxsaSBjbGFzcz0iYnJlYWRjcnVtYi1pdGVtIj48YSBpZD0idGl0bGUyIj7ogYzkvY3liIbnsbvnrqHnkIY8L2E+PC9saT4NCiAgICAgICAgICAgIDxsaSBjbGFzcz0iYnJlYWRjcnVtYi1pdGVtIGFjdGl2ZSIgaWQ9InRpdGxlMyI+57yW6L6R6IGM5L2N5YiG57G7PC9saT4NCiAgICAgICAgICA8L29sPg0KICAgICAgICA8L2Rpdj4NCiAgICAgICAgPGg0IGNsYXNzPSJwYWdlLXRpdGxlIiBpZD0idGl0bGUxIj7nvJbovpHogYzkvY3liIbnsbs8L2g0Pg0KICAgICAgPC9kaXY+DQogICAgPC9kaXY+DQogICAgIDxlbC1mb3JtIDptb2RlbD0iZm9ybURhdGEiIGxhYmVsLXdpZHRoPSIyMCUiIHJlZj0iZm9ybURhdGFSZWYiIDpydWxlcz0iYWRkcnVsZXMiICBhbGlnbj0ibGVmdCI+DQo8ZWwtZm9ybS1pdGVtIGxhYmVsPSLliIbnsbvlkI3np7AiIHByb3A9ImNhdG5hbWUiPg0KPGVsLWlucHV0IHYtbW9kZWw9ImZvcm1EYXRhLmNhdG5hbWUiIHBsYWNlaG9sZGVyPSLliIbnsbvlkI3np7AiICBzdHlsZT0id2lkdGg6NTAlOyIgPjwvZWwtaW5wdXQ+DQo8L2VsLWZvcm0taXRlbT4NCjxlbC1mb3JtLWl0ZW0+DQo8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIHNpemU9InNtYWxsIiBAY2xpY2s9InNhdmUiIDpsb2FkaW5nPSJidG5Mb2FkaW5nIiBpY29uPSJlbC1pY29uLXVwbG9hZCI+5o+QIOS6pDwvZWwtYnV0dG9uPg0KPGVsLWJ1dHRvbiB0eXBlPSJpbmZvIiBzaXplPSJzbWFsbCIgQGNsaWNrPSJnb0JhY2siIGljb249ImVsLWljb24tYmFjayI+6L+UIOWbnjwvZWwtYnV0dG9uPg0KPC9lbC1mb3JtLWl0ZW0+DQo8L2VsLWZvcm0+DQoNCg0KICA8L2Rpdj4NCg0K"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\jobcategories\\JobcategoriesEdit.vue"], "names": [], "mappings": ";;EAEE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;KACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAGP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/jobcategories/JobcategoriesEdit.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">职位分类管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">编辑职位分类</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">编辑职位分类</h4>\r\n      </div>\r\n    </div>\r\n     <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"分类名称\" prop=\"catname\">\r\n<el-input v-model=\"formData.catname\" placeholder=\"分类名称\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n  </div>\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'JobcategoriesEdit',\n  components: {\n    \n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          catname: [{ required: true, message: '请输入分类名称', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n    },\r\n\r\n \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/jobcategories/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            \n            \n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/jobcategories/update\";\n              this.btnLoading = true;\n              \n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });\n                  this.$router.push({\n                    path: \"/JobcategoriesManage\",\n                  });\n                } else {\n                  this.$message({\n                    message:res.msg,\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n                this.btnLoading = false;\n              });\n            }\n    \n          });\n        },\n        \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/JobcategoriesManage\",\n          });\n        },       \n              \n          \n           \n           \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}