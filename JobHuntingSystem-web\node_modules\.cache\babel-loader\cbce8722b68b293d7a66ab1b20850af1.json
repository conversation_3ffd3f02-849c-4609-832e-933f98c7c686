{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue", "mtime": 1741617188960}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "formData", "resumeData", "professionalsList", "professionalName", "created", "getUserInfo", "getProfessionals", "getResumeInfo", "methods", "lname", "sessionStorage", "getItem", "url", "post", "then", "res", "code", "resdata", "getProfessionalName", "params", "currentPage", "pageSize", "proid", "length", "professional", "find", "p", "proname", "sno", "goToAddResume", "$router", "push"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue"], "sourcesContent": ["<template>\r\n  <div class=\"resume-preview\">\r\n    <div class=\"resume-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"avatar-section\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" class=\"avatar\" />\r\n        </div>\r\n        <div class=\"basic-info\">\r\n          <h1 class=\"name\">{{ formData.sname }}</h1>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>性别：{{ formData.gender }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-date\"></i>\r\n              <span>年龄：{{ formData.age }}岁</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-phone\"></i>\r\n              <span>电话：{{ formData.phone }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-school\"></i>\r\n              <span>专业：{{ professionalName }}</span>\r\n            </div>\r\n          \r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"resume-body\">\r\n      <div class=\"resume-section\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          简历信息\r\n        </h2>\r\n        <div class=\"resume-content\" v-if=\"resumeData\">\r\n          <div class=\"info-row\">\r\n            <label>教育背景：</label>\r\n            <div class=\"content\" v-html=\"resumeData.education\"></div>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <label>实习经历：</label>\r\n            <div class=\"content\" v-html=\"resumeData.parttimejob\"></div>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <label>个人介绍：</label>\r\n            <div class=\"content\" v-html=\"resumeData.introduction\"></div>\r\n          </div>\r\n        </div>\r\n        <div class=\"no-resume\" v-else>\r\n          <el-empty description=\"暂无简历信息\">\r\n            <el-button type=\"primary\" @click=\"goToAddResume\">创建简历</el-button>\r\n          </el-empty>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    name: 'ResumePreview',\r\n    data() {\r\n      return {\r\n        formData: {},\r\n        resumeData: null,\r\n        professionalsList: [],\r\n        professionalName: '',\r\n      };\r\n    },\r\n    created() {\r\n      this.getUserInfo();\r\n      this.getProfessionals();\r\n      this.getResumeInfo();\r\n    },\r\n    methods: {\r\n      // 获取用户信息\r\n      getUserInfo() {\r\n        let lname = sessionStorage.getItem('lname');\r\n        let url = base + '/students/get?id=' + lname;\r\n        request.post(url).then((res) => {\r\n          if (res.code == 200) {\r\n            this.formData = res.resdata;\r\n            this.getProfessionalName();\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取专业列表\r\n      getProfessionals() {\r\n        let url = base + '/professionals/list';\r\n        request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n          if (res.code == 200) {\r\n            this.professionalsList = res.resdata;\r\n            this.getProfessionalName();\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取专业名称\r\n      getProfessionalName() {\r\n        if (this.formData.proid && this.professionalsList.length > 0) {\r\n          const professional = this.professionalsList.find((p) => p.proid === this.formData.proid);\r\n          this.professionalName = professional ? professional.proname : '';\r\n        }\r\n      },\r\n\r\n      // 获取简历信息\r\n      getResumeInfo() {\r\n        let lname = sessionStorage.getItem('lname');\r\n        let url = base + '/resume/list';\r\n        request.post(url, { sno: lname }, { params: { currentPage: 1, pageSize: 1 } }).then((res) => {\r\n          if (res.code == 200 && res.resdata.length > 0) {\r\n            this.resumeData = res.resdata[0];\r\n          }\r\n        });\r\n      },\r\n\r\n      // 跳转到创建简历页面\r\n      goToAddResume() {\r\n        this.$router.push('/Resume_Add');\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .resume-preview {\r\n    max-width: 1000px;\r\n    margin: 20px auto;\r\n    background: #fff;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    border-radius: 8px;\r\n  }\r\n\r\n  .resume-header {\r\n    background: linear-gradient(135deg, #3bc0c3 0%, #42d3d7 100%);\r\n    padding: 40px;\r\n    border-radius: 8px 8px 0 0;\r\n    color: #fff;\r\n  }\r\n\r\n  .header-content {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 40px;\r\n  }\r\n\r\n  .avatar-section {\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .avatar {\r\n    width: 150px;\r\n    height: 150px;\r\n    border-radius: 75px;\r\n    border: 4px solid rgba(255, 255, 255, 0.3);\r\n  }\r\n\r\n  .basic-info {\r\n    flex-grow: 1;\r\n  }\r\n\r\n  .name {\r\n    font-size: 28px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n    gap: 15px;\r\n  }\r\n\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .info-item i {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .resume-body {\r\n    padding: 40px;\r\n  }\r\n\r\n  .resume-section {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 20px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .section-title i {\r\n    color: #3bc0c3;\r\n  }\r\n\r\n  .info-row {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-row label {\r\n    font-weight: bold;\r\n    color: #666;\r\n    display: block;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .content {\r\n    line-height: 1.6;\r\n    color: #333;\r\n  }\r\n\r\n  .status-employed {\r\n    color: #67c23a;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .status-unemployed {\r\n    color: #f56c6c;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .no-resume {\r\n    text-align: center;\r\n    padding: 40px 0;\r\n  }\r\n</style>"], "mappings": ";;;AA+DE,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AAEnD,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,CAAC,CAAC;MACZC,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,EAAE;MACrBC,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB,CAAC;EACDC,OAAO,EAAE;IACP;IACAH,WAAWA,CAAA,EAAG;MACZ,IAAII,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIC,GAAE,GAAIf,IAAG,GAAI,mBAAkB,GAAIY,KAAK;MAC5Cb,OAAO,CAACiB,IAAI,CAACD,GAAG,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAC9B,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAAChB,QAAO,GAAIe,GAAG,CAACE,OAAO;UAC3B,IAAI,CAACC,mBAAmB,CAAC,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAZ,gBAAgBA,CAAA,EAAG;MACjB,IAAIM,GAAE,GAAIf,IAAG,GAAI,qBAAqB;MACtCD,OAAO,CAACiB,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,EAAE;QAAEO,MAAM,EAAE;UAAEC,WAAW,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAI;MAAE,CAAC,CAAC,CAACP,IAAI,CAAEC,GAAG,IAAK;QACjF,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAACd,iBAAgB,GAAIa,GAAG,CAACE,OAAO;UACpC,IAAI,CAACC,mBAAmB,CAAC,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAA,mBAAmBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAAClB,QAAQ,CAACsB,KAAI,IAAK,IAAI,CAACpB,iBAAiB,CAACqB,MAAK,GAAI,CAAC,EAAE;QAC5D,MAAMC,YAAW,GAAI,IAAI,CAACtB,iBAAiB,CAACuB,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACJ,KAAI,KAAM,IAAI,CAACtB,QAAQ,CAACsB,KAAK,CAAC;QACxF,IAAI,CAACnB,gBAAe,GAAIqB,YAAW,GAAIA,YAAY,CAACG,OAAM,GAAI,EAAE;MAClE;IACF,CAAC;IAED;IACApB,aAAaA,CAAA,EAAG;MACd,IAAIE,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIC,GAAE,GAAIf,IAAG,GAAI,cAAc;MAC/BD,OAAO,CAACiB,IAAI,CAACD,GAAG,EAAE;QAAEgB,GAAG,EAAEnB;MAAM,CAAC,EAAE;QAAEU,MAAM,EAAE;UAAEC,WAAW,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE;MAAE,CAAC,CAAC,CAACP,IAAI,CAAEC,GAAG,IAAK;QAC3F,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAE,IAAKD,GAAG,CAACE,OAAO,CAACM,MAAK,GAAI,CAAC,EAAE;UAC7C,IAAI,CAACtB,UAAS,GAAIc,GAAG,CAACE,OAAO,CAAC,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAY,aAAaA,CAAA,EAAG;MACd,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,aAAa,CAAC;IAClC;EACF;AACF,CAAC", "ignoreList": []}]}