{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Header.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Header.vue", "mtime": 1741615313504}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcGxvZ286IHJlcXVpcmUoJy4uL2Fzc2V0cy9pbWcvYXZhdGFyLTEuanBnJyksCiAgICAgIHVzZXJMbmFtZTogJycsCiAgICAgIHJvbGU6ICcnLAogICAgICBjbGlja25hdjogZmFsc2UKICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy51c2VyTG5hbWUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCd1c2VyTG5hbWUnKTsKICAgIHRoaXMucm9sZSA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3JvbGUnKTsKICAgIGlmICh0aGlzLnJvbGUgPT09ICfkvIHkuJonKSB7CiAgICAgIHZhciB1c2VyID0gSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCd1c2VyJykpOwogICAgICB0aGlzLnBsb2dvID0gJ2h0dHA6Ly9sb2NhbGhvc3Q6ODA4OC9Kb2JIdW50aW5nU3lzdGVtLycgKyB1c2VyLmxvZ287CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZWxlY3Qoa2V5LCBrZXlQYXRoKSB7CiAgICAgIGNvbnNvbGUubG9nKGtleSwga2V5UGF0aCk7CiAgICB9LAogICAgc2hvd2V4aXN0cygpIHsKICAgICAgY29uc29sZS5sb2coMzMzKTsKICAgICAgdGhpcy5zaG93ZXhpc3QgPSAhdGhpcy5zaG93ZXhpc3Q7CiAgICB9LAogICAgZXhpdDogZnVuY3Rpb24gKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTpgIDlh7rlkJc/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXJMbmFtZScpOwogICAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ3JvbGUnKTsKICAgICAgICBfdGhpcy4kcm91dGVyLnB1c2goJy9sb2dpbicpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["data", "plogo", "require", "userLname", "role", "clicknav", "mounted", "sessionStorage", "getItem", "user", "JSON", "parse", "logo", "methods", "handleSelect", "key", "keyP<PERSON>", "console", "log", "showexists", "showexist", "exit", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "$router", "push", "catch"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Header.vue"], "sourcesContent": ["<template>\r\n  <div class=\"navbar-custom\">\r\n    <div class=\"topbar container-fluid\">\r\n      <div class=\"d-flex align-items-center gap-1\">\r\n        <div class=\"logo-topbar\">\r\n          <a href=\"\" class=\"logo-light\">\r\n            <span class=\"logo-lg\"> 求职系统 </span>\r\n            <span class=\"logo-sm\"> 求职系统 </span>\r\n          </a>\r\n          <a href=\"\" class=\"logo-dark\">\r\n            <span class=\"logo-lg\"> 求职系统 </span>\r\n            <span class=\"logo-sm\"> 求职系统 </span>\r\n          </a>\r\n        </div>\r\n      </div>\r\n      <ul class=\"topbar-menu d-flex align-items-center gap-3\">\r\n        <li class=\"dropdown\">\r\n          <a class=\"nav-link dropdown-toggle arrow-none nav-user\" data-bs-toggle=\"dropdown\" href=\"#\" role=\"button\"\r\n            aria-haspopup=\"false\" aria-expanded=\"false\">\r\n            <span class=\"account-user-avatar\">\r\n              <img :src=\"plogo\" alt=\"user-image\" width=\"32\" class=\"rounded-circle\" style=\"width: 32px; height: 32px\" />\r\n            </span>\r\n            <span class=\"d-lg-block d-none\">\r\n              <h5 class=\"my-0 fw-normal\">\r\n                【<b style=\"color: #d03f3f\">{{ role }}</b>】{{ userLname }}\r\n                <i class=\"ri-arrow-down-s-line d-none d-sm-inline-block align-middle\"></i>\r\n              </h5>\r\n            </span>\r\n          </a>\r\n          <div class=\"dropdown-menu dropdown-menu-end dropdown-menu-animated profile-dropdown\">\r\n            <div class=\"dropdown-header noti-title\">\r\n              <h6 class=\"text-overflow m-0\">欢迎您 !</h6>\r\n            </div>\r\n\r\n            <a href=\"/\" class=\"dropdown-item\" target=\"_blank\">\r\n              <i class=\"ri-home-2-fill fs-18 align-middle me-1\"></i>\r\n              <span>网站首页</span>\r\n            </a>\r\n            <a href=\"/Password\" class=\"dropdown-item\">\r\n              <i class=\"ri-lock-password-line fs-18 align-middle me-1\"></i>\r\n              <span>修改密码</span>\r\n            </a>\r\n            <a href=\"javascript:void(0)\" class=\"dropdown-item\" @click=\"exit\">\r\n              <i class=\"ri-logout-box-line fs-18 align-middle me-1\"></i>\r\n              <span>退出登录</span>\r\n            </a>\r\n          </div>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        plogo: require('../assets/img/avatar-1.jpg'),\r\n        userLname: '',\r\n        role: '',\r\n        clicknav: false,\r\n      };\r\n    },\r\n    mounted() {\r\n      this.userLname = sessionStorage.getItem('userLname');\r\n      this.role = sessionStorage.getItem('role');\r\n\r\n      if (this.role === '企业') {\r\n        var user = JSON.parse(sessionStorage.getItem('user'));\r\n        this.plogo = 'http://localhost:8088/JobHuntingSystem/' + user.logo;\r\n      }\r\n    },\r\n    methods: {\r\n      handleSelect(key, keyPath) {\r\n        console.log(key, keyPath);\r\n      },\r\n      showexists() {\r\n        console.log(333);\r\n        this.showexist = !this.showexist;\r\n      },\r\n\r\n      exit: function () {\r\n        var _this = this;\r\n        this.$confirm('确认退出吗?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        })\r\n          .then(() => {\r\n            sessionStorage.removeItem('userLname');\r\n            sessionStorage.removeItem('role');\r\n            _this.$router.push('/login');\r\n          })\r\n          .catch(() => { });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style>\r\n  .logo-sm {\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    /* 超宽自动隐藏 */\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .logo-lg {\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n  }\r\n</style>"], "mappings": ";AAqDE,eAAe;EACbA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC5CC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACH,SAAQ,GAAII,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACJ,IAAG,GAAIG,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IAE1C,IAAI,IAAI,CAACJ,IAAG,KAAM,IAAI,EAAE;MACtB,IAAIK,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACJ,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACrD,IAAI,CAACP,KAAI,GAAI,yCAAwC,GAAIQ,IAAI,CAACG,IAAI;IACpE;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,YAAYA,CAACC,GAAG,EAAEC,OAAO,EAAE;MACzBC,OAAO,CAACC,GAAG,CAACH,GAAG,EAAEC,OAAO,CAAC;IAC3B,CAAC;IACDG,UAAUA,CAAA,EAAG;MACXF,OAAO,CAACC,GAAG,CAAC,GAAG,CAAC;MAChB,IAAI,CAACE,SAAQ,GAAI,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC;IAEDC,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,IAAIC,KAAI,GAAI,IAAI;MAChB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACVpB,cAAc,CAACqB,UAAU,CAAC,WAAW,CAAC;QACtCrB,cAAc,CAACqB,UAAU,CAAC,MAAM,CAAC;QACjCN,KAAK,CAACO,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;MAC9B,CAAC,EACAC,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB;EACF;AACF,CAAC", "ignoreList": []}]}