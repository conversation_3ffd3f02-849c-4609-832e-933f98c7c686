{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue?vue&type=style&index=0&id=04724886&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue", "mtime": 1749118278931}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749091667740}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749091668860}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749091668169}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyView.vue"], "names": [], "mappings": ";EAwME,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/CompanyView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"company-detail\">\r\n    <!-- 企业基本信息 -->\r\n    <div class=\"company-card\">\r\n      <div class=\"company-header\">\r\n        <div class=\"company-logo\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.logo\" alt=\"企业logo\" />\r\n        </div>\r\n        <div class=\"company-qualification\" v-if=\"formData.qualification\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.qualification\" alt=\"资质图片\" />\r\n        </div>\r\n        <div class=\"company-info\">\r\n          <div class=\"name-action\">\r\n            <h1 class=\"company-name\">{{ formData.comname }}</h1>\r\n          \r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-location\"></i>\r\n            <span>地址：{{ formData.address }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-phone\"></i>\r\n            <span>联系方式：{{ formData.contact }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n            <span>企业规模：{{ formData.scale }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-collection\"></i>\r\n            <span>企业性质：{{ formData.nature }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <i class=\"el-icon-time\"></i>\r\n            <span>添加时间：{{ formData.addtime }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"company-description\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          企业简介\r\n        </h2>\r\n        <div class=\"description-content\" v-html=\"formData.introduction\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 企业招聘职位列表 -->\r\n    <div class=\"positions-card\">\r\n      <h2 class=\"section-title\">\r\n        <i class=\"el-icon-suitcase\"></i>\r\n        招聘职位\r\n      </h2>\r\n\r\n      <div v-if=\"positionsList.length > 0\" class=\"positions-list\">\r\n        <div v-for=\"position in positionsList\" :key=\"position.pid\" class=\"position-item\">\r\n          <div class=\"position-header\">\r\n            <h3 class=\"position-title\">{{ position.pname }}</h3>\r\n            <span class=\"salary\">{{ position.streatment }}</span>\r\n          </div>\r\n          <div class=\"position-info\">\r\n            <span><i class=\"el-icon-location\"></i>{{ position.wlocation }}</span>\r\n            <span><i class=\"el-icon-user\"></i>招聘人数：{{ position.rnumber }}人</span>\r\n            <span><i class=\"el-icon-time\"></i>发布时间：{{ position.ptime }}</span>\r\n          </div>\r\n          <div class=\"position-footer\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"viewPosition(position.pid)\"\r\n              :disabled=\"position.pflag !== '开放'\">\r\n              查看详情\r\n            </el-button>\r\n            <el-tag :type=\"position.pflag === '开放' ? 'success' : 'info'\" size=\"small\">\r\n              {{ position.pflag }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <el-empty v-else description=\"暂无招聘职位\"></el-empty>\r\n    </div>\r\n\r\n    <!-- 发送私信对话框 -->\r\n    <el-dialog title=\"发送私信\" v-model=\"dialogVisible\" width=\"500px\">\r\n      <el-form :model=\"messageForm\" ref=\"messageFormRef\" :rules=\"rules\">\r\n        <el-form-item prop=\"content\">\r\n          <el-input type=\"textarea\" v-model=\"messageForm.content\" :rows=\"4\" placeholder=\"请输入私信内容\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"sendMessage\" :loading=\"sending\">发 送</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    name: 'CompanyView',\r\n    data() {\r\n      return {\r\n        id: '',\r\n        formData: {},\r\n        positionsList: [],\r\n        dialogVisible: false,\r\n        sending: false,\r\n        messageForm: {\r\n          content: '',\r\n          clname: '', // 企业账号\r\n          sno: '', // 求职者账号\r\n        },\r\n        rules: {\r\n          content: [\r\n            { required: true, message: '请输入私信内容', trigger: 'blur' },\r\n            { min: 1, max: 500, message: '长度在1到500个字符', trigger: 'blur' }\r\n          ]\r\n        }\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id;\r\n      this.getCompanyData();\r\n      this.getPositionsList();\r\n    },\r\n    methods: {\r\n      // 获取企业信息\r\n      async getCompanyData() {\r\n        try {\r\n          const res = await request.post(base + '/company/get?id=' + this.id);\r\n          if (res.code === 200) {\r\n            this.formData = res.resdata;\r\n          }\r\n        } catch (error) {\r\n          console.error('获取企业信息失败:', error);\r\n          this.$message({\r\n            message: '获取企业信息失败',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      },\r\n\r\n      // 获取企业发布的职位列表\r\n      async getPositionsList() {\r\n        try {\r\n          const res = await request.post(\r\n            base + '/positions/list',\r\n            { cid: this.id, pflag: '开放', pflag2: '审核通过' },\r\n            { params: { currentPage: 1, pageSize: 100 } }\r\n          );\r\n          if (res.code === 200) {\r\n            this.positionsList = res.resdata;\r\n          }\r\n        } catch (error) {\r\n          console.error('获取职位列表失败:', error);\r\n          this.$message({\r\n            message: '获取职位列表失败',\r\n            type: 'error',\r\n            offset: 320,\r\n          });\r\n        }\r\n      },\r\n\r\n      // 查看职位详情\r\n      viewPosition(pid) {\r\n        this.$router.push({\r\n          path: '/positionsView',\r\n          query: { id: pid },\r\n        });\r\n      },\r\n\r\n      // 处理发送私信按钮点击\r\n      handleSendMessage() {\r\n        // 检查是否登录\r\n        const sno = sessionStorage.getItem('lname');\r\n        if (!sno) {\r\n          this.$message({\r\n            message: '请先登录后再发送私信',\r\n            type: 'warning',\r\n            offset: 320\r\n          });\r\n          this.$router.push('/Slogin');\r\n          return;\r\n        }\r\n\r\n        this.messageForm.sno = sno;\r\n        this.messageForm.clname = this.formData.clname;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n   \r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .company-detail {\r\n    max-width: 1200px;\r\n    margin: 20px auto;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .company-card,\r\n  .positions-card {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    padding: 30px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .company-header {\r\n    display: flex;\r\n    gap: 30px;\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .company-logo {\r\n    width: 120px;\r\n    height: 120px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .company-logo img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .company-qualification {\r\n    width: 120px;\r\n    height: 120px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .company-qualification img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .company-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .name-action {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .company-name {\r\n    margin: 0;\r\n  }\r\n\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    margin-bottom: 10px;\r\n    color: #666;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 18px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    border-bottom: 2px solid #409eff;\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .description-content {\r\n    color: #666;\r\n    line-height: 1.8;\r\n  }\r\n\r\n  .positions-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n    gap: 20px;\r\n  }\r\n\r\n  .position-item {\r\n    border: 1px solid #eee;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .position-item:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .position-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .position-title {\r\n    font-size: 16px;\r\n    color: #333;\r\n    margin: 0;\r\n  }\r\n\r\n  .salary {\r\n    color: #ff4d4f;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .position-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin-bottom: 15px;\r\n    color: #666;\r\n  }\r\n\r\n  .position-info span {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 5px;\r\n  }\r\n\r\n  .position-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: right;\r\n    display: block;\r\n  }\r\n</style>"]}]}