{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue?vue&type=template&id=effc193a&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue", "mtime": 1741536460000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$data", "polist", "item", "key", "pid", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_toDisplayString", "pname", "_hoisted_6", "_hoisted_7", "wlocation", "rnumber", "_hoisted_8", "streatment", "ptime", "_hoisted_9", "href", "_createVNode", "_component_el_pagination", "onCurrentChange", "$options", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\PositionsList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"positions-list\">\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-6 col-md-6 wow fadeIn\" data-wow-delay=\"300ms\" v-for=\"item in polist\" :key=\"item.pid\">\r\n        <div class=\"icon-box icon-box--border\">\r\n          <div class=\"icon-box__heading\">\r\n            <h3 class=\"heading-title\">{{ item.pname }}</h3>\r\n          </div>\r\n          <div class=\"icon-box__content\">\r\n            <div class=\"job-info\">\r\n              <p><i class=\"el-icon-location\"></i> 工作地点：{{ item.wlocation }}</p>\r\n              <p><i class=\"el-icon-user\"></i> 招聘人数：{{ item.rnumber }}</p>\r\n              <p>\r\n                <i class=\"el-icon-money\"></i> 薪资待遇：<span class=\"salary\">{{\r\n                  item.streatment\r\n                }}</span>\r\n              </p>\r\n              <p><i class=\"el-icon-time\"></i> 发布时间：{{ item.ptime }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon-box__btn\">\r\n            <a :href=\"'positionsView?id=' + item.pid\">\r\n              查看详情<span><i class=\"fa fa-chevron-right\"></i></span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\n      background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" class=\"pagination\">\r\n    </el-pagination>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from '../../../utils/http';\r\nexport default {\r\n  name: 'PositionsList',\r\n  data() {\r\n    return {\r\n      page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 8, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n      },\r\n      polist: [],\r\n      catid: '',\r\n      keyword: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.catid = this.$route.query.catid == null ? '' : this.$route.query.catid;\r\n    this.keyword = this.$route.query.keyword == null ? '' : this.$route.query.keyword;\r\n    this.getDatas();\r\n  },\r\n  methods: {\r\n    // 分页\r\n    handleCurrentChange(val) {\r\n      this.page.currentPage = val;\r\n      this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {\r\n        pflag: '开放', // 只显示开放状态的职位\r\n        pflag2: '审核通过', // 只显示审核通过的职位\r\n        catid: this.catid,\r\n        condition: ' and pname like \"%' + this.keyword + '%\" ',\r\n      };\r\n      let url =\r\n        base +\r\n        '/positions/list?currentPage=' +\r\n        this.page.currentPage +\r\n        '&pageSize=' +\r\n        this.page.pageSize;\r\n      request.post(url, para).then((res) => {\r\n        this.polist = res.resdata;\r\n        this.page.totalCount = res.count;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.positions-list {\r\n  padding: 20px;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -15px;\r\n}\r\n\r\n.col-lg-6 {\r\n  padding: 15px;\r\n  width: 50%;\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .col-lg-6 {\r\n    width: 50%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .col-lg-6 {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.job-info {\r\n  text-align: left;\r\n  padding: 10px 0;\r\n}\r\n\r\n.job-info p {\r\n  margin: 5px 0;\r\n  color: #666;\r\n}\r\n\r\n.job-info i {\r\n  margin-right: 5px;\r\n  color: #3bc0c3;\r\n}\r\n\r\n.icon-box {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  height: 100%;\r\n}\r\n\r\n.icon-box:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.heading-title {\r\n  color: #333;\r\n  font-size: 18px;\r\n  margin-bottom: 15px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.icon-box__btn {\r\n  margin-top: 15px;\r\n  text-align: right;\r\n}\r\n\r\n.icon-box__btn a {\r\n  color: #3bc0c3;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.icon-box__btn a:hover {\r\n  color: #2a8f91;\r\n}\r\n\r\n.salary {\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n}\r\n\r\n.pagination {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAK;;EAEPA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EAEtBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAU;;EAIwBA,KAAK,EAAC;AAAQ;;EAOxDA,KAAK,EAAC;AAAe;;;;uBAnBlCC,mBAAA,CA+BM,OA/BNC,UA+BM,GA9BJC,mBAAA,CAyBM,OAzBNC,UAyBM,I,kBAxBJH,mBAAA,CAuBMI,SAAA,QAAAC,WAAA,CAvB0EC,KAAA,CAAAC,MAAM,EAAdC,IAAI;yBAA5ER,mBAAA,CAuBM;MAvBDD,KAAK,EAAC,8BAA8B;MAAC,gBAAc,EAAC,OAAO;MAAyBU,GAAG,EAAED,IAAI,CAACE;QACjGR,mBAAA,CAqBM,OArBNS,UAqBM,GApBJT,mBAAA,CAEM,OAFNU,UAEM,GADJV,mBAAA,CAA+C,MAA/CW,UAA+C,EAAAC,gBAAA,CAAlBN,IAAI,CAACO,KAAK,iB,GAEzCb,mBAAA,CAWM,OAXNc,UAWM,GAVJd,mBAAA,CASM,OATNe,UASM,GARJf,mBAAA,CAAiE,Y,0BAA9DA,mBAAA,CAAgC;MAA7BH,KAAK,EAAC;IAAkB,6B,iBAAK,QAAM,GAAAe,gBAAA,CAAGN,IAAI,CAACU,SAAS,iB,GAC1DhB,mBAAA,CAA2D,Y,0BAAxDA,mBAAA,CAA4B;MAAzBH,KAAK,EAAC;IAAc,6B,iBAAK,QAAM,GAAAe,gBAAA,CAAGN,IAAI,CAACW,OAAO,iB,GACpDjB,mBAAA,CAII,Y,0BAHFA,mBAAA,CAA6B;MAA1BH,KAAK,EAAC;IAAe,6B,2CAAK,QAAM,IAAAG,mBAAA,CAE1B,QAF0BkB,UAE1B,EAAAN,gBAAA,CADPN,IAAI,CAACa,UAAU,iB,GAGnBnB,mBAAA,CAAyD,Y,0BAAtDA,mBAAA,CAA4B;MAAzBH,KAAK,EAAC;IAAc,6B,iBAAK,QAAM,GAAAe,gBAAA,CAAGN,IAAI,CAACc,KAAK,iB,OAGtDpB,mBAAA,CAIM,OAJNqB,UAIM,GAHJrB,mBAAA,CAEI;MAFAsB,IAAI,wBAAwBhB,IAAI,CAACE;wDAAK,OACpC,GAAAR,mBAAA,CAAgD,eAA1CA,mBAAA,CAAmC;MAAhCH,KAAK,EAAC;IAAqB,G;oCAOlD0B,YAAA,CAEgBC,wBAAA;IAFAC,eAAc,EAAEC,QAAA,CAAAC,mBAAmB;IAAG,cAAY,EAAEvB,KAAA,CAAAwB,IAAI,CAACC,WAAW;IAAG,WAAS,EAAEzB,KAAA,CAAAwB,IAAI,CAACE,QAAQ;IAC7GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAE7B,KAAA,CAAAwB,IAAI,CAACM,UAAU;IAAErC,KAAK,EAAC", "ignoreList": []}]}