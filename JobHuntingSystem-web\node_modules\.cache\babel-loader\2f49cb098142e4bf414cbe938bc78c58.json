{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Login.vue?vue&type=template&id=26084dc2&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Login.vue", "mtime": 1741615327525}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_form", "model", "$data", "loginModel", "_component_el_form_item", "_component_el_input", "username", "$event", "placeholder", "size", "password", "type", "_component_el_radio", "role", "label", "_cache", "_component_el_button", "loading", "onClick", "$options", "login", "_hoisted_4", "_component_el_link", "toreg", "href", "_createCommentVNode", "_component_el_dialog", "title", "formVisible", "width", "formData", "ref", "rules", "_ctx", "align", "prop", "clname", "style", "password2", "comname", "scale", "nature", "contact", "address", "logo", "readonly", "showUpload", "rows", "introduction", "reg", "btnLoading", "uploadVisible", "onClose", "closeDialog", "_component_el_upload", "action", "drag", "limit", "handlePreview", "handleRemove", "fileList", "handleExceed", "name", "fileListChange", "id", "_hoisted_5", "hideUpload", "handleConfirm"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-form\">\n        <div class=\"login-header\">\n          <h2>求职系统</h2>\n          <p class=\"subtitle\">Job Hunting System</p>\n        </div>\n\n        <el-form :model=\"loginModel\" class=\"login-form-body\">\n          <el-form-item>\n            <el-input v-model=\"loginModel.username\" prefix-icon=\"User\" placeholder=\"请输入账号\" size=\"large\" />\n          </el-form-item>\n\n          <el-form-item>\n            <el-input v-model=\"loginModel.password\" prefix-icon=\"Lock\" type=\"password\" placeholder=\"请输入密码\" size=\"large\"\n              show-password />\n          </el-form-item>\n\n          <el-form-item class=\"role-select\">\n            <el-radio v-model=\"loginModel.role\" label=\"管理员\">管理员</el-radio>\n            <el-radio v-model=\"loginModel.role\" label=\"企业\">企业用户</el-radio>\n          </el-form-item>\n\n          <el-form-item>\n            <el-button type=\"primary\" class=\"login-button\" :loading=\"loading\" @click=\"login\" size=\"large\">\n              登 录\n            </el-button>\n          </el-form-item>\n        </el-form>\n\n        <div class=\"login-footer\">\n          <el-link type=\"primary\" @click=\"toreg\" href=\"#\">企业用户注册</el-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- 保留原有的注册对话框代码 -->\n    <el-dialog title=\"企业注册\" v-model=\"formVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n      <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\n        <el-form-item label=\"企业账号\" prop=\"clname\">\n          <el-input v-model=\"formData.clname\" placeholder=\"企业账号\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"登录密码\" prop=\"password\">\n          <el-input type=\"password\" v-model=\"formData.password\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"确认密码\" prop=\"password2\">\n          <el-input type=\"password\" v-model=\"formData.password2\" placeholder=\"确认密码\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"企业名称\" prop=\"comname\">\n          <el-input v-model=\"formData.comname\" placeholder=\"企业名称\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"企业规模\" prop=\"scale\">\n          <el-input v-model=\"formData.scale\" placeholder=\"企业规模\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"企业性质\" prop=\"nature\">\n          <el-input v-model=\"formData.nature\" placeholder=\"企业性质\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"联系方式\" prop=\"contact\">\n          <el-input v-model=\"formData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"联系地址\" prop=\"address\">\n          <el-input v-model=\"formData.address\" placeholder=\"联系地址\" style=\"width:50%;\"></el-input>\n        </el-form-item>\n        <el-form-item prop=\"logo\" label=\"企业logo\" min-width=\"20%\">\n          <el-input v-model=\"formData.logo\" placeholder=\"企业logo\" readonly=\"true\" style=\"width:50%;\"></el-input>\n          <el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\n        </el-form-item>\n        <el-form-item label=\"企业介绍\" prop=\"introduction\">\n          <el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.introduction\" placeholder=\"企业介绍\"\n            size=\"small\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"reg\" :loading=\"btnLoading\">注 册</el-button>\n        </el-form-item>\n      </el-form>\n    </el-dialog>\n    <el-dialog v-model=\"uploadVisible\" title=\"附件上传\" custom-class=\"el-dialog-widthSmall\" @close=\"closeDialog\">\n      <div>\n        <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n      </div>\n      <el-upload action=\"http://localhost:8088/gouwu/api/common/uploadFile\" style=\"\n        margin: auto;\n        margin-top: 10px;\n        border: 1px solid #dcdfe6;\n        border-radius: 4px;\n      \" drag :limit=\"1\" :on-preview=\"handlePreview\" :on-remove=\"handleRemove\" :file-list=\"fileList\"\n        :on-exceed=\"handleExceed\" :auto-upload=\"false\" name=\"file\" :on-change=\"fileListChange\">\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">\n          将数据文件拖到此处，或<em>点击上传</em>\n        </div>\n        <div class=\"el-upload__tip\">\n          <div style=\"display: inline; color: #d70000; font-size: 14px\" class=\"uploadFileWarning\"\n            id=\"uploadFileWarning\"></div>\n        </div>\n      </el-upload>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"hideUpload\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import request, { base } from \"../../utils/http\";\n  export default {\n    name: \"Login\",\n    data() {\n      return {\n        year: new Date().getFullYear(),\n        loginModel: {\n          username: \"\",\n          password: \"\",\n          role: \"管理员\"\n        },\n        loginModel2: {},\n        add: true, //是否是添加\n        formVisible: false,\n        formData: {},\n\n        addrules: {\n          clname: [{ required: true, message: '请输入企业账号', trigger: 'blur' },],\n          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n          password2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n          comname: [{ required: true, message: '请输入企业名称', trigger: 'blur' },],\n          scale: [{ required: true, message: '请输入企业规模', trigger: 'blur' },],\n          nature: [{ required: true, message: '请输入企业性质', trigger: 'blur' },],\n          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },],\n          logo: [{ required: true, message: '请上传企业logo', trigger: 'blur' }],\n          introduction: [{ required: true, message: '请输入企业介绍', trigger: 'blur' },],\n        },\n\n        btnLoading: false, //按钮是否在加载中\n        uploadVisible: false, //上传弹出框\n        loading: false,\n      };\n    },\n    mounted() { },\n    created() {\n\n    },\n    methods: {\n      login() {\n        if (this.loginModel.role === \"管理员\") {\n          let url = base + \"/admin/login\";\n          this.loginModel2.aname = this.loginModel.username;\n          this.loginModel2.password = this.loginModel.password;\n          request.post(url, this.loginModel2).then((res) => {\n            this.loading = false;\n            if (res.code == 200) {\n              console.log(JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"userLname\", res.resdata.aname);\n              sessionStorage.setItem(\"role\", \"管理员\");\n              this.$router.push(\"/main\");\n            } else {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n              });\n            }\n          });\n        } else {\n          // 企业登录逻辑\n          let url = base + \"/company/login\";\n          this.loading = true;\n          this.loginModel2.clname = this.loginModel.username;\n          this.loginModel2.password = this.loginModel.password;\n          request.post(url, this.loginModel2).then((res) => {\n            this.loading = false;\n            if (res.code == 200) {\n              console.log(JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n              sessionStorage.setItem(\"userLname\", res.resdata.clname);\n              sessionStorage.setItem(\"role\", \"企业\");\n              this.$router.push(\"/main\");\n            } else {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n              });\n            }\n          });\n        }\n      },\n\n      toreg() {\n        this.formVisible = true;\n        this.add = true;\n        this.isClear = true;\n        this.rules = this.addrules;\n        this.$nextTick(() => {\n          this.$refs[\"formDataRef\"].resetFields();\n        });\n      },\n\n      //注册\n      reg() {\n        //表单验证\n        this.$refs[\"formDataRef\"].validate((valid) => {\n\n          if (valid) {\n            let url = base + \"/company/add\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口             \n              if (res.code == 200) {\n                this.$message({\n                  message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.formVisible = false; //关闭表单\n                this.btnLoading = false; //按钮加载状态\n                this.$refs[\"formDataRef\"].resetFields(); //重置表单\n                this.$refs[\"formDataRef\"].clearValidate();\n              }\n              else if (res.code == 201) {\n                this.$message({\n                  message: res.msg,\n                  type: \"error\",\n                  offset: 320,\n                });\n              }\n              else {\n                this.$message({\n                  message: \"服务器错误\",\n                  type: \"error\",\n                  offset: 320,\n                });\n              }\n            });\n          }\n        });\n      },\n      //显示上传框\n      showUpload() {\n        this.uploadVisible = true;\n      },\n\n      //隐藏上传框\n      hideUpload() {\n        this.uploadVisible = false;\n      },\n      //上传\n      handleRemove(file, fileList) {\n        this.fileList = fileList;\n      },\n      handlePreview(file) {\n        console.log(file);\n      },\n      handleExceed(files, fileList) {\n        this.$message({\n          duration: 1000,\n          message: \"只能上传一个文件\",\n          type: \"error\",\n          offset: 320,\n        });\n      },\n      // 判断上传文件后缀\n      fileListChange(file, fileList) {\n        let extendFileName = \"png,jpg\";\n        let extendFileNames = extendFileName.split(\",\");\n        let regExpRules = [];\n        for (let i = 0; i < extendFileNames.length; i++) {\n          regExpRules.push(\n            new RegExp(\"(.*).(\" + extendFileNames[i] + \")$\", \"gim\")\n          );\n        }\n        let fileNames = [];\n        let files = [];\n        let that = this;\n        fileList.forEach(function (key, val) {\n          let ret = false;\n          for (let i = 0; i < regExpRules.length; i++) {\n            ret = ret || regExpRules[i].test(key[\"name\"]);\n          }\n          if (!ret) {\n            console.log(key[\"name\"] + \":\" + ret);\n            that.$message({\n              duration: 1000,\n              message: \"上传的文件后缀必须为\" + extendFileName + \"格式！\",\n              type: \"error\",\n              offset: 320,\n            });\n            return false;\n          }\n          if (fileNames.indexOf(key[\"name\"]) != -1) {\n            that.$message({\n              duration: 1000,\n              message: \"上传的文件重复！\",\n              type: \"error\",\n              offset: 320,\n            });\n            return false;\n          }\n          //只能上传一个文件，用最后上传的覆盖\n          if (!that.multiFiles) {\n            files = [];\n            fileNames = [];\n          }\n          files.push(key);\n          fileNames.push(key[\"name\"]);\n          if (fileNames !== \"\") {\n            // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n          }\n          //$(\".uploadFileWarning\").text(\"\");\n        });\n        this.files = fileNames;\n        this.fileList = files;\n      },\n      /**\n       * 确认按钮\n       */\n      handleConfirm() {\n        let filePath = this.fileList;\n        if (filePath.length === 0) {\n          this.$message({\n            duration: 1000,\n            message: \"请选择文件！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        let formData = new FormData();\n        this.fileList.forEach((file) => {\n          formData.append(\"file\", file.raw, file.raw.name);\n        });\n        let url = base + \"/common/uploadFile\";\n        console.log(\"url=\" + url);\n        request.post(url, formData).then((res) => {\n          console.log(res);\n          let furl = res.resdata.filePath;\n          this.formData.logo = furl;  // 上传文件的路径\n          this.hideUpload();\n          console.log(res);\n        });\n      },\n    },\n  };\n</script>\n\n<style scoped>\n  .login-container {\n    min-height: 100vh;\n    background: linear-gradient(135deg, #1e4db7 0%, #1e4db7 100%);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 0;\n    padding: 0;\n  }\n\n  .login-box {\n    width: 420px;\n    padding: 35px;\n    background: rgba(255, 255, 255, 0.98);\n    border-radius: 12px;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  }\n\n  .login-header {\n    text-align: center;\n    margin-bottom: 35px;\n  }\n\n  .login-header h2 {\n    color: #1e4db7;\n    font-size: 26px;\n    margin: 0;\n    font-weight: 600;\n  }\n\n  .subtitle {\n    color: #666;\n    font-size: 14px;\n    margin-top: 8px;\n  }\n\n  .login-form-body {\n    margin-top: 20px;\n  }\n\n  .login-form-body :deep(.el-form-item) {\n    margin-bottom: 25px;\n  }\n\n  .role-select {\n    text-align: center;\n    margin: 5px 0 20px;\n  }\n\n  .role-select :deep(.el-radio) {\n    margin-right: 30px;\n  }\n\n  .role-select :deep(.el-radio__label) {\n    color: #666;\n  }\n\n  .role-select :deep(.el-radio__input.is-checked + .el-radio__label) {\n    color: #1e4db7;\n  }\n\n  .login-button {\n    width: 100%;\n    height: 44px;\n    font-size: 16px;\n    background: #1e4db7;\n    border-color: #1e4db7;\n  }\n\n  .login-button:hover {\n    background: #2857c1;\n    border-color: #2857c1;\n  }\n\n  .login-footer {\n    margin-top: 20px;\n    text-align: center;\n  }\n\n  :deep(.el-input__wrapper) {\n    box-shadow: 0 0 0 1px #dcdfe6 inset;\n    background: #f5f7fa;\n  }\n\n  :deep(.el-input__wrapper:hover) {\n    box-shadow: 0 0 0 1px #1e4db7 inset;\n  }\n\n  :deep(.el-input__wrapper.is-focus) {\n    box-shadow: 0 0 0 1px #1e4db7 inset !important;\n  }\n\n  :deep(.el-radio__inner) {\n    border-color: #dcdfe6;\n  }\n\n  :deep(.el-radio__input.is-checked .el-radio__inner) {\n    background: #1e4db7;\n    border-color: #1e4db7;\n  }\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EA4BhBA,KAAK,EAAC;AAAc;;EAkErBA,KAAK,EAAC;AAAe;;;;;;;;;;uBAhG/BC,mBAAA,CAqGM,OArGNC,UAqGM,GApGJC,mBAAA,CAiCM,OAjCNC,UAiCM,GAhCJD,mBAAA,CA+BM,OA/BNE,UA+BM,G,4BA9BJF,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAA0C;IAAvCH,KAAK,EAAC;EAAU,GAAC,oBAAkB,E,sBAGxCM,YAAA,CAoBUC,kBAAA;IApBAC,KAAK,EAAEC,KAAA,CAAAC,UAAU;IAAEV,KAAK,EAAC;;sBACjC,MAEe,CAFfM,YAAA,CAEeK,uBAAA;wBADb,MAA8F,CAA9FL,YAAA,CAA8FM,mBAAA;oBAA3EH,KAAA,CAAAC,UAAU,CAACG,QAAQ;mEAAnBJ,KAAA,CAAAC,UAAU,CAACG,QAAQ,GAAAC,MAAA;QAAE,aAAW,EAAC,MAAM;QAACC,WAAW,EAAC,OAAO;QAACC,IAAI,EAAC;;;QAGtFV,YAAA,CAGeK,uBAAA;wBAFb,MACkB,CADlBL,YAAA,CACkBM,mBAAA;oBADCH,KAAA,CAAAC,UAAU,CAACO,QAAQ;mEAAnBR,KAAA,CAAAC,UAAU,CAACO,QAAQ,GAAAH,MAAA;QAAE,aAAW,EAAC,MAAM;QAACI,IAAI,EAAC,UAAU;QAACH,WAAW,EAAC,OAAO;QAACC,IAAI,EAAC,OAAO;QACzG,eAAa,EAAb;;;QAGJV,YAAA,CAGeK,uBAAA;MAHDX,KAAK,EAAC;IAAa;wBAC/B,MAA8D,CAA9DM,YAAA,CAA8Da,mBAAA;oBAA3CV,KAAA,CAAAC,UAAU,CAACU,IAAI;mEAAfX,KAAA,CAAAC,UAAU,CAACU,IAAI,GAAAN,MAAA;QAAEO,KAAK,EAAC;;0BAAM,MAAGC,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;yCACnDhB,YAAA,CAA8Da,mBAAA;oBAA3CV,KAAA,CAAAC,UAAU,CAACU,IAAI;mEAAfX,KAAA,CAAAC,UAAU,CAACU,IAAI,GAAAN,MAAA;QAAEO,KAAK,EAAC;;0BAAK,MAAIC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;QAGrDhB,YAAA,CAIeK,uBAAA;wBAHb,MAEY,CAFZL,YAAA,CAEYiB,oBAAA;QAFDL,IAAI,EAAC,SAAS;QAAClB,KAAK,EAAC,cAAc;QAAEwB,OAAO,EAAEf,KAAA,CAAAe,OAAO;QAAGC,OAAK,EAAEC,QAAA,CAAAC,KAAK;QAAEX,IAAI,EAAC;;0BAAQ,MAE9FM,MAAA,SAAAA,MAAA,Q,iBAF8F,OAE9F,E;;;;;;gCAIJnB,mBAAA,CAEM,OAFNyB,UAEM,GADJtB,YAAA,CAAgEuB,kBAAA;IAAvDX,IAAI,EAAC,SAAS;IAAEO,OAAK,EAAEC,QAAA,CAAAI,KAAK;IAAEC,IAAI,EAAC;;sBAAI,MAAMT,MAAA,SAAAA,MAAA,Q,iBAAN,QAAM,E;;wCAK5DU,mBAAA,kBAAqB,EACrB1B,YAAA,CAsCY2B,oBAAA;IAtCDC,KAAK,EAAC,MAAM;gBAAUzB,KAAA,CAAA0B,WAAW;iEAAX1B,KAAA,CAAA0B,WAAW,GAAArB,MAAA;IAAEsB,KAAK,EAAC,KAAK;IAAE,sBAAoB,EAAE;;sBAC/E,MAoCU,CApCV9B,YAAA,CAoCUC,kBAAA;MApCAC,KAAK,EAAEC,KAAA,CAAA4B,QAAQ;MAAE,aAAW,EAAC,KAAK;MAACC,GAAG,EAAC,aAAa;MAAEC,KAAK,EAAEC,IAAA,CAAAD,KAAK;MAAEE,KAAK,EAAC;;wBAClF,MAEe,CAFfnC,YAAA,CAEeK,uBAAA;QAFDU,KAAK,EAAC,MAAM;QAACqB,IAAI,EAAC;;0BAC9B,MAAqF,CAArFpC,YAAA,CAAqFM,mBAAA;sBAAlEH,KAAA,CAAA4B,QAAQ,CAACM,MAAM;qEAAflC,KAAA,CAAA4B,QAAQ,CAACM,MAAM,GAAA7B,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAC6B,KAAkB,EAAlB;YAAA;UAAA;;;UAEzDtC,YAAA,CAEeK,uBAAA;QAFDU,KAAK,EAAC,MAAM;QAACqB,IAAI,EAAC;;0BAC9B,MAAuG,CAAvGpC,YAAA,CAAuGM,mBAAA;UAA7FM,IAAI,EAAC,UAAU;sBAAUT,KAAA,CAAA4B,QAAQ,CAACpB,QAAQ;qEAAjBR,KAAA,CAAA4B,QAAQ,CAACpB,QAAQ,GAAAH,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAC6B,KAAkB,EAAlB;YAAA;UAAA;;;UAE3EtC,YAAA,CAEeK,uBAAA;QAFDU,KAAK,EAAC,MAAM;QAACqB,IAAI,EAAC;;0BAC9B,MAAwG,CAAxGpC,YAAA,CAAwGM,mBAAA;UAA9FM,IAAI,EAAC,UAAU;sBAAUT,KAAA,CAAA4B,QAAQ,CAACQ,SAAS;qEAAlBpC,KAAA,CAAA4B,QAAQ,CAACQ,SAAS,GAAA/B,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAC6B,KAAkB,EAAlB;YAAA;UAAA;;;UAE5EtC,YAAA,CAEeK,uBAAA;QAFDU,KAAK,EAAC,MAAM;QAACqB,IAAI,EAAC;;0BAC9B,MAAsF,CAAtFpC,YAAA,CAAsFM,mBAAA;sBAAnEH,KAAA,CAAA4B,QAAQ,CAACS,OAAO;qEAAhBrC,KAAA,CAAA4B,QAAQ,CAACS,OAAO,GAAAhC,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAC6B,KAAkB,EAAlB;YAAA;UAAA;;;UAE1DtC,YAAA,CAEeK,uBAAA;QAFDU,KAAK,EAAC,MAAM;QAACqB,IAAI,EAAC;;0BAC9B,MAAoF,CAApFpC,YAAA,CAAoFM,mBAAA;sBAAjEH,KAAA,CAAA4B,QAAQ,CAACU,KAAK;qEAAdtC,KAAA,CAAA4B,QAAQ,CAACU,KAAK,GAAAjC,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAC6B,KAAkB,EAAlB;YAAA;UAAA;;;UAExDtC,YAAA,CAEeK,uBAAA;QAFDU,KAAK,EAAC,MAAM;QAACqB,IAAI,EAAC;;0BAC9B,MAAqF,CAArFpC,YAAA,CAAqFM,mBAAA;sBAAlEH,KAAA,CAAA4B,QAAQ,CAACW,MAAM;qEAAfvC,KAAA,CAAA4B,QAAQ,CAACW,MAAM,GAAAlC,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAC6B,KAAkB,EAAlB;YAAA;UAAA;;;UAEzDtC,YAAA,CAEeK,uBAAA;QAFDU,KAAK,EAAC,MAAM;QAACqB,IAAI,EAAC;;0BAC9B,MAAsF,CAAtFpC,YAAA,CAAsFM,mBAAA;sBAAnEH,KAAA,CAAA4B,QAAQ,CAACY,OAAO;uEAAhBxC,KAAA,CAAA4B,QAAQ,CAACY,OAAO,GAAAnC,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAC6B,KAAkB,EAAlB;YAAA;UAAA;;;UAE1DtC,YAAA,CAEeK,uBAAA;QAFDU,KAAK,EAAC,MAAM;QAACqB,IAAI,EAAC;;0BAC9B,MAAsF,CAAtFpC,YAAA,CAAsFM,mBAAA;sBAAnEH,KAAA,CAAA4B,QAAQ,CAACa,OAAO;uEAAhBzC,KAAA,CAAA4B,QAAQ,CAACa,OAAO,GAAApC,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAC6B,KAAkB,EAAlB;YAAA;UAAA;;;UAE1DtC,YAAA,CAGeK,uBAAA;QAHD+B,IAAI,EAAC,MAAM;QAACrB,KAAK,EAAC,QAAQ;QAAC,WAAS,EAAC;;0BACjD,MAAqG,CAArGf,YAAA,CAAqGM,mBAAA;sBAAlFH,KAAA,CAAA4B,QAAQ,CAACc,IAAI;uEAAb1C,KAAA,CAAA4B,QAAQ,CAACc,IAAI,GAAArC,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAACqC,QAAQ,EAAC,MAAM;UAACR,KAAkB,EAAlB;YAAA;UAAA;iDACvEtC,YAAA,CAAyEiB,oBAAA;UAA9DL,IAAI,EAAC,SAAS;UAACF,IAAI,EAAC,OAAO;UAAES,OAAK,EAAEC,QAAA,CAAA2B;;4BAAY,MAAE/B,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;UAE/DhB,YAAA,CAGeK,uBAAA;QAHDU,KAAK,EAAC,MAAM;QAACqB,IAAI,EAAC;;0BAC9B,MAC0B,CAD1BpC,YAAA,CAC0BM,mBAAA;UADhBM,IAAI,EAAC,UAAU;UAAEoC,IAAI,EAAE,CAAC;sBAAW7C,KAAA,CAAA4B,QAAQ,CAACkB,YAAY;uEAArB9C,KAAA,CAAA4B,QAAQ,CAACkB,YAAY,GAAAzC,MAAA;UAAEC,WAAW,EAAC,MAAM;UACpFC,IAAI,EAAC;;;UAETV,YAAA,CAEeK,uBAAA;0BADb,MAA4E,CAA5EL,YAAA,CAA4EiB,oBAAA;UAAjEL,IAAI,EAAC,SAAS;UAAEO,OAAK,EAAEC,QAAA,CAAA8B,GAAG;UAAGhC,OAAO,EAAEf,KAAA,CAAAgD;;4BAAY,MAAGnC,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;;;;;;qCAItEhB,YAAA,CAwBY2B,oBAAA;gBAxBQxB,KAAA,CAAAiD,aAAa;iEAAbjD,KAAA,CAAAiD,aAAa,GAAA5C,MAAA;IAAEoB,KAAK,EAAC,MAAM;IAAC,cAAY,EAAC,sBAAsB;IAAEyB,OAAK,EAAEnB,IAAA,CAAAoB;;sBAC1F,MAEM,C,4BAFNzD,mBAAA,CAEM,cADJA,mBAAA,CAAkC,WAA/B,wBAA2B,E,sBAEhCG,YAAA,CAeYuD,oBAAA;MAfDC,MAAM,EAAC,mDAAmD;MAAClB,KAKrE,EALqE;QAAA;QAAA;QAAA;QAAA;MAAA,CAKrE;MAACmB,IAAI,EAAJ,EAAI;MAAEC,KAAK,EAAE,CAAC;MAAG,YAAU,EAAEtC,QAAA,CAAAuC,aAAa;MAAG,WAAS,EAAEvC,QAAA,CAAAwC,YAAY;MAAG,WAAS,EAAE1B,IAAA,CAAA2B,QAAQ;MACzF,WAAS,EAAEzC,QAAA,CAAA0C,YAAY;MAAG,aAAW,EAAE,KAAK;MAAEC,IAAI,EAAC,MAAM;MAAE,WAAS,EAAE3C,QAAA,CAAA4C;;wBACvE,MAA8BhD,MAAA,SAAAA,MAAA,QAA9BnB,mBAAA,CAA8B;QAA3BH,KAAK,EAAC;MAAgB,4BACzBG,mBAAA,CAEM;QAFDH,KAAK,EAAC;MAAiB,I,iBAAC,cAChB,GAAAG,mBAAA,CAAa,YAAT,MAAI,E,qBAErBA,mBAAA,CAGM;QAHDH,KAAK,EAAC;MAAgB,IACzBG,mBAAA,CAC+B;QAD1ByC,KAAwD,EAAxD;UAAA;UAAA;UAAA;QAAA,CAAwD;QAAC5C,KAAK,EAAC,mBAAmB;QACrFuE,EAAE,EAAC;;;2FAGTpE,mBAAA,CAGO,QAHPqE,UAGO,GAFLlE,YAAA,CAA8CiB,oBAAA;MAAlCE,OAAK,EAAEC,QAAA,CAAA+C;IAAU;wBAAE,MAAGnD,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;oCAClChB,YAAA,CAAgEiB,oBAAA;MAArDL,IAAI,EAAC,SAAS;MAAEO,OAAK,EAAEC,QAAA,CAAAgD;;wBAAe,MAAGpD,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E", "ignoreList": []}]}