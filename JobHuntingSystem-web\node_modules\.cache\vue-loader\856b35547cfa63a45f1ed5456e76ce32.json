{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue", "mtime": 1741615257361}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\Left.vue"], "names": [], "mappings": ";EAgCE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC;IACH,CAAC;EACH,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/components/Left.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"col-xxl-4 col-xl-4 col-lg-4\">\r\n    <div class=\"animated-list\">\r\n      <h2 class=\"section-title\">职位分类</h2>\r\n      <ul class=\"fen-list\">\r\n        <li v-for=\"(item, index) in list1\" :key=\"index\">\r\n          <a :href=\"'positionsList?catid=' + item.catid\">{{ item.catname }}</a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"animated-list\">\r\n      <h2 class=\"section-title\">最新职位</h2>\r\n      <ul class=\"news-list\">\r\n        <li class=\"fade-in\" v-for=\"(item, index) in list2\" :key=\"index\">\r\n          <a :href=\"'positionsView?id=' + item.pid\"><i class=\"fas fa-star\"></i>{{ item.pname }}</a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"animated-gallery\">\r\n      <h2 class=\"section-title\">最新企业</h2>\r\n      <div class=\"image-grid\">\r\n        <div class=\"image-item zoom-in\" v-for=\"(item, index) in list3\" :key=\"index\">\r\n          <a :href=\"'companyView?id=' + item.cid\">\r\n            <img :src=\"'http://localhost:8088/JobHuntingSystem/' + item.logo\" style=\"width: 165px; height: 123px\" />\r\n            <p class=\"image-caption\">{{ item.comname }}</p>\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../utils/http';\r\n  export default {\r\n    name: 'Left',\r\n    data() {\r\n      return {\r\n        list1: '',\r\n        list2: '',\r\n        list3: '',\r\n        isLoggedIn: false,\r\n      };\r\n    },\r\n    created() {\r\n      this.checkLoginStatus();\r\n      this.getlist1();\r\n      this.getlist2();\r\n      this.getlist3();\r\n    },\r\n    methods: {\r\n      // 检查登录状态\r\n      checkLoginStatus() {\r\n        const lname = sessionStorage.getItem('lname');\r\n        this.isLoggedIn = !!lname;\r\n      },\r\n\r\n      // 获取职位分类\r\n      getlist1() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + '/jobcategories/list?currentPage=1&pageSize=100';\r\n        request.post(url, para).then((res) => {\r\n          this.list1 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 获取最新职位\r\n      getlist2() {\r\n        let para = {\r\n          pflag: '开放', // 只获取开放状态的职位\r\n          pflag2: '审核通过',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/positions/list?currentPage=1&pageSize=10';\r\n        request.post(url, para).then((res) => {\r\n          this.list2 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n\r\n      // 获取最新企业\r\n      getlist3() {\r\n        let para = {\r\n          cflag: '审核通过',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + '/company/list?currentPage=1&pageSize=6';\r\n        request.post(url, para).then((res) => {\r\n          this.list3 = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style>\r\n  .fen-list {\r\n    list-style-type: none;\r\n    padding: 0;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .fen-list li {\r\n    width: calc(50% - 10px);\r\n    margin-bottom: 15px;\r\n    opacity: 0;\r\n    animation: fadeIn 0.5s ease-in forwards;\r\n    font-size: 16px;\r\n    color: #555;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .fen-list li:hover {\r\n    transform: translateY(-5px);\r\n  }\r\n\r\n  .fen-list li a {\r\n    color: #333;\r\n    text-decoration: none;\r\n    display: block;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 100%;\r\n    padding: 10px;\r\n    background-color: #f0f0f0;\r\n    border-radius: 5px;\r\n    transition: background-color 0.3s ease;\r\n  }\r\n\r\n  .fen-list li a:hover {\r\n    color: #3498db;\r\n    background-color: #e0e0e0;\r\n  }\r\n\r\n  .fen-list li i {\r\n    margin-right: 10px;\r\n    color: #3498db;\r\n  }\r\n\r\n  @keyframes fadeIn {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(20px);\r\n    }\r\n\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .animated-list,\r\n  .animated-gallery {\r\n    margin-bottom: 40px;\r\n    background: #f9f9f9;\r\n    border-radius: 10px;\r\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 18px;\r\n    margin-bottom: 20px;\r\n    color: #333;\r\n    border-bottom: 2px solid #3498db;\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .news-list {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  .news-list li {\r\n    margin-bottom: 15px;\r\n    opacity: 0;\r\n    animation: fadeIn 0.5s ease-in forwards;\r\n    display: flex;\r\n    flex-direction: column;\r\n    font-size: 16px;\r\n    color: #555;\r\n  }\r\n\r\n  .news-list li a {\r\n    color: #333;\r\n    text-decoration: none;\r\n    display: flex;\r\n    align-items: center;\r\n    display: block;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .news-list li a:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .news-list li i {\r\n    margin-right: 10px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .news-date {\r\n    font-size: 14px;\r\n    color: #888;\r\n    margin-top: 5px;\r\n    margin-left: 24px;\r\n  }\r\n\r\n  .section-divider {\r\n    border: 0;\r\n    height: 1px;\r\n    background: #e0e0e0;\r\n    margin: 30px 0;\r\n  }\r\n\r\n  .image-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 20px;\r\n  }\r\n\r\n  .image-item {\r\n    position: relative;\r\n    overflow: hidden;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .image-item img {\r\n    width: 100%;\r\n    height: auto;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .image-item:hover img {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  .image-caption {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: #fff;\r\n    padding: 10px;\r\n    margin: 0;\r\n    font-size: 13px;\r\n    text-align: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n  }\r\n\r\n  .image-item:hover .image-caption {\r\n    opacity: 1;\r\n  }\r\n\r\n  @keyframes fadeIn {\r\n    to {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .fade-in {\r\n    animation-delay: calc(var(--item-index) * 0.1s);\r\n  }\r\n\r\n  .zoom-in {\r\n    animation: zoomIn 0.5s ease-out;\r\n  }\r\n\r\n  @keyframes zoomIn {\r\n    from {\r\n      transform: scale(0.8);\r\n      opacity: 0;\r\n    }\r\n\r\n    to {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .user-menu {\r\n    list-style-type: none;\r\n    padding: 0;\r\n    margin: 0;\r\n  }\r\n\r\n  .user-menu li {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .user-menu a {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 10px 15px;\r\n    color: #333;\r\n    text-decoration: none;\r\n    border-radius: 5px;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .user-menu a:hover {\r\n    background-color: #3bc0c3;\r\n    color: #fff;\r\n  }\r\n\r\n  .user-menu a.router-link-active {\r\n    background-color: #3bc0c3;\r\n    color: #fff;\r\n  }\r\n\r\n  .user-menu i {\r\n    margin-right: 10px;\r\n    font-size: 18px;\r\n  }\r\n</style>"]}]}