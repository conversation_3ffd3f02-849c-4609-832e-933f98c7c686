{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Main.vue?vue&type=template&id=c1f1971a", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Main.vue", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBjcmVhdGVFbGVtZW50Vk5vZGUgYXMgX2NyZWF0ZUVsZW1lbnRWTm9kZSwgd2l0aEN0eCBhcyBfd2l0aEN0eCwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IHsKICAiZGF0YS1icy10aGVtZSI6ICJsaWdodCIsCiAgImRhdGEtbGF5b3V0LW1vZGUiOiAiZmx1aWQiLAogICJkYXRhLW1lbnUtY29sb3IiOiAiZGFyayIsCiAgImRhdGEtdG9wYmFyLWNvbG9yIjogImxpZ2h0IiwKICAiZGF0YS1sYXlvdXQtcG9zaXRpb24iOiAiZml4ZWQiLAogICJkYXRhLXNpZGVuYXYtc2l6ZSI6ICJkZWZhdWx0IiwKICBjbGFzczogIm1lbnVpdGVtLWFjdGl2ZSIKfTsKY29uc3QgX2hvaXN0ZWRfMiA9IHsKICBjbGFzczogIndyYXBwZXIiCn07CmNvbnN0IF9ob2lzdGVkXzMgPSB7CiAgY2xhc3M6ICJjb250ZW50LXBhZ2UiCn07CmNvbnN0IF9ob2lzdGVkXzQgPSB7CiAgY2xhc3M6ICJjb250ZW50Igp9Owpjb25zdCBfaG9pc3RlZF81ID0gewogIGNsYXNzOiAiY29udGFpbmVyLWZsdWlkIgp9OwpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIGNvbnN0IF9jb21wb25lbnRfSGVhZGVyID0gX3Jlc29sdmVDb21wb25lbnQoIkhlYWRlciIpOwogIGNvbnN0IF9jb21wb25lbnRfTGVmdE1lbnUgPSBfcmVzb2x2ZUNvbXBvbmVudCgiTGVmdE1lbnUiKTsKICBjb25zdCBfY29tcG9uZW50X3JvdXRlcl92aWV3ID0gX3Jlc29sdmVDb21wb25lbnQoInJvdXRlci12aWV3Iik7CiAgY29uc3QgX2NvbXBvbmVudF9FbENvbmZpZ1Byb3ZpZGVyID0gX3Jlc29sdmVDb21wb25lbnQoIkVsQ29uZmlnUHJvdmlkZXIiKTsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJodG1sIiwgX2hvaXN0ZWRfMSwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X0VsQ29uZmlnUHJvdmlkZXIsIHsKICAgIGxvY2FsZTogJGRhdGEubG9jYWxlCiAgfSwgewogICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzIsIFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9IZWFkZXIpLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MZWZ0TWVudSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzMsIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF80LCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfNSwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X3JvdXRlcl92aWV3KV0pXSldKV0pXSksCiAgICBfOiAxIC8qIFNUQUJMRSAqLwogIH0sIDggLyogUFJPUFMgKi8sIFsibG9jYWxlIl0pXSk7Cn0="}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_ElConfigProvider", "locale", "$data", "_createElementVNode", "_hoisted_2", "_component_Header", "_component_LeftMenu", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_component_router_view"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\Main.vue"], "sourcesContent": ["<template>\r\n  <html data-bs-theme=\"light\" data-layout-mode=\"fluid\" data-menu-color=\"dark\" data-topbar-color=\"light\"\r\n        data-layout-position=\"fixed\" data-sidenav-size=\"default\" class=\"menuitem-active\">\r\n  <ElConfigProvider :locale=\"locale\">\r\n\r\n    <div class=\"wrapper\">\r\n      <Header />\r\n\r\n      <LeftMenu />\r\n\r\n      <div class=\"content-page\">\r\n        <div class=\"content\">\r\n          <div class=\"container-fluid\">\r\n            <router-view />\r\n          </div>\r\n        </div>  \r\n      </div>\r\n    </div>\r\n  </ElConfigProvider>\r\n\r\n  </html>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"../components/Header\";\r\nimport LeftMenu from \"../components/LeftMenu\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\nimport zhCn from \"element-plus/lib/locale/lang/zh-cn\";\r\n\r\nimport \"../assets/js/vendor.min.js\";\r\n\r\nexport default {\r\n  name: \"MainLayout\",\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n@import url(../assets/css/app.min.css);\r\n@import url(../assets/css/icons.min.css);\r\n\r\nbody{\r\n  background-image: url(../assets/img/auth-bg.jpg);\r\n  background-size: cover;\r\n}\r\n.form-control{\r\n  border: 1px solid #3bc0c3;\r\n}\r\n.form-control:focus{\r\n  border: 2px solid #c01750;\r\n  box-shadow: none;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";;EACQ,eAAa,EAAC,OAAO;EAAC,kBAAgB,EAAC,OAAO;EAAC,iBAAe,EAAC,MAAM;EAAC,mBAAiB,EAAC,OAAO;EAC/F,sBAAoB,EAAC,OAAO;EAAC,mBAAiB,EAAC,SAAS;EAACA,KAAK,EAAC;;;EAG9DA,KAAK,EAAC;AAAS;;EAKbA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAiB;;;;;;uBAXpCC,mBAAA,CAmBO,QAnBPC,UAmBO,GAjBPC,YAAA,CAemBC,2BAAA;IAfAC,MAAM,EAAEC,KAAA,CAAAD;EAAM;sBAE/B,MAYM,CAZNE,mBAAA,CAYM,OAZNC,UAYM,GAXJL,YAAA,CAAUM,iBAAA,GAEVN,YAAA,CAAYO,mBAAA,GAEZH,mBAAA,CAMM,OANNI,UAMM,GALJJ,mBAAA,CAIM,OAJNK,UAIM,GAHJL,mBAAA,CAEM,OAFNM,UAEM,GADJV,YAAA,CAAeW,sBAAA,E", "ignoreList": []}]}