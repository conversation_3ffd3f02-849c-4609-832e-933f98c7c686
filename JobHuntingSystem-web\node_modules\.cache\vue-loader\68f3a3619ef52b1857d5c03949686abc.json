{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryDetail.vue", "mtime": 1741614867102}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAnLi4vLi4vLi4vLi4vdXRpbHMvaHR0cCc7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdSZXN1bWVkZWxpdmVyeURldGFpbCcsDQogIGNvbXBvbmVudHM6IHt9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBpZDogJycsDQogICAgICBmb3JtRGF0YToge30sIC8v6KGo5Y2V5pWw5o2uDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7IC8v6I635Y+W5Y+C5pWwDQogICAgdGhpcy5nZXREYXRhcygpOw0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICAvL+iOt+WPluWIl+ihqOaVsOaNrg0KICAgIGdldERhdGFzKCkgew0KICAgICAgbGV0IHBhcmEgPSB7fTsNCiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOw0KICAgICAgbGV0IHVybCA9IGJhc2UgKyAnL3Jlc3VtZWRlbGl2ZXJ5L2dldD9pZD0nICsgdGhpcy5pZDsNCiAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHBhcmEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmZvcm1EYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyZXMucmVzZGF0YSkpOw0KICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g6L+U5ZueDQogICAgYmFjaygpIHsNCiAgICAgIC8v6L+U5Zue5LiK5LiA6aG1DQogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\admin\\resumedelivery\\ResumedeliveryDetail.vue"], "names": [], "mappings": ";AA4BA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,EAAE,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,EAAE,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;EACH,CAAC;AACH,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/admin/resumedelivery/ResumedeliveryDetail.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"page-title-box\">\r\n        <div class=\"page-title-right\">\r\n          <ol class=\"breadcrumb m-0\">\r\n            <li class=\"breadcrumb-item\"><a id=\"title2\">简历投递管理</a></li>\r\n            <li class=\"breadcrumb-item active\" id=\"title3\">简历投递详情</li>\r\n          </ol>\r\n        </div>\r\n        <h4 class=\"page-title\" id=\"title1\">简历投递详情</h4>\r\n      </div>\r\n    </div>\r\n    <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n      <el-form-item label=\"投递\"> {{ formData.delid }}</el-form-item>\r\n      <el-form-item label=\"职位\"> {{ formData.by1 }}</el-form-item>\r\n      <el-form-item label=\"账号\"> {{ formData.sno }}</el-form-item>\r\n      <el-form-item label=\"简历\"> {{ formData.by2 }}</el-form-item>\r\n      <el-form-item label=\"投递时间\"> {{ formData.submittime }}</el-form-item>\r\n      <el-form-item label=\"审核状态\"> {{ formData.auditstatus }}</el-form-item>\r\n      <el-form-item label=\"审核回复\"> {{ formData.auditreply }}</el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport request, { base } from '../../../../utils/http';\r\nexport default {\r\n  name: 'ResumedeliveryDetail',\r\n  components: {},\r\n  data() {\r\n    return {\r\n      id: '',\r\n      formData: {}, //表单数据\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id; //获取参数\r\n    this.getDatas();\r\n  },\r\n\r\n  methods: {\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + '/resumedelivery/get?id=' + this.id;\r\n      request.post(url, para).then((res) => {\r\n        this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n\r\n    // 返回\r\n    back() {\r\n      //返回上一页\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped></style>\r\n"]}]}