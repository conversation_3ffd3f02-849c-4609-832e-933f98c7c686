{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyList.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyList.vue", "mtime": 1741615257159}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gJy4uLy4uLy4uL3V0aWxzL2h0dHAnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0NvbXBhbnlMaXN0JywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcGFnZTogewogICAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICAgIC8vIOW9k+<PERSON><PERSON><PERSON>mhtQogICAgICAgIHBhZ2VTaXplOiAxMiwKICAgICAgICAvLyDmr4/pobXmmL7npLrmnaHnm67kuKrmlbAKICAgICAgICB0b3RhbENvdW50OiAwIC8vIOaAu+adoeebruaVsAogICAgICB9LAogICAgICBjb2xpc3Q6ICcnCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0RGF0YXMoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWIhumhtQogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5wYWdlLmN1cnJlbnRQYWdlID0gdmFsOwogICAgICB0aGlzLmdldERhdGFzKCk7CiAgICB9LAogICAgLy/ojrflj5bliJfooajmlbDmja4KICAgIGdldERhdGFzKCkgewogICAgICBsZXQgcGFyYSA9IHsKICAgICAgICBjZmxhZzogIuWuoeaguOmAmui/hyIKICAgICAgfTsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgIGxldCB1cmwgPSBiYXNlICsgJy9jb21wYW55L2xpc3Q/Y3VycmVudFBhZ2U9JyArIHRoaXMucGFnZS5jdXJyZW50UGFnZSArICcmcGFnZVNpemU9JyArIHRoaXMucGFnZS5wYWdlU2l6ZTsKICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMucmVzZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0aGlzLmlzUGFnZSA9IHRydWU7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuaXNQYWdlID0gZmFsc2U7CiAgICAgICAgfQogICAgICAgIHRoaXMucGFnZS50b3RhbENvdW50ID0gcmVzLmNvdW50OwogICAgICAgIHRoaXMuY29saXN0ID0gcmVzLnJlc2RhdGE7CiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["request", "base", "name", "data", "page", "currentPage", "pageSize", "totalCount", "colist", "created", "getDatas", "methods", "handleCurrentChange", "val", "para", "cflag", "listLoading", "url", "post", "then", "res", "resdata", "length", "isPage", "count"], "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\CompanyList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"divlist\">\r\n    <ul>\r\n      <li class=\"widthk4\" v-for=\"item in colist\" :key=\"item.cid\">\r\n        <a :href=\"'companyView?id=' + item.cid\"><img :src=\"'http://localhost:8088/JobHuntingSystem/' + item.logo\"\r\n            style=\"width: 154px; height: 120px\" /></a>\r\n        <span class=\"wspan\"><a :href=\"'companyView?id=' + item.cid\">{{ item.comname }}</a></span>\r\n      </li>\r\n    </ul>\r\n  </div>\r\n  <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\n    background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\n    style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n</template>\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n  export default {\r\n    name: 'CompanyList',\r\n    data() {\r\n      return {\r\n        page: {\r\n          currentPage: 1, // 当前页\r\n          pageSize: 12, // 每页显示条目个数\r\n          totalCount: 0, // 总条目数\r\n        },\r\n        colist: '',\r\n      };\r\n    },\r\n    created() {\r\n      this.getDatas();\r\n    },\r\n    methods: {\r\n      // 分页\r\n      handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n      },\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n          cflag: \"审核通过\",\r\n        };\r\n        this.listLoading = true;\r\n        let url =\r\n          base +\r\n          '/company/list?currentPage=' +\r\n          this.page.currentPage +\r\n          '&pageSize=' +\r\n          this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n          if (res.resdata.length > 0) {\r\n            this.isPage = true;\r\n          } else {\r\n            this.isPage = false;\r\n          }\r\n          this.page.totalCount = res.count;\r\n          this.colist = res.resdata;\r\n          this.listLoading = false;\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style></style>"], "mappings": "AAeE,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,MAAM,EAAE;IACV,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE;IACP;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAACT,IAAI,CAACC,WAAU,GAAIQ,GAAG;MAC3B,IAAI,CAACH,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAII,IAAG,GAAI;QACTC,KAAK,EAAE;MACT,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GACJhB,IAAG,GACH,4BAA2B,GAC3B,IAAI,CAACG,IAAI,CAACC,WAAU,GACpB,YAAW,GACX,IAAI,CAACD,IAAI,CAACE,QAAQ;MACpBN,OAAO,CAACkB,IAAI,CAACD,GAAG,EAAEH,IAAI,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAACnB,IAAI,CAACG,UAAS,GAAIa,GAAG,CAACI,KAAK;QAChC,IAAI,CAAChB,MAAK,GAAIY,GAAG,CAACC,OAAO;QACzB,IAAI,CAACL,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}]}