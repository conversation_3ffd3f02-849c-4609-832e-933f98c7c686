{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue?vue&type=template&id=9f4da21a", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue", "mtime": 1741614414000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\BbsView.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxG,CAAC,CAAC,CAAC;;<PERSON><PERSON>,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAER,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;cAE7B,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAET,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpH,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;cAE1B,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAET,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product3/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/BbsView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <table style=\"width:100%;line-height:27px; font-size:12px;border: 1px solid #C2D5E3;  margin-bottom:10px;\">\r\n    <tr>\r\n\r\n      <td> <span style=\"font-size:14px; font-weight:bold\">\r\n          {{ bbsmore.btitle }}\r\n        </span>\r\n\r\n      </td>\r\n      <td width=\"200\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\">\r\n        查看：<b style=\"color:red;\">\r\n          {{ bbsmore.btotal }}\r\n        </b> |\r\n        回复：<b style=\"color:red;\">\r\n          {{ bbsmore.by1 }}\r\n        </b> |\r\n        <a href=\"/bbs\">返回列表</a>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/JobHuntingSystem/' + bbsmore.by2\" />\r\n        </div>\r\n\r\n        <span class=\"cu-tag bg-gradual-green\" style=\" height:28px;\">楼主</span>\r\n        <span class=\"text-red\">\r\n          {{ bbsmore.sno }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"bbsmore.bdetail\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ bbsmore.addtime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \" v-for=\"item in bbsmore.bbsmore\">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/JobHuntingSystem/' + item.by1\" />\r\n        </div>\r\n\r\n        <span class=\"text-red\">\r\n          {{ item.sno }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"item.mdetail\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ item.antime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\r\n\r\n    <el-form-item label=\"回复内容\" prop=\"mdetail\">\r\n      <WangEditor ref=\"wangEditorRef\" v-model=\"formData.mdetail\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n        @change=\"editorChange\" height=\"200\"></WangEditor>\r\n    </el-form-item>\r\n    <el-form-item label=\"\">\r\n      <el-button type=\"primary\" @click=\"save\" :loading=\"btnLoading\">提 交</el-button>\r\n    </el-form-item>\r\n\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport WangEditor from \"../../components/WangEditor\";\r\n\r\nexport default {\r\n  name: \"bbsView\",\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      bbsmore: \"\",\r\n\r\n      formData: {\r\n        mdetail: \"\",\r\n      },\r\n\r\n      rules: {\r\n        mdetail: [\r\n          { required: true, message: \"请输入回复内容\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n\r\n\r\n    };\r\n  },\r\n  created() {\r\n\r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let id = this.$route.query.id;\r\n      let para = {\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/bbs/get?id=\" + id;\r\n      request.post(url, para).then((res) => {\r\n        this.bbsmore = res.resdata;\r\n      });\r\n    },\r\n\r\n    //回复\r\n    save() {\r\n      var lname = sessionStorage.getItem(\"lname\");\r\n      if (lname == null) {\r\n        this.$message({\r\n          message: \"请先登录\",\r\n          type: \"error\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs.formDataRef.validate((valid) => {\r\n        if (valid) {\r\n          this.btnLoading = true;\r\n          let para = {\r\n            bid: this.bbsmore.bid,\r\n            mdetail: this.formData.mdetail,\r\n            sno: lname,\r\n          };\r\n          let url = base + \"/bbsmore/add\";\r\n          request.post(url, para).then((res) => {\r\n            this.btnLoading = false;\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: \"回复成功\",\r\n                type: \"success\",\r\n              });\r\n              this.getDatas();\r\n              this.formData.mdetail = \"\";\r\n\r\n              //设置富文本编辑器内容\r\n              this.$nextTick(() => {\r\n                this.$refs[\"wangEditorRef\"].editor.txt.html(\"\");\r\n              });\r\n\r\n\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.mdetail = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 180px;\r\n}\r\n</style>\r\n\r\n"]}]}