{"remainingRequest": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue", "mtime": 1741617188960}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\views\\web\\ResumePreview.vue"], "names": [], "mappings": ";EA+DE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACtB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACjF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UAC5D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE;MACF,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC3F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;QACF,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC;IACH,CAAC;EACH,CAAC", "file": "I:/modify/00203JobHuntingSystem/JobHuntingSystem-web/src/views/web/ResumePreview.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"resume-preview\">\r\n    <div class=\"resume-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"avatar-section\">\r\n          <img :src=\"'http://localhost:8088/JobHuntingSystem/' + formData.spic\" class=\"avatar\" />\r\n        </div>\r\n        <div class=\"basic-info\">\r\n          <h1 class=\"name\">{{ formData.sname }}</h1>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>性别：{{ formData.gender }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-date\"></i>\r\n              <span>年龄：{{ formData.age }}岁</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-phone\"></i>\r\n              <span>电话：{{ formData.phone }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <i class=\"el-icon-school\"></i>\r\n              <span>专业：{{ professionalName }}</span>\r\n            </div>\r\n          \r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"resume-body\">\r\n      <div class=\"resume-section\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          简历信息\r\n        </h2>\r\n        <div class=\"resume-content\" v-if=\"resumeData\">\r\n          <div class=\"info-row\">\r\n            <label>教育背景：</label>\r\n            <div class=\"content\" v-html=\"resumeData.education\"></div>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <label>实习经历：</label>\r\n            <div class=\"content\" v-html=\"resumeData.parttimejob\"></div>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <label>个人介绍：</label>\r\n            <div class=\"content\" v-html=\"resumeData.introduction\"></div>\r\n          </div>\r\n        </div>\r\n        <div class=\"no-resume\" v-else>\r\n          <el-empty description=\"暂无简历信息\">\r\n            <el-button type=\"primary\" @click=\"goToAddResume\">创建简历</el-button>\r\n          </el-empty>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../../utils/http';\r\n\r\n  export default {\r\n    name: 'ResumePreview',\r\n    data() {\r\n      return {\r\n        formData: {},\r\n        resumeData: null,\r\n        professionalsList: [],\r\n        professionalName: '',\r\n      };\r\n    },\r\n    created() {\r\n      this.getUserInfo();\r\n      this.getProfessionals();\r\n      this.getResumeInfo();\r\n    },\r\n    methods: {\r\n      // 获取用户信息\r\n      getUserInfo() {\r\n        let lname = sessionStorage.getItem('lname');\r\n        let url = base + '/students/get?id=' + lname;\r\n        request.post(url).then((res) => {\r\n          if (res.code == 200) {\r\n            this.formData = res.resdata;\r\n            this.getProfessionalName();\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取专业列表\r\n      getProfessionals() {\r\n        let url = base + '/professionals/list';\r\n        request.post(url, {}, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n          if (res.code == 200) {\r\n            this.professionalsList = res.resdata;\r\n            this.getProfessionalName();\r\n          }\r\n        });\r\n      },\r\n\r\n      // 获取专业名称\r\n      getProfessionalName() {\r\n        if (this.formData.proid && this.professionalsList.length > 0) {\r\n          const professional = this.professionalsList.find((p) => p.proid === this.formData.proid);\r\n          this.professionalName = professional ? professional.proname : '';\r\n        }\r\n      },\r\n\r\n      // 获取简历信息\r\n      getResumeInfo() {\r\n        let lname = sessionStorage.getItem('lname');\r\n        let url = base + '/resume/list';\r\n        request.post(url, { sno: lname }, { params: { currentPage: 1, pageSize: 1 } }).then((res) => {\r\n          if (res.code == 200 && res.resdata.length > 0) {\r\n            this.resumeData = res.resdata[0];\r\n          }\r\n        });\r\n      },\r\n\r\n      // 跳转到创建简历页面\r\n      goToAddResume() {\r\n        this.$router.push('/Resume_Add');\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .resume-preview {\r\n    max-width: 1000px;\r\n    margin: 20px auto;\r\n    background: #fff;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    border-radius: 8px;\r\n  }\r\n\r\n  .resume-header {\r\n    background: linear-gradient(135deg, #3bc0c3 0%, #42d3d7 100%);\r\n    padding: 40px;\r\n    border-radius: 8px 8px 0 0;\r\n    color: #fff;\r\n  }\r\n\r\n  .header-content {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 40px;\r\n  }\r\n\r\n  .avatar-section {\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .avatar {\r\n    width: 150px;\r\n    height: 150px;\r\n    border-radius: 75px;\r\n    border: 4px solid rgba(255, 255, 255, 0.3);\r\n  }\r\n\r\n  .basic-info {\r\n    flex-grow: 1;\r\n  }\r\n\r\n  .name {\r\n    font-size: 28px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n    gap: 15px;\r\n  }\r\n\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .info-item i {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .resume-body {\r\n    padding: 40px;\r\n  }\r\n\r\n  .resume-section {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 20px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .section-title i {\r\n    color: #3bc0c3;\r\n  }\r\n\r\n  .info-row {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .info-row label {\r\n    font-weight: bold;\r\n    color: #666;\r\n    display: block;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .content {\r\n    line-height: 1.6;\r\n    color: #333;\r\n  }\r\n\r\n  .status-employed {\r\n    color: #67c23a;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .status-unemployed {\r\n    color: #f56c6c;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .no-resume {\r\n    text-align: center;\r\n    padding: 40px 0;\r\n  }\r\n</style>"]}]}