{"remainingRequest": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue?vue&type=template&id=91b6ff58&scoped=true", "dependencies": [{"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue", "mtime": 1741616960745}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\babel.config.js", "mtime": 1741600428000}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749091668180}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749091668891}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749091667365}, {"path": "I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749091668387}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_imports_0", "class", "href", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "$data", "islogin", "_Fragment", "key", "_hoisted_6", "_toDisplayString", "lname", "onClick", "_cache", "args", "$options", "exit", "_hoisted_7", "_hoisted_8", "_hoisted_9", "src", "alt", "toggleMobileMenu", "_normalizeClass", "mobileMenuActive", "_hoisted_10", "activePath", "showSearchDialog", "_createVNode", "_component_el_dialog", "searchDialogVisible", "$event", "title", "width", "footer", "_withCtx", "_hoisted_11", "_component_el_button", "type", "handleSearch", "_component_el_form", "_component_el_form_item", "_component_el_input", "searchKeyword", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>"], "sources": ["I:\\product3\\00203JobHuntingSystem\\JobHuntingSystem-web\\src\\components\\TopMenu.vue"], "sourcesContent": ["<template>\r\n  <div class=\"top-menu\">\r\n    <!-- 顶部信息栏 -->\r\n    <div class=\"top-bar\">\r\n      <div class=\"container\">\r\n        <div class=\"top-bar-content\">\r\n          <div class=\"contact-info\">\r\n            <a href=\"mailto:<EMAIL>\" class=\"contact-item\">\r\n              <i class=\"fas fa-envelope\"></i>\r\n              <span><EMAIL></span>\r\n            </a>\r\n            <a href=\"tel:01066666666\" class=\"contact-item\">\r\n              <i class=\"fas fa-phone\"></i>\r\n              <span>010 6666 6666</span>\r\n            </a>\r\n          </div>\r\n          <div class=\"user-info\">\r\n            <template v-if=\"islogin\">\r\n              <a href=\"/Sreg\" class=\"user-link\">求职者注册</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"/Slogin\" class=\"user-link\">求职者登录</a>\r\n            </template>\r\n            <template v-else>\r\n              <span class=\"welcome-text\">欢迎您：</span>\r\n              <a href=\"/sweclome\" class=\"user-name\">{{ lname }}</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"javascript:void(0);\" @click=\"exit\" class=\"logout-link\">退出登录</a>\r\n            </template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主导航栏 -->\r\n    <div class=\"main-nav\">\r\n      <div class=\"container\">\r\n        <div class=\"nav-content\">\r\n          <!-- Logo -->\r\n          <a href=\"/index\" class=\"logo\">\r\n            <img src=\"../assets/images/main-logo.png\" alt=\"求职系统\" />\r\n          </a>\r\n\r\n          <!-- 移动端菜单按钮 -->\r\n          <div class=\"mobile-toggle\" @click=\"toggleMobileMenu\">\r\n            <span></span>\r\n            <span></span>\r\n            <span></span>\r\n          </div>\r\n\r\n          <!-- 导航菜单 -->\r\n          <div class=\"nav-menu\" :class=\"{ 'active': mobileMenuActive }\">\r\n            <ul class=\"nav-list\">\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/index' }\">\r\n                <a href=\"/index\" class=\"nav-link\">网站首页</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/positionsList' }\">\r\n                <a href=\"/positionsList\" class=\"nav-link\">招聘职位</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/companyList' }\">\r\n                <a href=\"/companyList\" class=\"nav-link\">企业展示</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/bbs' }\">\r\n                <a href=\"/bbs\" class=\"nav-link\">交流论坛</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/ai' }\">\r\n                <a href=\"/ai\" class=\"nav-link\">AI顾问</a>\r\n              </li>\r\n              <li class=\"nav-item\" :class=\"{ 'active': activePath === '/sweclome' }\">\r\n                <a href=\"/sweclome\" class=\"nav-link\">个人中心</a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- 搜索按钮 -->\r\n          <div class=\"search-btn\" @click=\"showSearchDialog\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索对话框 -->\r\n    <el-dialog v-model=\"searchDialogVisible\" title=\"职位搜索\" width=\"30%\" class=\"search-dialog\">\r\n      <el-form>\r\n        <el-form-item>\r\n          <el-input v-model=\"searchKeyword\" placeholder=\"请输入职位关键词\" prefix-icon=\"el-icon-search\"\r\n            @keyup.enter=\"handleSearch\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"searchDialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">搜 索</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from '../../utils/http';\r\n  import '../assets/css/font-awesome.min.css';\r\n  import '../assets/css/qbootstrap.min.css';\r\n  import '../assets/css/qanimate.css';\r\n  import '../assets/css/owl.carousel.min.css';\r\n  import '../assets/css/owl.theme.default.min.css';\r\n  import '../assets/css/qmain.css';\r\n  import { ElMessage } from 'element-plus';\r\n\r\n  export default {\r\n    name: 'TopMenu',\r\n    data() {\r\n      return {\r\n        islogin: true,\r\n        lname: '',\r\n        ishow: false,\r\n        key: '',\r\n        searchDialogVisible: false,\r\n        searchKeyword: '',\r\n        mobileMenuActive: false,\r\n        activePath: ''\r\n      };\r\n    },\r\n    mounted() {\r\n      this.lname = sessionStorage.getItem('lname');\r\n      if (this.lname) {\r\n        this.islogin = false;\r\n      }\r\n      // 获取当前路径\r\n      this.activePath = window.location.pathname;\r\n    },\r\n    methods: {\r\n      exit: function () {\r\n        var _this = this;\r\n        this.$confirm('确认退出吗?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        })\r\n          .then(() => {\r\n            sessionStorage.removeItem('lname');\r\n            location.href = '/index';\r\n          })\r\n          .catch(() => { });\r\n      },\r\n      showSearchDialog() {\r\n        this.searchDialogVisible = true;\r\n      },\r\n      handleSearch() {\r\n        if (!this.searchKeyword) {\r\n          ElMessage.warning('请输入搜索关键词');\r\n          return;\r\n        }\r\n        location.href = '/positionsList?keyword=' + this.searchKeyword;\r\n      },\r\n      toggleMobileMenu() {\r\n        this.mobileMenuActive = !this.mobileMenuActive;\r\n      }\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 整体容器 */\r\n  .top-menu {\r\n    font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\r\n  }\r\n\r\n  /* 顶部信息栏 */\r\n  .top-bar {\r\n    background-color: #f8f9fa;\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .top-bar-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .contact-info {\r\n    display: flex;\r\n  }\r\n\r\n  .contact-item {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #666;\r\n    margin-right: 20px;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .contact-item:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .contact-item i {\r\n    margin-right: 8px;\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-info {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .user-link,\r\n  .user-name,\r\n  .logout-link {\r\n    color: #666;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .user-link:hover,\r\n  .user-name:hover,\r\n  .logout-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .user-name {\r\n    color: #3498db;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .welcome-text {\r\n    color: #666;\r\n  }\r\n\r\n  .divider {\r\n    margin: 0 10px;\r\n    color: #ccc;\r\n  }\r\n\r\n  /* 主导航栏 */\r\n  .main-nav {\r\n    background-color: #fff;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n    position: relative;\r\n    z-index: 100;\r\n  }\r\n\r\n  .nav-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 0;\r\n    position: relative;\r\n  }\r\n\r\n  .logo {\r\n    display: block;\r\n    margin-right: 30px;\r\n  }\r\n\r\n  .logo img {\r\n    height: 50px;\r\n    display: block;\r\n  }\r\n\r\n  .mobile-toggle {\r\n    display: none;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    width: 30px;\r\n    height: 22px;\r\n    cursor: pointer;\r\n    z-index: 101;\r\n  }\r\n\r\n  .mobile-toggle span {\r\n    display: block;\r\n    height: 3px;\r\n    width: 100%;\r\n    background-color: #333;\r\n    border-radius: 3px;\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  .nav-menu {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .nav-list {\r\n    display: flex;\r\n    list-style: none;\r\n    margin: 0;\r\n    padding: 0;\r\n  }\r\n\r\n  .nav-item {\r\n    position: relative;\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .nav-link {\r\n    display: block;\r\n    padding: 10px 15px;\r\n    color: #333;\r\n    font-weight: 500;\r\n    text-decoration: none;\r\n    transition: color 0.3s;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .nav-link:hover {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active .nav-link {\r\n    color: #3498db;\r\n  }\r\n\r\n  .nav-item.active::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -15px;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 3px;\r\n    background-color: #3498db;\r\n  }\r\n\r\n  .search-btn {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f8f9fa;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.3s;\r\n    margin-left: 15px;\r\n  }\r\n\r\n  .search-btn:hover {\r\n    background-color: #e9ecef;\r\n  }\r\n\r\n  .search-btn i {\r\n    color: #666;\r\n    font-size: 18px;\r\n  }\r\n\r\n  /* 搜索对话框 */\r\n  .search-dialog :deep(.el-dialog__header) {\r\n    border-bottom: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__body) {\r\n    padding: 20px;\r\n  }\r\n\r\n  .search-dialog :deep(.el-dialog__footer) {\r\n    border-top: 1px solid #eaeaea;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n  @media (max-width: 992px) {\r\n    .mobile-toggle {\r\n      display: flex;\r\n    }\r\n\r\n    .nav-menu {\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100vh;\r\n      background-color: rgba(255, 255, 255, 0.95);\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      align-items: center;\r\n      transform: translateX(-100%);\r\n      transition: transform 0.3s ease;\r\n      z-index: 100;\r\n    }\r\n\r\n    .nav-menu.active {\r\n      transform: translateX(0);\r\n    }\r\n\r\n    .nav-list {\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n\r\n    .nav-item {\r\n      margin: 10px 0;\r\n    }\r\n\r\n    .nav-link {\r\n      font-size: 18px;\r\n      padding: 10px 20px;\r\n    }\r\n\r\n    .nav-item.active::after {\r\n      display: none;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .top-bar-content {\r\n      flex-direction: column;\r\n    }\r\n\r\n    .contact-info {\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .logo img {\r\n      height: 40px;\r\n    }\r\n  }\r\n</style>"], "mappings": ";OAuCiBA,UAAoC;;EAtC9CC,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAiB;;EAWrBA,KAAK,EAAC;AAAW;;EAQfC,IAAI,EAAC,WAAW;EAACD,KAAK,EAAC;;;EAU/BA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAa;;EAehBA,KAAK,EAAC;AAAU;;EAuClBA,KAAK,EAAC;AAAe;;;;;;;uBAzFjCE,mBAAA,CA+FM,OA/FNC,UA+FM,GA9FJC,mBAAA,WAAc,EACdC,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJD,mBAAA,CA0BM,OA1BNE,UA0BM,GAzBJF,mBAAA,CAwBM,OAxBNG,UAwBM,G,gbAbJH,mBAAA,CAYM,OAZNI,UAYM,GAXYC,KAAA,CAAAC,OAAO,I,cAAvBT,mBAAA,CAIWU,SAAA;IAAAC,GAAA;EAAA,I,0BAHTR,mBAAA,CAA2C;IAAxCJ,IAAI,EAAC,OAAO;IAACD,KAAK,EAAC;KAAY,OAAK,sB,0BACvCK,mBAAA,CAA8B;IAAxBL,KAAK,EAAC;EAAS,GAAC,GAAC,sB,0BACvBK,mBAAA,CAA6C;IAA1CJ,IAAI,EAAC,SAAS;IAACD,KAAK,EAAC;KAAY,OAAK,qB,8CAE3CE,mBAAA,CAKWU,SAAA;IAAAC,GAAA;EAAA,I,0BAJTR,mBAAA,CAAsC;IAAhCL,KAAK,EAAC;EAAc,GAAC,MAAI,sBAC/BK,mBAAA,CAAqD,KAArDS,UAAqD,EAAAC,gBAAA,CAAZL,KAAA,CAAAM,KAAK,kB,4BAC9CX,mBAAA,CAA8B;IAAxBL,KAAK,EAAC;EAAS,GAAC,GAAC,sBACvBK,mBAAA,CAAwE;IAArEJ,IAAI,EAAC,qBAAqB;IAAEgB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAC,IAAA,IAAAF,IAAA,CAAI;IAAEnB,KAAK,EAAC;KAAc,MAAI,E,sCAO9EI,mBAAA,UAAa,EACbC,mBAAA,CA6CM,OA7CNiB,UA6CM,GA5CJjB,mBAAA,CA2CM,OA3CNkB,UA2CM,GA1CJlB,mBAAA,CAyCM,OAzCNmB,UAyCM,GAxCJpB,mBAAA,UAAa,E,4BACbC,mBAAA,CAEI;IAFDJ,IAAI,EAAC,QAAQ;IAACD,KAAK,EAAC;MACrBK,mBAAA,CAAuD;IAAlDoB,GAAoC,EAApC1B,UAAoC;IAAC2B,GAAG,EAAC;2BAGhDtB,mBAAA,aAAgB,EAChBC,mBAAA,CAIM;IAJDL,KAAK,EAAC,eAAe;IAAEiB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAO,gBAAA,IAAAP,QAAA,CAAAO,gBAAA,IAAAR,IAAA,CAAgB;kCACjDd,mBAAA,CAAa,uCACbA,mBAAA,CAAa,uCACbA,mBAAA,CAAa,sC,IAGfD,mBAAA,UAAa,EACbC,mBAAA,CAqBM;IArBDL,KAAK,EAAA4B,eAAA,EAAC,UAAU;MAAA,UAAqBlB,KAAA,CAAAmB;IAAgB;MACxDxB,mBAAA,CAmBK,MAnBLyB,WAmBK,GAlBHzB,mBAAA,CAEK;IAFDL,KAAK,EAAA4B,eAAA,EAAC,UAAU;MAAA,UAAqBlB,KAAA,CAAAqB,UAAU;IAAA;kCACjD1B,mBAAA,CAA0C;IAAvCJ,IAAI,EAAC,QAAQ;IAACD,KAAK,EAAC;KAAW,MAAI,oB,mBAExCK,mBAAA,CAEK;IAFDL,KAAK,EAAA4B,eAAA,EAAC,UAAU;MAAA,UAAqBlB,KAAA,CAAAqB,UAAU;IAAA;kCACjD1B,mBAAA,CAAkD;IAA/CJ,IAAI,EAAC,gBAAgB;IAACD,KAAK,EAAC;KAAW,MAAI,oB,mBAEhDK,mBAAA,CAEK;IAFDL,KAAK,EAAA4B,eAAA,EAAC,UAAU;MAAA,UAAqBlB,KAAA,CAAAqB,UAAU;IAAA;kCACjD1B,mBAAA,CAAgD;IAA7CJ,IAAI,EAAC,cAAc;IAACD,KAAK,EAAC;KAAW,MAAI,oB,mBAE9CK,mBAAA,CAEK;IAFDL,KAAK,EAAA4B,eAAA,EAAC,UAAU;MAAA,UAAqBlB,KAAA,CAAAqB,UAAU;IAAA;kCACjD1B,mBAAA,CAAwC;IAArCJ,IAAI,EAAC,MAAM;IAACD,KAAK,EAAC;KAAW,MAAI,oB,mBAEtCK,mBAAA,CAEK;IAFDL,KAAK,EAAA4B,eAAA,EAAC,UAAU;MAAA,UAAqBlB,KAAA,CAAAqB,UAAU;IAAA;kCACjD1B,mBAAA,CAAuC;IAApCJ,IAAI,EAAC,KAAK;IAACD,KAAK,EAAC;KAAW,MAAI,oB,mBAErCK,mBAAA,CAEK;IAFDL,KAAK,EAAA4B,eAAA,EAAC,UAAU;MAAA,UAAqBlB,KAAA,CAAAqB,UAAU;IAAA;kCACjD1B,mBAAA,CAA6C;IAA1CJ,IAAI,EAAC,WAAW;IAACD,KAAK,EAAC;KAAW,MAAI,oB,sCAK/CI,mBAAA,UAAa,EACbC,mBAAA,CAEM;IAFDL,KAAK,EAAC,YAAY;IAAEiB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAY,gBAAA,IAAAZ,QAAA,CAAAY,gBAAA,IAAAb,IAAA,CAAgB;kCAC9Cd,mBAAA,CAA6B;IAA1BL,KAAK,EAAC;EAAe,2B,UAMhCI,mBAAA,WAAc,EACd6B,YAAA,CAaYC,oBAAA;gBAbQxB,KAAA,CAAAyB,mBAAmB;+DAAnBzB,KAAA,CAAAyB,mBAAmB,GAAAC,MAAA;IAAEC,KAAK,EAAC,MAAM;IAACC,KAAK,EAAC,KAAK;IAACtC,KAAK,EAAC;;IAO3DuC,MAAM,EAAAC,QAAA,CACf,MAGO,CAHPnC,mBAAA,CAGO,QAHPoC,WAGO,GAFLR,YAAA,CAA+DS,oBAAA;MAAnDzB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAE1B,KAAA,CAAAyB,mBAAmB;;wBAAU,MAAGjB,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;QACnDe,YAAA,CAA+DS,oBAAA;MAApDC,IAAI,EAAC,SAAS;MAAE1B,OAAK,EAAEG,QAAA,CAAAwB;;wBAAc,MAAG1B,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;sBATvD,MAKU,CALVe,YAAA,CAKUY,kBAAA;wBAJR,MAGe,CAHfZ,YAAA,CAGea,uBAAA;0BAFb,MACyC,CADzCb,YAAA,CACyCc,mBAAA;sBADtBrC,KAAA,CAAAsC,aAAa;qEAAbtC,KAAA,CAAAsC,aAAa,GAAAZ,MAAA;UAAEa,WAAW,EAAC,UAAU;UAAC,aAAW,EAAC,gBAAgB;UAClFC,OAAK,EAAAC,SAAA,CAAQ/B,QAAA,CAAAwB,YAAY", "ignoreList": []}]}